#!/bin/bash
set -e

echo "Setting up JHipster application environment..."

# Update package lists
sudo apt-get update -qq

# Install Java 21 (OpenJDK)
echo "Installing Java 21..."
sudo apt-get install -y openjdk-21-jdk

# Install curl and other dependencies
sudo apt-get install -y curl wget gnupg2 software-properties-common

# Install Node.js v22.15.0 using NodeSource repository
echo "Installing Node.js v22.15.0..."
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# Update npm to version 11.3.0
echo "Updating npm to version 11.3.0..."
sudo npm install -g npm@11.3.0 --silent

# Install Angular CLI globally
echo "Installing Angular CLI..."
sudo npm install -g @angular/cli --silent

# Make Maven wrapper executable
echo "Making Maven wrapper executable..."
chmod +x ./mvnw

# Install npm dependencies
echo "Installing npm dependencies..."
npm install --silent

# Set up environment variables and PATH
echo "Setting up environment variables..."

# Add Java to PATH in ~/.profile
cat >> ~/.profile << 'EOF'

# Java 21
export JAVA_HOME=/usr/lib/jvm/java-21-openjdk-amd64
export PATH=$JAVA_HOME/bin:$PATH

EOF

# Source the profile to make changes available immediately
source ~/.profile

echo "Setup completed successfully!"