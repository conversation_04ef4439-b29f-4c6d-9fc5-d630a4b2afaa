- Seg<PERSON> la imagen:
- ![Dashboard](dashboard.svg)

# Zona izquierda. 25%

- Participants
  - En este recuadro saldra a su vez dividido en dos zonas una encima de otra
    - Arriba "Sender"
      - Saldrán uno 5 con un scroll ordenados alfabeticamente
        - ejemplo: "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "María
    - Abajo "Receiver"
      - Saldrán unos 5 con un scroll ordenados alfabéticamente
        - ejemplo: "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
- <PERSON><PERSON>
  - En este recuadro saldra a su vez dividido en dos zonas una encima de otra
    - Arriba "Sender"
      - Saldrán uno 5 con un scroll ordenados alfabeticamente
        - ejemplo: 645883333,<EMAIL>,555555 ( con el icono de tipo)
    - Abajo "Receiver"
      - <PERSON><PERSON><PERSON> unos 5 con un scroll ordenados alfabéticamente
        - ejemplo: <EMAIL>,5555555,jose@mailes

# Zona central 50%

- Filtro (cada fila es un fila en la vista tambien)
  - Texto, fecha inicio, fecha fin
  - tags, feeling
- Message
  - fila arriba: time-sender-recient-feeling-typesourceicon(whatsapp-email-audio)
  - fila abajo: content

# Zona derecha 25%

- Convestations
  - Lista de convesations
- Tags
  - Lista de tags
