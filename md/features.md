### Email

- Al subir un buzón deberá agregar los mail que se quieren extraer.
  - ejem: [<EMAIL>][mail2@mailes], se buscarán en las que los dos interactuen ya sea como sender o recipient

### Telefonos

- Con prefijo ( el usuario usa banderas, por defecto la de su pais)

### Timezone

- Revisar que todo lo que se suba vaya con timezone para que en la base de datos se guarden sin timezone todo para evitar problemas

### Subidas inteligentes

- Al subir un source
  - solo sean un recipent concreto / no ( no aplica en whatsapp ni audio)
  - Los mensajes que contenga o no "xxxx"
  - Los mensajes que estén o no este entre rango de fechas
  - solo contentan X palabras / no ( no aplica en audio)
  - en un rango de fechas ( no aplica en audio)

### Source imagen

- Al subir una imagen
  - tiene los campos de message para integrase con el resto.

### Source documento

- Al subir un documento
  - tiene los campos de message para integrase con el resto.

### Agregar tag

El usuario podra crear tags para poder filtrar los mensajes y las conversaciones.

- Agregas tag a mensaje
- Agregas tag a conversation
