# Agregar recurso de audio

## Ficheros

### Front

    - audio-message-source-button.html
    - audio-message-source-button.ts

### Back

    - API: AudioMessageSourceResource.java

    -

## 1. Subir archivo de audio

    - Al abri el modal saldrá la opción de agregar un audio en los formatos .ogg, .wav, .mp3, .webm
    - Al subir automáticamente se manda al backend para que se transcriba.
    - El end-point devolvera el texto transcribido.

## 2. Agregar datos

    - Arriba en la misma fila saldrá para agregar.
        - time: Dia y hora del audio
        - sender: Quien envio el audio
        - recipient: Quien recibio el audio
    - Saldra la opción de poder oir el audio
    - Justo debajo el texto transcribido.
    - Una vez agregado los tres datos superiores, obligatorio se envía todo al backend.
    - Se guardará todo

## 3. Guardado

    - El audioMessage se pasa a Message
    - Se crea un Source:
        - messagesIds : el id de message
        - type : SourceType.AUDIO
        - file : El archivo de audio
        - fileContentType : El tipo de archivo de audio
