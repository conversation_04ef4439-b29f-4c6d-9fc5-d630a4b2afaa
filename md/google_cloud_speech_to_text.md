# Configuración de Google Cloud Speech-to-Text

## Pasos para configurar Google Cloud Speech-to-Text

### 1. Crear un proyecto en Google Cloud Console

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la API de Speech-to-Text:
   - Ve a "APIs & Services" > "Library"
   - Busca "Cloud Speech-to-Text API"
   - Haz clic en "Enable"

### 2. Crear una cuenta de servicio

1. Ve a "IAM & Admin" > "Service Accounts"
2. Haz clic en "Create Service Account"
3. Proporciona un nombre y descripción
4. Asigna el rol "Cloud Speech Client" o "Editor"
5. Haz clic en "Create Key" y selecciona "JSON"
6. Descarga el archivo JSON

### 3. Configurar las credenciales en el proyecto

1. Coloca el archivo JSON descargado en la raíz del proyecto
2. Renómbralo a `google-cloud-credentials.json`
3. El archivo ya está incluido en `.gitignore` para evitar subirlo al repositorio

### 4. Variables de entorno (opcional)

Alternativamente, puedes usar la variable de entorno:

```bash
export GOOGLE_APPLICATION_CREDENTIALS=/ruta/completa/al/archivo/google-cloud-credentials.json
```

### 5. Configuración en application.yml

```java
@ConfigurationProperties(prefix = "application", ignoreUnknownFields = false)
public class ApplicationProperties {

  private final Google google = new Google();

  // jhipster-needle-application-properties-property

  public Google getGoogle() {
    return google;
  }
}

```

La configuración actual en `application.yml` es:

```yaml
application:
  google:
    speech:
      enabled: true
      language-code: es-ES
      sample-rate: 16000
      credentials-path: ${GOOGLE_APPLICATION_CREDENTIALS:google-cloud-credentials.json}
```

### 6. Formatos de audio soportados

- WAV (recomendado)
- FLAC
- MP3
- OPUS
- OGG
- WebM

### 7. Configuración de idioma

Puedes cambiar el idioma modificando `language-code` en `application.yml`:

- `es-ES` - Español (España)
- `es-MX` - Español (México)
- `en-US` - Inglés (Estados Unidos)
- `en-GB` - Inglés (Reino Unido)

### 8. Fallback

Si Google Cloud Speech no está disponible o configurado, el sistema usará transcripciones de prueba automáticamente.

## Estructura de archivos

```
proyecto/
├── google-cloud-credentials.json  (tu archivo de credenciales)
├── .gitignore                     (incluye *.json para credenciales)
└── src/main/resources/config/
    └── application.yml            (configuración)
```

## Notas de seguridad

- **NUNCA** subas el archivo de credenciales al repositorio
- El archivo está incluido en `.gitignore`
- Usa variables de entorno en producción
- Rota las claves periódicamente
