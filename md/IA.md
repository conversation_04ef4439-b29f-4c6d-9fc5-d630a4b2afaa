# Código.

- Estilos
- Siempre tiene que estar todo en inglés.
- Hay que respetar el translate, osea que los html usarlo.
  Los ficheros de translate se encuentran en `src/main/webapp/i18n/\*", que tenelos actualizados y crear los ficheros y claves por defecto.
  El inglés será el original, y el español el traducido, en el codigo y translate.

  - Ejemplos

  ```html
  placeholder="{{ 'global.form.username.placeholder' | translate }}"

  <label for="password" jhiTranslate="login.form.password">Password</label>
  ```

  \*\* SOLO HABLAR ESPAÑOL EN LOS JSON DE TRANDUCCIÓN. PARA HABLAR CONMIGO TAMBIÉN HABLAS EN ESPAÑOL.

- Siempre se hará todo con componentes, que sea reutilizables y con la mejores practicas de programación.

05-07-2025

# Modal edición de conversación

- Crear json convesation.json para inglés y español, y poner todo con translate.
- Estilos

  - Los textos son muy confusos, los titulos, label, etc con todos iguales, cuesta ubicarse.
  - Los botones no tienen el mismo estilo que el resto de la aplicación.
  - Poner un fondo rojo como hay en otras zonas de la aplicación.
  - Evitar tantos separadores, scrolls, que sea lo más limpio.

- Titulo "Crear conversación" o "Editar conversación" según corresponda.
  - A la derecha estará al mismo nivel "Crear/Actualizar" y "Cerrar", quitar la X
- Arriba en la primera fila y en el orden del listado
  - Nombre de la conversación: "Nombre de la conversación" (50%)
  - Fecha de inicio: "Fecha de inicio" (25% )
  - Fecha de fin: "Fecha de fin" (25%)
- Abajo ocupando el 100% el filtro y mensajes para agregar a la conversación. ( poder desplegar y plegar, por defecto cerrado)
  - Filtro (100%)
  - Mensajes (100%)
- Abajo ocupando el 100 los mensajes de la conversación. ( poder desplegar y plegar, por defecto abierto)
  - Mensajes (100%)
- Quitar sentimientos y etiquetas de momento.
