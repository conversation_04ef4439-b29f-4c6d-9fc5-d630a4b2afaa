Módulo: Data
Feature: Data Source Management
    As an authenticated user
    I want to be able to upload, list, and manage my conversation data source files
    So that I can organize and prepare my data for searching.

    Background: User is Authenticated for Data Operations
        Given I am an authenticated user "<EMAIL>" with a valid JWT
        And the application is running
        And user "<EMAIL>" has no existing data sources

    Scenario: Successfully initiate an Excel file upload and get a preview (Single-Step Upload V1)
        Given I select a valid Excel file "conversations_january.xlsx" with 15 rows of valid message data
        When I upload the file "conversations_january.xlsx"
        Then the upload and processing should be successful
        And a new data source named "conversations_january.xlsx" should be created for me
        And 15 messages from that source should be saved in the system
        And I should receive details of the created data source

    Scenario: Attempt to upload an unsupported file type (e.g., .txt)
        Given I select an unsupported file "notes.txt"
        When I attempt to upload the file "notes.txt"
        Then the upload should fail
        And I should see an error message indicating "Invalid file type. Only .xlsx or .xls are supported."

    Scenario: Attempt to upload an empty Excel file
        Given I select an empty Excel file "empty_conversations.xlsx"
        When I attempt to upload the file "empty_conversations.xlsx"
        Then the upload should fail or result in a data source with zero messages
        And I should see an error message indicating "File appears to be empty or unparseable." or "No valid message data found"

    Scenario: Attempt to upload an Excel file with parsing errors in all rows
        Given I select an Excel file "all_bad_data.xlsx" where all rows have formatting errors
        When I attempt to upload the file "all_bad_data.xlsx"
        Then the upload should fail
        And I should see an error message indicating "No valid message data found in the uploaded file."
        And no data source should be created

    Scenario: List my data sources when I have none
        Given I have no data sources uploaded
        When I request to list my data sources
        Then I should receive an empty list of data sources

    Scenario: List my data sources when I have uploaded some
        Given I have successfully uploaded a data source "source1.xlsx"
        And I have successfully uploaded another data source "source2.xlsx"
        When I request to list my data sources
        Then I should receive a list containing 2 data sources
        And the list should include "source1.xlsx" and "source2.xlsx"

    Scenario: Successfully delete an existing data source I own
        Given I have successfully uploaded a data source "source_to_delete.xlsx" with ID "<source_id_to_delete>"
        And messages associated with "<source_id_to_delete>" exist
        When I request to delete the data source with ID "<source_id_to_delete>"
        Then the deletion should be successful
        And the data source with ID "<source_id_to_delete>" should no longer exist
        And all messages associated with "<source_id_to_delete>" should be deleted

    Scenario: Attempt to delete a data source that does not exist
        Given I have no data sources
        When I request to delete a data source with ID "non_existent_source_id"
        Then the deletion should fail
        And I should see an error message indicating "Data source not found"

Feature: Message Search and Management
    As an authenticated user
    I want to be able to search, view, and edit my uploaded messages
    So that I can find specific information and correct my data.

    Background: User Authenticated and Data Source Exists for Message Operations
        Given I am an authenticated user "<EMAIL>" with a valid JWT
        And the application is running
        And I have a confirmed data source named "project_alpha_chat.xlsx" with ID "<source_id_alpha>"
        And data source "<source_id_alpha>" contains the following messages:
            | messageId  | timestamp            | sender  | recipients | content                                           |
            | <msg_id_1> | 2024-03-10T10:00:00Z | Alice   | Bob        | "Discussion about the Q1 budget proposal"         |
            | <msg_id_2> | 2024-03-10T10:05:00Z | Bob     | Alice      | "I agree, the budget needs adjustment"            |
            | <msg_id_3> | 2024-03-11T14:30:00Z | Alice   | Charlie    | "Meeting scheduled for tomorrow to review budget" |
            | <msg_id_4> | 2024-03-12T09:00:00Z | Charlie | Alice      | "Can we postpone the budget meeting?"             |
            | <msg_id_5> | 2024-03-15T11:00:00Z | David   | Alice      | "Final budget approved and signed"                |

    Scenario: Search messages by a single keyword
        Given I want to search within my data source "<source_id_alpha>"
        When I search for the keyword "budget"
        Then I should see a list of messages containing "budget"
        And the message with ID "<msg_id_1>" should be in the results
        And the message with ID "<msg_id_2>" should be in the results
        And the message with ID "<msg_id_3>" should be in the results
        And the message with ID "<msg_id_5>" should be in the results

    Scenario: Search messages by multiple keywords (AND logic)
        Given I want to search within my data source "<source_id_alpha>"
        When I search for the keywords "budget" AND "proposal"
        Then I should see a list of messages containing both "budget" AND "proposal"
        And the message with ID "<msg_id_1>" should be in the results

    Scenario: Search messages by keyword within a specific date range
        Given I want to search within my data source "<source_id_alpha>"
        When I search for the keyword "meeting" from "2024-03-11T00:00:00Z" to "2024-03-12T23:59:59Z"
        Then I should see a list of messages containing "meeting" within that date range
        And the message with ID "<msg_id_3>" should be in the results
        And the message with ID "<msg_id_4>" should be in the results

    Scenario: Search messages returns empty list if no keyword match
        Given I want to search within my data source "<source_id_alpha>"
        When I search for the keyword "nonexistent_keyword_xyz"
        Then I should see an empty list of messages

    Scenario: View all messages for a specific data source (no search criteria)
        Given I want to view all messages for data source "<source_id_alpha>"
        When I perform a search with no keywords and no date range for that source
        Then I should see all 5 messages from data source "<source_id_alpha>"

    Scenario: Successfully edit the content of an existing message I own
        Given I want to edit the message with ID "<msg_id_2>"
        And the current content of message "<msg_id_2>" is "I agree, the budget needs adjustment"
        When I update the content of message "<msg_id_2>" to "I agree, the Q1 budget needs significant adjustment and review"
        Then the update should be successful
        And if I retrieve message "<msg_id_2>", its content should be "I agree, the Q1 budget needs significant adjustment and review"

    Scenario: Attempt to edit a message that does not exist
        Given I want to edit a message
        When I attempt to update the content of a non-existent message with ID "non_existent_msg_id" to "any content"
        Then the update should fail
        And I should see an error message indicating "Message not found"