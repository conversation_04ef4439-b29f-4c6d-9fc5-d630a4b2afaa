Módulo: Access
Feature: User Registration
  As a new visitor to the platform
  I want to be able to register an account
  So that I can access the platform's features.

  Background: Existing System State
    Given the application is running
    And the following users already exist in the system:
      | username     | password     | role          | status |
      | existinguser | Password123! | USER          | ACTIVE |
      | adminuser    | AdminPass!   | ADMINISTRATOR | ACTIVE |

  Scenario: Successful new user registration
    Given I am a new visitor
    When I attempt to register with a new username "<EMAIL>" and a valid password "ValidPass123!"
    Then the registration should be successful
    And a new user with username "<EMAIL>" and role "USER" should exist in the system
    And the new user status should be "ACTIVE"

  Scenario: Attempt to register with an already existing username
    Given I am a new visitor
    When I attempt to register with an existing username "existinguser" and any password "anyPassword"
    Then the registration should fail
    And I should see an error message indicating "Username already exists"
    And no new user should be created with username "existinguser"

  Scenario: Attempt to register with an invalid password (e.g., too short)
    Given I am a new visitor
    And the system requires passwords to be at least 8 characters long
    When I attempt to register with username "<EMAIL>" and an invalid password "short"
    Then the registration should fail
    And I should see an error message indicating "Password must be at least 8 characters"
    And no new user should be created with username "<EMAIL>"

  Scenario: Attempt to register with an invalid password (e.g., missing complexity requirements)
    Given I am a new visitor
    And the system requires passwords to contain uppercase, lowercase, numbers, and special characters
    When I attempt to register with username "<EMAIL>" and an invalid password "password"
    Then the registration should fail
    And I should see an error message indicating "Password does not meet complexity requirements"
    And no new user should be created with username "<EMAIL>"

  Scenario: Attempt to register with an invalid username format
    Given I am a new visitor
    And the system requires usernames to be valid (e.g., specific pattern like "^[a-zA-Z0-9_]{3,20}$")
    When I attempt to register with an invalid username "invalid username with spaces" and a valid password "ValidPass123!"
    Then the registration should fail
    And I should see an error message indicating "Invalid username format"
    And no new user should be created with username "invalid username with spaces"

Feature: User Authentication
  As a registered user of the platform
  I want to be able to log in with my username and password
  So that I can access my account and data.

  Background: Existing System State for Authentication
    Given the application is running
    And the following users already exist in the system:
      | username     | password     | role          | status  | googleId  |
      | activeuser   | Password123! | USER          | ACTIVE  | null      |
      | blockeduser  | Password123! | USER          | BLOCKED | null      |
      | adminuser    | AdminPass!   | ADMINISTRATOR | ACTIVE  | null      |
      | googlelinked | null         | USER          | ACTIVE  | google123 |

  Scenario: Successful login with valid username and password
    Given I am a visitor
    When I attempt to log in with username "activeuser" and password "Password123!"
    Then the login should be successful
    And I should receive a valid authentication token
    And the token should contain the username "activeuser"
    And the token should contain the role "ROLE_USER"

  Scenario: Failed login with incorrect password
    Given I am a visitor
    When I attempt to log in with username "activeuser" and password "WrongPassword!"
    Then the login should fail
    And I should see an error message indicating "Invalid credentials"
    And I should not receive an authentication token

  Scenario: Failed login with non-existent username
    Given I am a visitor
    When I attempt to log in with username "nonexistentuser" and password "anyPassword"
    Then the login should fail
    And I should see an error message indicating "User not found"
    And I should not receive an authentication token

  Scenario: Failed login attempt for a blocked user account
    Given I am a visitor
    When I attempt to log in with username "blockeduser" and password "Password123!"
    Then the login should fail
    And I should see an error message indicating "User account is blocked"
    And I should not receive an authentication token

  Scenario: Attempt to login with an empty username
    Given I am a visitor
    When I attempt to log in with an empty username "" and password "Password123!"
    Then the login should fail
    And I should see an error message indicating "Username cannot be blank"
    And I should not receive an authentication token

  Scenario: Attempt to login with an empty password
    Given I am a visitor
    When I attempt to log in with username "activeuser" and an empty password ""
    Then the login should fail
    And I should see an error message indicating "Password cannot be blank"
    And I should not receive an authentication token

  Scenario: Successful login via Google for an existing Google-linked user
    Given a user "googlelinked" exists, linked with Google ID "google123"
    When the platform processes a successful Google authentication callback for Google ID "google123" associated with username "googlelinked"
    Then the login should be successful for user "googlelinked"
    And I should receive a valid authentication token for user "googlelinked"

  Scenario: Successful login via Google creates a new user if username does not exist
    Given I am a visitor initiating Google login
    And no user exists with username "newgoogleuser"
    When the platform processes a successful Google authentication callback for Google ID "google789" associated with username "newgoogleuser"
    Then a new user account with username "newgoogleuser", role "USER", status "ACTIVE", and Google ID "google789" should exist
    And the login should be successful for this new user
    And I should receive a valid authentication token for the user "newgoogleuser"