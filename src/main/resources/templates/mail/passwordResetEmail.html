<!doctype html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}" lang="en">
  <head>
    <title th:text="#{email.reset.title}">JHipster password reset</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <link rel="icon" th:href="@{|${baseUrl}/favicon.ico|}" />
  </head>
  <body>
    <p th:text="#{email.reset.greeting(${user.login})}">Dear</p>
    <p th:text="#{email.reset.text1}">
      For your JHipster account a password reset was requested, please click on the URL below to reset it:
    </p>
    <p>
      <a th:with="url=(@{|${baseUrl}/account/reset/finish?key=${user.resetKey}|})" th:href="${url}" th:text="${url}">Login link</a>
    </p>
    <p>
      <span th:text="#{email.reset.text2}">Regards, </span>
      <br />
      <em th:text="#{email.signature}">JHipster.</em>
    </p>
  </body>
</html>
