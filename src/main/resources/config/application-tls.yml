# ===================================================================
# Activate this profile to enable TLS and HTTP/2.
#
# <PERSON><PERSON><PERSON><PERSON> has generated a self-signed certificate, which will be used to encrypt traffic.
# As your browser will not understand this certificate, you will need to import it.
#
# Another (easiest) solution with Chrome is to enable the "allow-insecure-localhost" flag
# at chrome://flags/#allow-insecure-localhost
# ===================================================================
server:
  ssl:
    key-store: classpath:config/tls/keystore.p12
    key-store-password: password
    key-store-type: PKCS12
    key-alias: selfsigned
    ciphers:
      - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA
      - TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA
      - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA
      - TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA
    enabled-protocols: TLSv1.2
  http2:
    enabled: true
