package com.wishforthecure.forconversations.security;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * Utilidad para generar hashes BCrypt de contraseñas.
 * Para usar: ejecutar la clase y obtener el hash en la consola.
 */
public class PasswordHashGenerator {

    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "W3sf4rth3c4r2#";
        String hashedPassword = encoder.encode(password);
        System.out.println("Hash BCrypt para la contraseña '" + password + "': " + hashedPassword);

        // Verificar si un hash existente coincide con la contraseña
        String existingHash = "$2a$10$XJgGl.Bk9WYQcFvtfkTVIOcOaJ8JqCjJSjABNvmLmA4vJQa.Zzm6q";
        boolean matches = encoder.matches(password, existingHash);
        System.out.println("¿El hash existente coincide con la contraseña? " + matches);
    }
}
