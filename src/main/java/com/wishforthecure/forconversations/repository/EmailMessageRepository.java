package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.EmailMessage;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Spring Data MongoDB reactive repository for the EmailMessage entity.
 */
@SuppressWarnings("unused")
@Repository
public interface EmailMessageRepository extends ReactiveMongoRepository<EmailMessage, String> {}
