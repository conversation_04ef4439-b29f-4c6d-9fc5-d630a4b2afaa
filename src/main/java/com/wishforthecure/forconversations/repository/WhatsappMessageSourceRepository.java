package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.WhatsappMessageSource;
import java.util.UUID;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * Spring Data MongoDB reactive repository for the WhatsappMessageSource entity.
 */
@SuppressWarnings("unused")
@Repository
public interface WhatsappMessageSourceRepository extends ReactiveMongoRepository<WhatsappMessageSource, String> {
    Mono<WhatsappMessageSource> findByUploadId(UUID uploadId);
}
