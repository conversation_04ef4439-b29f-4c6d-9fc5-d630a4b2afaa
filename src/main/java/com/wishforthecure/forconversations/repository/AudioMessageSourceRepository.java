package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.AudioMessageSource;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Spring Data MongoDB reactive repository for the AudioMessageSource entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AudioMessageSourceRepository extends ReactiveMongoRepository<AudioMessageSource, String> {}
