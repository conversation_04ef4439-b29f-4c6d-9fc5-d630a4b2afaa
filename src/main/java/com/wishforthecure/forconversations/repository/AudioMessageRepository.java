package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.AudioMessage;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Spring Data MongoDB reactive repository for the AudioMessage entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AudioMessageRepository extends ReactiveMongoRepository<AudioMessage, String> {}
