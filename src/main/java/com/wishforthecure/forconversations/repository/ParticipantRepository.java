package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.Participant;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

/**
 * Spring Data MongoDB reactive repository for the Participant entity.
 */
@SuppressWarnings("unused")
@Repository
public interface ParticipantRepository extends ReactiveMongoRepository<Participant, String> {
    Flux<Participant> findByUserId(String userId);

    Flux<Participant> findByUserIdAndNameContainingIgnoreCaseOrSurnameContainingIgnoreCase(String userId, String name, String surname);
}
