package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.Alias;
import java.util.Set;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

/**
 * Spring Data MongoDB reactive repository for the Alias entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AliasRepository extends ReactiveMongoRepository<Alias, String> {
    Flux<Alias> findByValueIn(Set<String> values);

    Flux<Alias> findByUserId(String userId);

    Flux<Alias> findByUserIdAndValueContainingIgnoreCase(String userId, String value);
}
