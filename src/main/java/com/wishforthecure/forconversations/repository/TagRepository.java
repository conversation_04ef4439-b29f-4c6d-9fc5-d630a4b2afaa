package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.Tag;
import java.util.Set;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

/**
 * Spring Data MongoDB reactive repository for the Tag entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TagRepository extends ReactiveMongoRepository<Tag, String> {
    Flux<Tag> findByValueIn(Set<String> values);

    Flux<Tag> findByUserId(String userId);

    Flux<Tag> findByUserIdAndValueContainingIgnoreCase(String userId, String value);
}
