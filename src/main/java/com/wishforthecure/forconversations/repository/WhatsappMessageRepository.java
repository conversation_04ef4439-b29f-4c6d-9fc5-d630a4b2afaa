package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.WhatsappMessage;
import java.util.UUID;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * Spring Data MongoDB reactive repository for the WhatsappMessage entity.
 */
@Repository
public interface WhatsappMessageRepository extends ReactiveMongoRepository<WhatsappMessage, String> {
    Mono<WhatsappMessage> findByUploadId(UUID uploadId);
}
