package com.wishforthecure.forconversations.repository;

import com.wishforthecure.forconversations.domain.EmailMessageSource;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Spring Data MongoDB reactive repository for the EmailMessageSource entity.
 */
@SuppressWarnings("unused")
@Repository
public interface EmailMessageSourceRepository extends ReactiveMongoRepository<EmailMessageSource, String> {}
