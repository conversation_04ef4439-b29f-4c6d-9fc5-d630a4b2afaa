package com.wishforthecure.forconversations.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "application")
public class ApplicationProperties {

    private final Google google = new Google();

    public Google getGoogle() {
        return google;
    }

    public static class Google {

        private final Speech speech = new Speech();

        public Speech getSpeech() {
            return speech;
        }
    }

    public static class Speech {

        private String projectId;
        private String location;
        private String recognizerId;
        private String credentialsPath;
        private boolean enabled;
        private String languageCode;
        private int sampleRate;

        // Getters y Setters para todos los campos

        public String getProjectId() {
            return projectId;
        }

        public void setProjectId(String projectId) {
            this.projectId = projectId;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getRecognizerId() {
            return recognizerId;
        }

        public void setRecognizerId(String recognizerId) {
            this.recognizerId = recognizerId;
        }

        public String getCredentialsPath() {
            return credentialsPath;
        }

        public void setCredentialsPath(String credentialsPath) {
            this.credentialsPath = credentialsPath;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getLanguageCode() {
            return languageCode;
        }

        public void setLanguageCode(String languageCode) {
            this.languageCode = languageCode;
        }

        public int getSampleRate() {
            return sampleRate;
        }

        public void setSampleRate(int sampleRate) {
            this.sampleRate = sampleRate;
        }
    }
}
