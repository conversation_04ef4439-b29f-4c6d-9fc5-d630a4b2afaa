package com.wishforthecure.forconversations.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.data.elasticsearch.repository.config.EnableReactiveElasticsearchRepositories;

/**
 * Configuration for Elasticsearch repositories.
 * This configuration is only enabled when Elasticsearch is available and configured.
 */
@Configuration
@ConditionalOnClass(ReactiveElasticsearchTemplate.class)
@ConditionalOnProperty(name = "spring.elasticsearch.uris", matchIfMissing = false)
@EnableReactiveElasticsearchRepositories(
    basePackages = "com.wishforthecure.forconversations.repository.search",
    excludeFilters = @ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        classes = {
            com.wishforthecure.forconversations.repository.MessageRepository.class,
            org.springframework.data.mongodb.repository.ReactiveMongoRepository.class,
        }
    )
)
public class ElasticsearchRepositoryConfiguration {

    private static final Logger LOG = LoggerFactory.getLogger(ElasticsearchRepositoryConfiguration.class);

    public ElasticsearchRepositoryConfiguration() {
        LOG.info("Elasticsearch repositories enabled with uris configuration");
    }
}
