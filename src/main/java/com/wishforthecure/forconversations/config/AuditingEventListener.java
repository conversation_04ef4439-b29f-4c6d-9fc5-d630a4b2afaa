package com.wishforthecure.forconversations.config;

import com.wishforthecure.forconversations.domain.AbstractAuditingEntity;
import java.time.Instant;
import org.springframework.data.mongodb.core.mapping.event.AbstractMongoEventListener;
import org.springframework.data.mongodb.core.mapping.event.BeforeConvertEvent;
import org.springframework.data.mongodb.core.mapping.event.BeforeSaveEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * Listener de eventos de MongoDB para auditoría automática.
 * Se ejecuta antes de guardar entidades que extienden AbstractAuditingEntity.
 */
@Component
public class AuditingEventListener extends AbstractMongoEventListener<AbstractAuditingEntity<?>> {

    /**
     * Se ejecuta antes de convertir la entidad para guardarla.
     * Establece automáticamente los campos de auditoría.
     */
    @Override
    public void onBeforeConvert(BeforeConvertEvent<AbstractAuditingEntity<?>> event) {
        AbstractAuditingEntity<?> entity = event.getSource();
        String currentUser = getCurrentUser();
        Instant now = Instant.now();

        // Si es una entidad nueva (sin createdBy), establecer campos de creación
        if (entity.getCreatedBy() == null) {
            entity.setCreatedBy(currentUser);
            entity.setCreatedDate(now);
        }

        // Siempre actualizar campos de modificación
        entity.setLastModifiedBy(currentUser);
        entity.setLastModifiedDate(now);
    }

    /**
     * Se ejecuta antes de guardar la entidad en la base de datos.
     * Backup por si onBeforeConvert no se ejecuta.
     */
    @Override
    public void onBeforeSave(BeforeSaveEvent<AbstractAuditingEntity<?>> event) {
        AbstractAuditingEntity<?> entity = event.getSource();
        String currentUser = getCurrentUser();
        Instant now = Instant.now();

        // Si es una entidad nueva (sin createdBy), establecer campos de creación
        if (entity.getCreatedBy() == null) {
            entity.setCreatedBy(currentUser);
            entity.setCreatedDate(now);
        }

        // Siempre actualizar campos de modificación
        entity.setLastModifiedBy(currentUser);
        entity.setLastModifiedDate(now);
    }

    /**
     * Obtiene el usuario actual del contexto de seguridad.
     *
     * @return El login del usuario actual o "system" si no hay usuario autenticado
     */
    private String getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            return "system";
        }

        String currentUser = authentication.getName();

        // Si es "anonymousUser", usar "system"
        if ("anonymousUser".equals(currentUser)) {
            return "system";
        }

        return currentUser;
    }
}
