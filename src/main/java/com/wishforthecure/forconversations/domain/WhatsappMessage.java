package com.wishforthecure.forconversations.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * A WhatsappMessage.
 */
@Document(collection = "whatsapp_message")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class WhatsappMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @NotNull(message = "must not be null")
    @Field("upload_id")
    private UUID uploadId;

    @NotNull(message = "must not be null")
    @Field("time")
    private Instant time;

    @NotNull(message = "must not be null")
    @Field("sender")
    private String sender;

    @NotNull(message = "must not be null")
    @Field("recipients")
    private String recipients;

    @NotNull(message = "must not be null")
    @Field("content")
    private String content;

    @NotNull(message = "must not be null")
    @Field("type")
    private SourceType type;

    @Field("whatsappMessageSource")
    @JsonIgnoreProperties(value = { "messages" }, allowSetters = true)
    private WhatsappMessageSource whatsappMessageSource;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getId() {
        return this.id;
    }

    public WhatsappMessage id(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return this.time;
    }

    public WhatsappMessage time(Instant time) {
        this.setTime(time);
        return this;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return this.sender;
    }

    public WhatsappMessage sender(String sender) {
        this.setSender(sender);
        return this;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getRecipients() {
        return this.recipients;
    }

    public WhatsappMessage recipients(String recipients) {
        this.setRecipients(recipients);
        return this;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public String getContent() {
        return this.content;
    }

    public WhatsappMessage content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public SourceType getType() {
        return this.type;
    }

    public WhatsappMessage type(SourceType type) {
        this.setType(type);
        return this;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    public WhatsappMessageSource getWhatsappMessageSource() {
        return this.whatsappMessageSource;
    }

    public void setWhatsappMessageSource(WhatsappMessageSource whatsappMessageSource) {
        this.whatsappMessageSource = whatsappMessageSource;
    }

    public WhatsappMessage whatsappMessageSource(WhatsappMessageSource whatsappMessageSource) {
        this.setWhatsappMessageSource(whatsappMessageSource);
        return this;
    }

    public UUID getUploadId() {
        return this.uploadId;
    }

    public WhatsappMessage uploadId(UUID uploadId) {
        this.setUploadId(uploadId);
        return this;
    }

    public void setUploadId(UUID uploadId) {
        this.uploadId = uploadId;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and
    // setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof WhatsappMessage)) {
            return false;
        }
        return getId() != null && getId().equals(((WhatsappMessage) o).getId());
    }

    @Override
    public int hashCode() {
        // see
        // https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "WhatsappMessage{" +
                "id=" + getId() +
                ", time='" + getTime() + "'" +
                ", sender='" + getSender() + "'" +
                ", recipients='" + getRecipients() + "'" +
                ", content='" + getContent() + "'" +
                ", type='" + getType() + "'" +
                ", uploadId='" + getUploadId() + "'" +
                "}";
    }
}
