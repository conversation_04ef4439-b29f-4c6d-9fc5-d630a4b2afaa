package com.wishforthecure.forconversations.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * A EmailMessage.
 */
@Document(collection = "email_message")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmailMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @NotNull(message = "must not be null")
    @Field("time")
    private Instant time;

    @NotNull(message = "must not be null")
    @Field("sender")
    private String sender;

    @NotNull(message = "must not be null")
    @Field("recipient")
    private String recipient;

    @NotNull(message = "must not be null")
    @Field("content")
    private String content;

    @NotNull(message = "must not be null")
    @Field("type")
    private SourceType type;

    @Field("emailMessageSource")
    @JsonIgnoreProperties(value = { "messages" }, allowSetters = true)
    private EmailMessageSource emailMessageSource;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getId() {
        return this.id;
    }

    public EmailMessage id(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return this.time;
    }

    public EmailMessage time(Instant time) {
        this.setTime(time);
        return this;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return this.sender;
    }

    public EmailMessage sender(String sender) {
        this.setSender(sender);
        return this;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getrecipient() {
        return this.recipient;
    }

    public EmailMessage recipient(String recipient) {
        this.setrecipient(recipient);
        return this;
    }

    public void setrecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getContent() {
        return this.content;
    }

    public EmailMessage content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public SourceType getType() {
        return this.type;
    }

    public EmailMessage type(SourceType type) {
        this.setType(type);
        return this;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    public EmailMessageSource getEmailMessageSource() {
        return this.emailMessageSource;
    }

    public void setEmailMessageSource(EmailMessageSource emailMessageSource) {
        this.emailMessageSource = emailMessageSource;
    }

    public EmailMessage emailMessageSource(EmailMessageSource emailMessageSource) {
        this.setEmailMessageSource(emailMessageSource);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmailMessage)) {
            return false;
        }
        return getId() != null && getId().equals(((EmailMessage) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmailMessage{" +
            "id=" + getId() +
            ", time='" + getTime() + "'" +
            ", sender='" + getSender() + "'" +
            ", recipient='" + getrecipient() + "'" +
            ", content='" + getContent() + "'" +
            ", type='" + getType() + "'" +
            "}";
    }
}
