package com.wishforthecure.forconversations.domain.enumeration;

public enum Feeling {
    // Positive Feelings
    HAPPY("😊", "Feliz", "#FFD700", "fa-regular fa-face-smile"),
    EXCITED("🤩", "Emocionado", "#FF6B35", "fa-regular fa-face-grin-stars"),
    GRATEFUL("🙏", "Agradecido", "#32CD32", "fa-solid fa-hand-holding-heart"),
    LOVE("❤️", "Amor", "#FF1493", "fa-regular fa-heart"),
    PROUD("🏆", "Orgulloso", "#FFD700", "fa-solid fa-award"),
    HOPEFUL("🌟", "Esperanzado", "#87CEEB", "fa-solid fa-star"),

    // Negative Feelings
    SAD("😢", "Triste", "#4682B4", "fa-regular fa-face-frown"),
    ANGRY("😠", "Enojado", "#DC143C", "fa-regular fa-face-angry"),
    FRUSTRATED("😤", "Frustrado", "#FF4500", "fa-regular fa-face-tired"),
    DISAPPOINTED("😞", "Decepcionado", "#708090", "fa-regular fa-face-sad-tear"),
    FEARFUL("😨", "Temeroso", "#9370DB", "fa-regular fa-face-flushed"),
    ANXIOUS("😰", "Ansioso", "#FF6347", "fa-solid fa-exclamation-triangle"),
    BLOCKED("🚫", "Bloqueado", "#8B0000", "fa-solid fa-ban"),

    // Neutral/Complex Feelings
    NEUTRAL("😐", "Neutral", "#808080", "fa-regular fa-face-meh"),
    SURPRISED("😲", "Sorprendido", "#FFA500", "fa-regular fa-face-surprise"),
    CONFUSED("🤔", "Confundido", "#DDA0DD", "fa-regular fa-circle-question"),
    THOUGHTFUL("🧠", "Pensativo", "#20B2AA", "fa-solid fa-brain"),
    CALM("😌", "Tranquilo", "#98FB98", "fa-solid fa-hand-peace"),
    CURIOUS("🔍", "Curioso", "#FFB6C1", "fa-solid fa-magnifying-glass");

    private final String emoji;
    private final String label;
    private final String color;
    private final String fontAwesomeIcon;

    Feeling(String emoji, String label, String color, String fontAwesomeIcon) {
        this.emoji = emoji;
        this.label = label;
        this.color = color;
        this.fontAwesomeIcon = fontAwesomeIcon;
    }

    public String getEmoji() {
        return emoji;
    }

    public String getLabel() {
        return label;
    }

    public String getColor() {
        return color;
    }

    public String getFontAwesomeIcon() {
        return fontAwesomeIcon;
    }
}
