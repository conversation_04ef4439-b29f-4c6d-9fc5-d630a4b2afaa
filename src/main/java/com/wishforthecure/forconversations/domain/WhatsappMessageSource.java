package com.wishforthecure.forconversations.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.wishforthecure.forconversations.domain.enumeration.UploadStatus;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "whatsapp_message_source")
public class WhatsappMessageSource implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @NotNull
    @Indexed(unique = true)
    @Field("upload_id")
    private UUID uploadId;

    @NotNull
    @Field("status")
    private UploadStatus status;

    @NotNull
    @Field("created_at")
    private Instant createdAt;

    @Field("name_person_one")
    private String namePersonOne;

    @Field("name_person_two")
    private String namePersonTwo;

    @Field("alias_person_one")
    private String aliasPersonOne;

    @Field("alias_person_two")
    private String aliasPersonTwo;

    @Field("file")
    private byte[] file;

    // @NotNull - Removed to allow null after processing
    @Field("file_content_type")
    private String fileContentType;

    // This set will remain empty during the PENDING_CONFIRMATION state.
    @Field("messages")
    @JsonIgnoreProperties(value = { "whatsappMessageSource" }, allowSetters = true)
    private Set<WhatsappMessage> messages = new HashSet<>();

    // Getters and Setters for all fields...

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public UUID getUploadId() {
        return uploadId;
    }

    public void setUploadId(UUID uploadId) {
        this.uploadId = uploadId;
    }

    public UploadStatus getStatus() {
        return status;
    }

    public void setStatus(UploadStatus status) {
        this.status = status;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getNamePersonOne() {
        return namePersonOne;
    }

    public void setNamePersonOne(String namePersonOne) {
        this.namePersonOne = namePersonOne;
    }

    public String getNamePersonTwo() {
        return namePersonTwo;
    }

    public void setNamePersonTwo(String namePersonTwo) {
        this.namePersonTwo = namePersonTwo;
    }

    public String getAliasPersonOne() {
        return aliasPersonOne;
    }

    public void setAliasPersonOne(String aliasPersonOne) {
        this.aliasPersonOne = aliasPersonOne;
    }

    public String getAliasPersonTwo() {
        return aliasPersonTwo;
    }

    public void setAliasPersonTwo(String aliasPersonTwo) {
        this.aliasPersonTwo = aliasPersonTwo;
    }

    public byte[] getFile() {
        return file;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFileContentType() {
        return fileContentType;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    public Set<WhatsappMessage> getMessages() {
        return messages;
    }

    public void setMessages(Set<WhatsappMessage> messages) {
        this.messages = messages;
    }
}
