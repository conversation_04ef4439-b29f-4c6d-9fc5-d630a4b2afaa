package com.wishforthecure.forconversations.web.rest;

import com.wishforthecure.forconversations.domain.Message;
import com.wishforthecure.forconversations.domain.enumeration.Feeling;
import com.wishforthecure.forconversations.repository.MessageRepository;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.dto.DashboardFilterDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.web.rest.errors.BadRequestAlertException;
import com.wishforthecure.forconversations.web.rest.errors.ElasticsearchExceptionMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.reactive.ResponseUtil;

/**
 * REST controller for managing {@link Message}.
 */
@RestController
@RequestMapping("/api/messages")
public class MessageResource {

    private static final Logger LOG = LoggerFactory.getLogger(MessageResource.class);

    private static final String ENTITY_NAME = "message";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final MessageService messageService;

    private final MessageRepository messageRepository;

    public MessageResource(MessageService messageService, MessageRepository messageRepository) {
        this.messageService = messageService;
        this.messageRepository = messageRepository;
    }

    /**
     * {@code POST  /messages} : Create a new message.
     *
     * @param messageDTO the messageDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new messageDTO, or with status {@code 400 (Bad Request)} if the message has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public Mono<ResponseEntity<MessageDTO>> createMessage(@Valid @RequestBody MessageDTO messageDTO) throws URISyntaxException {
        LOG.debug("REST request to save Message : {}", messageDTO);
        if (messageDTO.getId() != null) {
            throw new BadRequestAlertException("A new message cannot already have an ID", ENTITY_NAME, "idexists");
        }
        return messageService
            .save(messageDTO)
            .map(result -> {
                try {
                    return ResponseEntity.created(new URI("/api/messages/" + result.getId()))
                        .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, result.getId()))
                        .body(result);
                } catch (URISyntaxException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    /**
     * {@code PUT  /messages/:id} : Updates an existing message.
     *
     * @param id the id of the messageDTO to save.
     * @param messageDTO the messageDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated messageDTO,
     * or with status {@code 400 (Bad Request)} if the messageDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the messageDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public Mono<ResponseEntity<MessageDTO>> updateMessage(
        @PathVariable(required = false) final String id,
        @Valid @RequestBody MessageDTO messageDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Message : {}, {}", id, messageDTO);
        if (messageDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, messageDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        return messageRepository
            .existsById(id)
            .flatMap(exists -> {
                if (!exists) {
                    return Mono.error(new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound"));
                }

                return messageService
                    .update(messageDTO)
                    .switchIfEmpty(Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND)))
                    .map(result ->
                        ResponseEntity.ok()
                            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, result.getId()))
                            .body(result)
                    );
            });
    }

    /**
     * {@code PATCH  /messages/:id} : Partial updates given fields of an existing message, field will ignore if it is null
     *
     * @param id the id of the messageDTO to save.
     * @param messageDTO the messageDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated messageDTO,
     * or with status {@code 400 (Bad Request)} if the messageDTO is not valid,
     * or with status {@code 404 (Not Found)} if the messageDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the messageDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public Mono<ResponseEntity<MessageDTO>> partialUpdateMessage(
        @PathVariable(required = false) final String id,
        @NotNull @RequestBody MessageDTO messageDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Message partially : {}, {}", id, messageDTO);
        if (messageDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, messageDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        return messageRepository
            .existsById(id)
            .flatMap(exists -> {
                if (!exists) {
                    return Mono.error(new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound"));
                }

                Mono<MessageDTO> result = messageService.partialUpdate(messageDTO);

                return result
                    .switchIfEmpty(Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND)))
                    .map(res ->
                        ResponseEntity.ok()
                            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, res.getId()))
                            .body(res)
                    );
            });
    }

    /**
     * {@code GET  /messages} : get all the messages.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of messages in body.
     */
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<List<MessageDTO>> getAllMessages() {
        LOG.debug("REST request to get all Messages");
        return messageService.findAll().collectList();
    }

    /**
     * {@code GET  /messages} : get all the messages as a stream.
     * @return the {@link Flux} of messages.
     */
    @GetMapping(value = "", produces = MediaType.APPLICATION_NDJSON_VALUE)
    public Flux<MessageDTO> getAllMessagesAsStream() {
        LOG.debug("REST request to get all Messages as a stream");
        return messageService.findAll();
    }

    /**
     * {@code GET  /messages/:id} : get the "id" message.
     *
     * @param id the id of the messageDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the messageDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public Mono<ResponseEntity<MessageDTO>> getMessage(@PathVariable String id) {
        LOG.debug("REST request to get Message : {}", id);
        Mono<MessageDTO> messageDTO = messageService.findOne(id);
        return ResponseUtil.wrapOrNotFound(messageDTO);
    }

    /**
     * {@code DELETE  /messages/:id} : delete the "id" message.
     *
     * @param id the id of the messageDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> deleteMessage(@PathVariable String id) {
        LOG.debug("REST request to delete Message : {}", id);
        return messageService
            .delete(id)
            .then(
                Mono.just(
                    ResponseEntity.noContent().headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id)).build()
                )
            );
    }

    /**
     * {@code SEARCH  /messages/_search?query=:query} : search for the message corresponding
     * to the query.
     *
     * @param query the query of the message search.
     * @return the result of the search.
     */
    @GetMapping("/_search")
    public Mono<List<MessageDTO>> searchMessages(@RequestParam String query) {
        LOG.debug("REST request to search Messages for query {}", query);
        try {
            return messageService.search(query).collectList();
        } catch (RuntimeException e) {
            throw ElasticsearchExceptionMapper.mapException(e);
        }
    }

    /**
     * {@code POST  /messages/dashboard-filter} : search for messages using dashboard filter criteria.
     *
     * @param filter the filter criteria for the message search.
     * @return the result of the search.
     */
    @PostMapping("/dashboard-filter")
    public Mono<List<MessageDTO>> searchMessagesByFilter(@RequestBody DashboardFilterDTO filter) {
        LOG.debug("REST request to search Messages by filter: {}", filter);
        try {
            return messageService.searchByFilter(filter).collectList();
        } catch (RuntimeException e) {
            LOG.error("Error searching messages by filter", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error searching messages");
        }
    }

    /**
     * {@code PUT  /messages/:id/feelings} : Update feelings for a specific message.
     *
     * @param id the id of the message to update feelings for.
     * @param feelings the list of feelings to set for the message.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated messageDTO.
     */
    @PutMapping("/{id}/feelings")
    public Mono<ResponseEntity<MessageDTO>> updateMessageFeelings(@PathVariable String id, @RequestBody List<Feeling> feelings) {
        LOG.debug("REST request to update feelings for Message : {}, feelings: {}", id, feelings);

        return messageService
            .findOne(id)
            .switchIfEmpty(Mono.error(new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound")))
            .flatMap(messageDTO -> {
                messageDTO.setFeelingList(feelings);
                return messageService.update(messageDTO);
            })
            .map(result ->
                ResponseEntity.ok()
                    .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, result.getId()))
                    .body(result)
            );
    }
}
