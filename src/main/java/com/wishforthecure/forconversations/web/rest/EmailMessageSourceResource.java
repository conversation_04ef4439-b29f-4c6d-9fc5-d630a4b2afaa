package com.wishforthecure.forconversations.web.rest;

import com.wishforthecure.forconversations.service.EmailMessageSourceService;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.Part;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

/**
 * REST controller for managing email message sources.
 */
@RestController
@RequestMapping("/api/email-message-sources")
public class EmailMessageSourceResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmailMessageSourceResource.class);
    private final EmailMessageSourceService emailMessageSourceService;

    public EmailMessageSourceResource(EmailMessageSourceService emailMessageSourceService) {
        this.emailMessageSourceService = emailMessageSourceService;
    }

    @PostMapping(value = "/load-data", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<Void> loadChatData(@RequestPart("file") Part filePart) {
        LOG.debug("Processing email data file");

        return filePart
            .content()
            .collectList()
            .flatMap(buffers -> {
                try {
                    // Convertir los DataBuffer a un solo byte array
                    int size = buffers.stream().mapToInt(DataBuffer::readableByteCount).sum();
                    byte[] bytes = new byte[size];
                    int offset = 0;

                    for (DataBuffer buffer : buffers) {
                        int length = buffer.readableByteCount();
                        buffer.read(bytes, offset, length);
                        DataBufferUtils.release(buffer);
                        offset += length;
                    }

                    // Procesar el archivo
                    return emailMessageSourceService.loadFile(bytes);
                } catch (Exception e) {
                    LOG.error("Error processing file", e);
                    return Mono.error(
                        new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error al procesar el archivo: " + e.getMessage())
                    );
                }
            })
            .onErrorResume(e -> {
                if (!(e instanceof ResponseStatusException)) {
                    LOG.error("Unexpected error processing file", e);
                    return Mono.error(
                        new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error inesperado al procesar el archivo")
                    );
                }
                return Mono.error(e);
            });
    }

    @PostMapping(value = "/diagnose", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<String>> diagnoseFile(@RequestPart("file") Part filePart) {
        LOG.info("Diagnosing file");

        return filePart
            .content()
            .collectList()
            .map(buffers -> {
                try {
                    // Convertir los DataBuffer a un solo byte array
                    int size = buffers.stream().mapToInt(DataBuffer::readableByteCount).sum();
                    byte[] bytes = new byte[size];
                    int offset = 0;

                    for (DataBuffer buffer : buffers) {
                        int length = buffer.readableByteCount();
                        buffer.read(bytes, offset, length);
                        DataBufferUtils.release(buffer);
                        offset += length;
                    }

                    StringBuilder diagnosis = new StringBuilder();
                    diagnosis.append("File Analysis:\n");
                    diagnosis.append("Size: ").append(bytes.length).append(" bytes\n");

                    String content = new String(bytes, StandardCharsets.UTF_8);
                    String[] lines = content.split("\n", 10);

                    diagnosis.append("First ").append(Math.min(lines.length, 5)).append(" lines:\n");
                    for (int i = 0; i < Math.min(lines.length, 5); i++) {
                        diagnosis.append("Line ").append(i + 1).append(": '").append(lines[i]).append("'\n");
                    }

                    // Check if it looks like MBOX format
                    boolean hasFromLine = lines.length > 0 && lines[0].startsWith("From ");
                    diagnosis.append("\nMBOX Format Check:\n");
                    diagnosis.append("Has 'From ' line: ").append(hasFromLine).append("\n");

                    if (hasFromLine) {
                        diagnosis.append("From line: '").append(lines[0]).append("'\n");
                        // Check if it matches the expected pattern
                        boolean matchesPattern = lines[0].matches("^From \\S+@\\S.*\\d{4}$");
                        diagnosis.append("Matches MBOX pattern: ").append(matchesPattern).append("\n");
                    }

                    return ResponseEntity.ok(diagnosis.toString());
                } catch (Exception e) {
                    LOG.error("Error diagnosing file", e);
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error diagnosing file: " + e.getMessage());
                }
            });
    }
}
