package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.List;
import java.util.stream.Collectors;
import org.jmolecules.ddd.annotation.Service;

@Service
public class MessageFilteringService {

    /**
     * Filters a list of messages by applying a sequence of filters.
     * A message is kept only if it passes ALL filters in the chain.
     *
     * @param messages The original list of messages.
     * @param filters  The ordered list of filters to apply.
     * @return A new list containing only the messages that passed all filters.
     */
    public List<Message> filterMessages(List<? extends Message> messages, List<MessageFilter> filters) {
        if (filters == null || filters.isEmpty()) {
            return List.copyOf(messages);
        }

        return messages.stream().filter(message -> passesAllFilters(message, filters)).collect(Collectors.toList());
    }

    private boolean passesAllFilters(Message message, List<MessageFilter> filters) {
        for (MessageFilter filter : filters) {
            if (!filter.apply(message)) {
                return false; // Short-circuit if any filter fails
            }
        }
        return true;
    }
}
