package com.wishforthecure.forconversations.hexa.domain.model.conversation;

import java.io.Serializable;
import java.util.UUID;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class ConversationId implements Serializable {

    UUID value;

    private ConversationId(UUID value) {
        this.value = value;
    }

    public static ConversationId of(UUID value) {
        if (value == null) {
            throw new IllegalArgumentException("ConversationId cannot be null.");
        }
        return new ConversationId(value);
    }

    public static ConversationId create() {
        return new ConversationId(UUID.randomUUID());
    }
}
