package com.wishforthecure.forconversations.hexa.domain.model.filter;

import java.util.regex.Pattern;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class EmailAddress {

    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Z0-9_!#$%&'*+/=?`{|}~^.-]+@[A-Z0-9.-]+$", Pattern.CASE_INSENSITIVE);

    String value;

    private EmailAddress(String value) {
        this.value = value;
    }

    public static EmailAddress of(String value) {
        if (value == null || !EMAIL_PATTERN.matcher(value).matches()) {
            throw new IllegalArgumentException("Invalid email address format: " + value);
        }
        return new EmailAddress(value);
    }
}
