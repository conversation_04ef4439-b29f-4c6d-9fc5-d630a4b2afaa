package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class KeywordFilter implements MessageFilter {

    Set<Keyword> keywords;
    FilterMode mode;

    public static KeywordFilter of(Set<String> keywords, FilterMode mode) {
        if (keywords == null || keywords.isEmpty() || mode == null) {
            throw new IllegalArgumentException("Keywords and mode cannot be null or empty.");
        }
        Set<Keyword> keywordObjects = keywords.stream().map(Keyword::of).collect(Collectors.toSet());
        return new KeywordFilter(keywordObjects, mode);
    }

    @Override
    public boolean apply(Message message) {
        boolean matches = textContainsAllKeywords(message.getTextContent());
        return (mode == FilterMode.INCLUDE) ? matches : !matches;
    }

    private boolean textContainsAllKeywords(String textContent) {
        if (textContent == null || textContent.isBlank()) {
            return false;
        }
        String lowerCaseContent = textContent.toLowerCase();
        return keywords.stream().allMatch(keyword -> lowerCaseContent.contains(keyword.getValue()));
    }
}
