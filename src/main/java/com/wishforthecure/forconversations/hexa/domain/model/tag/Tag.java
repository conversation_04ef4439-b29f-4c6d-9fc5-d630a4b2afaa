package com.wishforthecure.forconversations.hexa.domain.model.tag;

import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class Tag {

    String value;

    private Tag(String value) {
        this.value = value;
    }

    public static Tag of(String value) {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException("Tag value cannot be blank.");
        }
        return new Tag(value.trim().toLowerCase());
    }
}
