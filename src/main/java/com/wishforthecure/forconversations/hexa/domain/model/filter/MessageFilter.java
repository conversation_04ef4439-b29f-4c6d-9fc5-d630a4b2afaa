package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
public interface MessageFilter {
    /**
     * Applies the filter logic to a given message.
     *
     * @param message The message to evaluate.
     * @return {@code true} if the message should be kept, {@code false} otherwise.
     */
    boolean apply(Message message);
}
