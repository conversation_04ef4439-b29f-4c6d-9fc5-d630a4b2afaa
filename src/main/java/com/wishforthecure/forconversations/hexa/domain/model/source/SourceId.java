package com.wishforthecure.forconversations.hexa.domain.model.source;

import java.io.Serializable;
import java.util.UUID;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class SourceId implements Serializable {

    UUID value;

    private SourceId(UUID value) {
        this.value = value;
    }

    public static SourceId of(UUID value) {
        if (value == null) {
            throw new IllegalArgumentException("SourceId cannot be null.");
        }
        return new SourceId(value);
    }

    public static SourceId create() {
        return new SourceId(UUID.randomUUID());
    }
}
