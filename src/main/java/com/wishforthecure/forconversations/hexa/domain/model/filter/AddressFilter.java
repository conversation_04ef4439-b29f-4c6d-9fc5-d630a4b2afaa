package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAlias;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessage;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class AddressFilter implements MessageFilter {

    Set<EmailAddress> addresses;
    FilterMode mode;

    public static AddressFilter of(Set<String> emailStrings, FilterMode mode) {
        if (emailStrings == null || emailStrings.isEmpty() || mode == null) {
            throw new IllegalArgumentException("Email addresses and mode cannot be null or empty.");
        }
        Set<EmailAddress> emailAddressObjects = emailStrings.stream().map(EmailAddress::of).collect(Collectors.toSet());
        return new AddressFilter(emailAddressObjects, mode);
    }

    @Override
    public boolean apply(Message message) {
        // This filter only applies to EmailMessage instances.
        // For other message types, it has no effect (returns true).
        if (!(message instanceof EmailMessage)) {
            return true;
        }

        boolean matches = containsAddress((EmailMessage) message);
        return (mode == FilterMode.INCLUDE) ? matches : !matches;
    }

    private boolean containsAddress(EmailMessage emailMessage) {
        EmailAlias alias = emailMessage.getEmailAlias();
        if (alias == null) {
            return false;
        }

        EmailAddress sender = EmailAddress.of(alias.getSenderEmail());
        EmailAddress recipient = EmailAddress.of(alias.getRecipientEmail());

        return addresses.contains(sender) || addresses.contains(recipient);
    }
}
