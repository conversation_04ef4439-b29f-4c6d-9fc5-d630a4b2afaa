package com.wishforthecure.forconversations.hexa.domain.model.conversation;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.Feeling;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.tag.Tag;
import java.time.Instant;
import java.util.*;
import lombok.Getter;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Getter
public class Conversation {

    private final ConversationId id;
    private String name;
    private Instant startDate;
    private Instant endDate;
    private Feeling feeling;
    private final Set<Tag> tags;
    private final List<Message> messages;

    private Conversation(ConversationId id, String name) {
        this.id = id;
        this.name = name;
        this.messages = new ArrayList<>();
        this.tags = new HashSet<>();
        this.feeling = Feeling.NEUTRAL;
    }

    public static Conversation create(String name) {
        if (name == null || name.isBlank()) {
            throw new IllegalArgumentException("Conversation name cannot be blank.");
        }
        return new Conversation(ConversationId.create(), name);
    }

    public void addMessage(Message message) {
        if (this.endDate != null) {
            throw new IllegalStateException("Cannot add messages to a concluded conversation.");
        }
        this.messages.add(message);
        this.messages.sort(Comparator.comparing(Message::getTimestamp));
        updateStartDate();
    }

    public void conclude(Instant endDate) {
        if (this.startDate != null && endDate.isBefore(this.startDate)) {
            throw new IllegalStateException("End date cannot be before the start date.");
        }
        this.endDate = endDate;
    }

    public void assignFeeling(Feeling feeling) {
        this.feeling = feeling;
    }

    public void addTag(Tag tag) {
        this.tags.add(tag);
    }

    public void removeTag(Tag tag) {
        this.tags.remove(tag);
    }

    public void rename(String newName) {
        if (newName == null || newName.isBlank()) {
            throw new IllegalArgumentException("Conversation name cannot be blank.");
        }
        this.name = newName;
    }

    private void updateStartDate() {
        if (!this.messages.isEmpty()) {
            this.startDate = this.messages.get(0).getTimestamp();
        }
    }

    public Set<Tag> getTags() {
        return Collections.unmodifiableSet(tags);
    }

    public List<Message> getMessages() {
        return Collections.unmodifiableList(messages);
    }
}
