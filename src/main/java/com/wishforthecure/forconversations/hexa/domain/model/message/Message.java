package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.Alias;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.Feeling;
import java.time.Instant;
import java.util.List;

public interface Message {
    MessageId getMessageId();
    Instant getTime();
    String getSender();
    String getRecipients();
    String getContent();
    Alias getAlias();
    List<Feeling> getFeelingList();
}
