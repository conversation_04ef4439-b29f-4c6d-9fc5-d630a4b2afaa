package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessage;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.Entity;

@Entity
@Getter
@AllArgsConstructor
public class WhatsAppSource implements Source {

    private final SourceId sourceId;
    private final Instant timestamp;
    private final List<WhatsAppMessage> whatsAppMessages;
    private final byte[] source;
}
