package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import java.util.List;

public interface SourceParser {
    /**
     * Parses a Source object to extract a list of Messages.
     *
     * @param source The source data to parse.
     * @return A list of messages contained within the source.
     */
    List<Message> parse(Source source);

    /**
     * Indicates which SourceType this parser can handle.
     *
     * @return The SourceType supported by this parser.
     */
    SourceType getSupportedSourceType();
}
