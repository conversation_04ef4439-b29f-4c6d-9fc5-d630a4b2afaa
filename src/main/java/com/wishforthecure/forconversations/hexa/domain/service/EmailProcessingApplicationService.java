package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.filter.*;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessage;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.service.MessageFilteringService;
import java.time.Instant;
import java.util.List;
import java.util.Set;

/**
 * Example of an Application Service that uses the domain filtering logic.
 * This class would belong in your 'application' layer.
 */
public class EmailProcessingApplicationService {

    private final MessageFilteringService filteringService;

    // The service is injected via constructor.
    public EmailProcessingApplicationService(MessageFilteringService filteringService) {
        this.filteringService = filteringService;
    }

    /**
     * Executes a filtering process on a list of incoming emails.
     *
     * @param incomingEmails The raw list of emails to be processed.
     * @return A list of emails that have passed all defined filter criteria.
     */
    public List<Message> processAndFilterEmails(List<EmailMessage> incomingEmails) {
        // 1. Define the filters to apply.
        // Includes messages from the first half of the year 2025.
        MessageFilter dateFilter = DateRangeFilter.of(
            Instant.parse("2025-01-01T00:00:00Z"),
            Instant.parse("2025-06-30T23:59:59Z"),
            FilterMode.INCLUDE
        );

        // Excludes messages containing the word "promotion".
        MessageFilter keywordFilter = KeywordFilter.of(Set.of("promotion"), FilterMode.EXCLUDE);

        // Excludes messages from a known blocklist.
        MessageFilter addressFilter = AddressFilter.of(Set.of("<EMAIL>", "<EMAIL>"), FilterMode.EXCLUDE);

        // 2. Create the ordered list of filters for the chain.
        List<MessageFilter> activeFilters = List.of(dateFilter, keywordFilter, addressFilter);

        // 3. Use the injected domain service to apply the filters.
        List<Message> processedMessages = filteringService.filterMessages(incomingEmails, activeFilters);

        // 4. Return the resulting list for further processing.
        return processedMessages;
    }
}
