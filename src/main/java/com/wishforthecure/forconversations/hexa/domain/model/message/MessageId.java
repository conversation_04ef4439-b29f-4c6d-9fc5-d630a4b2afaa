package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.io.Serializable;
import java.util.UUID;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class MessageId implements Serializable {

    UUID value;

    private MessageId(UUID value) {
        this.value = value;
    }

    public static MessageId of(UUID value) {
        if (value == null) {
            throw new IllegalArgumentException("MessageId cannot be null.");
        }
        return new MessageId(value);
    }

    public static MessageId create() {
        return new MessageId(UUID.randomUUID());
    }
}
