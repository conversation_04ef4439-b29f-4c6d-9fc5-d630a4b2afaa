package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.Alias;
import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAlias;
import java.time.Instant;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class EmailMessage implements Message {

    MessageId messageId;
    Instant timestamp;
    EmailAlias emailAlias;
    String textContent;

    @Override
    public Alias getAlias() {
        return emailAlias;
    }
}
