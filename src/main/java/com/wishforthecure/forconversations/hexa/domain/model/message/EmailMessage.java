package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.Alias;
import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAlias;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
@Data
@AllArgsConstructor
public class EmailMessage implements Message {

    MessageId messageId;
    Instant timestamp;
    EmailAlias sender;
    List<EmailAlias> recipients;
    String content;
    SourceType sourceType = SourceType.EMAIL;
    @Override
    public Instant getTime() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getTime'");
    }
    @Override
    public Alias getAlias() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getAlias'");
    }
}
