package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import java.util.Optional;
import org.jmolecules.ddd.annotation.Repository;

@Repository
public interface SourceRepository {
    /**
     * Saves a given source.
     *
     * @param source The source to save.
     * @return The saved source.
     */
    Source save(Source source);

    /**
     * Retrieves a source by its ID.
     *
     * @param id The ID of the source.
     * @return An Optional containing the source if found, or empty if not.
     */
    Optional<Source> findById(SourceId id);
}
