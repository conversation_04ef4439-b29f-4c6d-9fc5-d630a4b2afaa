package com.wishforthecure.forconversations.hexa.domain.model.filter;

import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public final class Keyword {

    String value;

    private Keyword(String value) {
        this.value = value;
    }

    public static Keyword of(String value) {
        if (value == null || value.isBlank()) {
            throw new IllegalArgumentException("Keyword value cannot be blank.");
        }
        return new Keyword(value.trim().toLowerCase());
    }
}
