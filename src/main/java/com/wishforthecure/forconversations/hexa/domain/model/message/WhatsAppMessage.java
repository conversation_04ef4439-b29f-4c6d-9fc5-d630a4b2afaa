package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.Alias;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppAlias;
import java.time.Instant;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class WhatsAppMessage implements Message {

    MessageId messageId;
    Instant timestamp;
    WhatsAppAlias whatsAppAlias;
    String textContent;
    byte[] file;

    @Override
    public Alias getAlias() {
        return whatsAppAlias;
    }
}
