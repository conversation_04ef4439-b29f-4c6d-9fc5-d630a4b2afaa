package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.Alias;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppAlias;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.Feeling;
import java.time.Instant;
import java.util.List;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class WhatsAppMessage implements Message {

    MessageId messageId;
    Instant timestamp;
    WhatsAppAlias whatsAppAlias;
    String textContent;
    byte[] file;
    List<Feeling> feelingList;

    @Override
    public Instant getTime() {
        return timestamp;
    }

    @Override
    public String getSender() {
        return whatsAppAlias != null ? whatsAppAlias.toString() : null;
    }

    @Override
    public String getRecipients() {
        return null; // WhatsApp messages typically don't have multiple recipients
    }

    @Override
    public String getContent() {
        return textContent;
    }

    @Override
    public Alias getAlias() {
        return whatsAppAlias;
    }
}
