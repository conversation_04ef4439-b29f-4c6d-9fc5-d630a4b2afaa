package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import java.util.List;
import java.util.Optional;
import org.jmolecules.ddd.annotation.Repository;

@Repository
public interface ConversationRepository {
    /**
     * Saves a given conversation.
     *
     * @param conversation The conversation to save.
     * @return The saved conversation.
     */
    Conversation save(Conversation conversation);

    /**
     * Retrieves a conversation by its ID.
     *
     * @param id The ID of the conversation.
     * @return An Optional containing the conversation if found, or empty if not.
     */
    Optional<Conversation> findById(ConversationId id);

    /**
     * Deletes a conversation by its ID.
     *
     * @param id The ID of the conversation to delete.
     */
    void deleteById(ConversationId id);

    /**
     * Retrieves all conversations.
     *
     * @return A list of all conversations.
     */
    List<Conversation> findAll();
}
