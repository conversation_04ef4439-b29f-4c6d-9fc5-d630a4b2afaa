package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.time.Instant;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class DateRangeFilter implements MessageFilter {

    Instant startDate;
    Instant endDate;
    FilterMode mode;

    public static DateRangeFilter of(Instant startDate, Instant endDate, FilterMode mode) {
        if (startDate == null || endDate == null || mode == null) {
            throw new IllegalArgumentException("Filter parameters cannot be null.");
        }
        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("End date cannot be before start date.");
        }
        return new DateRangeFilter(startDate, endDate, mode);
    }

    @Override
    public boolean apply(Message message) {
        boolean matches = isWithinRange(message.getTimestamp());
        return (mode == FilterMode.INCLUDE) ? matches : !matches;
    }

    private boolean isWithinRange(Instant timestamp) {
        return !timestamp.isBefore(startDate) && !timestamp.isAfter(endDate);
    }
}
