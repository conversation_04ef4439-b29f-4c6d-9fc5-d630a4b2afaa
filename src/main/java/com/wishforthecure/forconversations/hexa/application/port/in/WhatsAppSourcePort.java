package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;
import reactor.core.publisher.Mono;

public interface WhatsAppSourcePort {
    Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO);
    Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO);
}
