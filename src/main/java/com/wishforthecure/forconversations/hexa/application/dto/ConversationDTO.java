package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.Feeling;
import com.wishforthecure.forconversations.hexa.domain.model.tag.Tag;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import lombok.Value;

@Value
public class ConversationDTO {

    ConversationId id;
    String name;
    Instant startDate;
    Instant endDate;
    Feeling feeling;
    Set<Tag> tags;
    // Para simplificar, no incluiremos los mensajes en la lista principal,
    // podrían tener su propio endpoint si son muchos.
}
