package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.MessagePort;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import java.util.List;
import reactor.core.publisher.Mono;

public class MessageService implements MessagePort {

    @Override
    public Mono<MessageDTO> save(MessageDTO messageDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }

    @Override
    public Mono<List<MessageDTO>> saveAll(List<MessageDTO> messageDTOList) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'saveAll'");
    }
}
