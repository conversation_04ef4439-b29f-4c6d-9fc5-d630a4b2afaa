package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.SourcePort;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import reactor.core.publisher.Mono;

public class SourceService implements SourcePort {

    @Override
    public Mono<SourceDTO> save(SourceDTO sourceDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}
