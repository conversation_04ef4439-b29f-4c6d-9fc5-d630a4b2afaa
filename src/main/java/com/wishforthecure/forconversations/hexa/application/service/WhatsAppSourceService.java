package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.WhatsAppSourcePort;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;
import reactor.core.publisher.Mono;

public class WhatsAppSourceService implements WhatsAppSourcePort {

    @Override
    public Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'upload'");
    }

    @Override
    public Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}
