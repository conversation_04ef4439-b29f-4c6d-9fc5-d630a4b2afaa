package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.hexa.application.port.in.EmailSourcePort;
import com.wishforthecure.forconversations.hexa.application.port.in.MessagePort;
import com.wishforthecure.forconversations.hexa.application.port.in.SourcePort;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessage;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EmailSourceService implements EmailSourcePort {

    private static final Logger LOG = LoggerFactory.getLogger(EmailSourceService.class);

    private final MessagePort messagePort;
    private final SourcePort sourcePort;
    private final MBoxUtils mboxUtils;

    public EmailSourceService(MessagePort messagePort, SourcePort sourcePort, MBoxUtils mboxUtils) {
        this.messagePort = messagePort;
        this.sourcePort = sourcePort;
        this.mboxUtils = mboxUtils;
    }

    @Override
    public Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO) {
        byte[] bytes = emailSourceUploadDTO.getFile();

        // Parse MBOX file using MBoxUtils
        List<EmailMessage> emailMessages = mboxUtils.parse(bytes);

        // Convert EmailMessage to MessageDTO
        List<MessageDTO> messagesDTOList = emailMessages
            .stream()
            .map(emailMessage ->
                new MessageDTO(emailMessage.getTime(), emailMessage.getSender(), emailMessage.getRecipients(), emailMessage.getContent(), SourceType.EMAIL)
            )
            .collect(Collectors.toList());

        LOG.debug("Parsed {} email messages from MBOX file", messagesDTOList.size());

        return messagePort
            .saveAll(messagesDTOList)
            .flatMap(savedMessages -> {
                // Extract all message IDs
                List<String> messageIds = savedMessages.stream().map(MessageDTO::getId).collect(Collectors.toList());

                // Set required fields on sourceDTO
                SourceDTO sourceDTO = new SourceDTO(Instant.now(), messageIds, SourceType.EMAIL, bytes, null);

                // Generate sourceId and save the source
                return sourceDTO
                    .withGeneratedIds()
                    .flatMap(updatedSource -> {
                        LOG.debug("Saving source with message IDs: {}", messageIds);
                        return sourcePort.save(updatedSource);
                    });
            })
            .then()
            .thenReturn(new EmailSourceSaveDTO());
    }



    @Override
    public Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO) {
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}
