package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.hexa.application.port.in.EmailSourcePort;
import com.wishforthecure.forconversations.hexa.application.port.in.MessagePort;
import com.wishforthecure.forconversations.hexa.application.port.in.SourcePort;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessage;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EmailSourceService implements EmailSourcePort {

    private static final Logger LOG = LoggerFactory.getLogger(EmailSourceService.class);

    private final MessagePort messagePort;
    private final SourcePort sourcePort;
    private final MBoxUtils mboxUtils;

    public EmailSourceService(MessagePort messagePort, SourcePort sourcePort, MBoxUtils mboxUtils) {
        this.messagePort = messagePort;
        this.sourcePort = sourcePort;
        this.mboxUtils = mboxUtils;
    }

    @Override
    public Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO) {
        byte[] file = emailSourceUploadDTO.getFile();
        List<EmailMessage> emailMessages = extractData(file);

        return messagePort.saveAll(emailMessages)
                .flatMap(savedMessages -> {
                    List<String> messageIds = savedMessages.stream()
                            .map(EmailMessage::getMessageId)
                            .map(messageId -> messageId.toString())
                            .collect(Collectors.toList());

                    SourceDTO sourceDTO = new SourceDTO(
                            Instant.now(),
                            messageIds,
                            SourceType.EMAIL,
                            file,
                            null);

                    return sourcePort.save(sourceDTO)
                            .map(savedSource -> new EmailSourceSaveDTO(savedSource.getId(), messageIds.size()));
                });
    }

    private List<EmailMessage> extractData(byte[] file) {
        return mboxUtils.parse(file);
    }

    private MessageDTO convertToMessageDTO(EmailMessage emailMessage) {
        MessageDTO dto = new MessageDTO();
        dto.setTime(emailMessage.getTime());
        dto.setSender(emailMessage.getSender());
        dto.setRecipient(emailMessage.getRecipients());
        dto.setContent(emailMessage.getContent());
        dto.setType(SourceType.EMAIL);
        // Convert domain feelings to DTO feelings if needed
        if (emailMessage.getFeelingList() != null) {
            List<com.wishforthecure.forconversations.domain.enumeration.Feeling> dtoFeelings = emailMessage
                    .getFeelingList().stream()
                    .map(this::convertFeelingToDTO)
                    .collect(Collectors.toList());
            dto.setFeelingList(dtoFeelings);
        }
        return dto;
    }

    private com.wishforthecure.forconversations.domain.enumeration.Feeling convertFeelingToDTO(
            com.wishforthecure.forconversations.hexa.domain.model.feeling.Feeling domainFeeling) {
        return com.wishforthecure.forconversations.domain.enumeration.Feeling.valueOf(domainFeeling.name());
    }

    @Override
    public Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO) {
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}
