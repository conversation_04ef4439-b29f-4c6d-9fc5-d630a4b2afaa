package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.hexa.application.port.in.EmailSourcePort;
import com.wishforthecure.forconversations.hexa.application.port.in.MessagePort;
import com.wishforthecure.forconversations.hexa.application.port.in.SourcePort;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessage;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;

public class EmailSourceService implements EmailSourcePort {

    private final MessagePort messagePort;
    private final SourcePort sourcePort;
    private final MBoxUtils mboxUtils;

    @Override
    public Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO) {
        throw new UnsupportedOperationException("Unimplemented method 'upload'");
    }

    private List<EmailMessage> extractData(byte[] file) {
        List<EmailMessage> emailMessages = mboxUtils.parse(file);

        List<EmailMessage> messagesDTOList = emailMessages
            .stream()
            .map(emailMessage ->
                new EmailMessage(emailDto.getTime(), emailDto.getSender(), emailDto.getRecipients(), emailDto.getContent(), SourceType.EMAIL)
            )
            .collect(Collectors.toList());

        LOG.debug("Parsed {} email messages from MBOX file", messagesDTOList.size());

        return messageService
            .saveAll(messagesDTOList)
            .flatMap(savedMessages -> {
                // Extract all message IDs
                List<String> messageIds = savedMessages.stream().map(MessageDTO::getId).collect(Collectors.toList());

                // Set required fields on sourceDTO
                SourceDTO sourceDTO = new SourceDTO(Instant.now(), messageIds, SourceType.EMAIL, bytes, null);

                // Generate sourceId and save the source
                return sourceDTO
                    .withGeneratedIds()
                    .flatMap(updatedSource -> {
                        LOG.debug("Saving source with message IDs: {}", messageIds);
                        return sourceService.save(updatedSource);
                    });
            })
            .then();
    }

    @Override
    public Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}
