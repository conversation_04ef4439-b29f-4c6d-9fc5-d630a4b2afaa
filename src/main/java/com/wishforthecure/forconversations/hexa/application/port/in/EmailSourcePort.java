package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;
import reactor.core.publisher.Mono;

public interface EmailSourcePort {
    Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO);
    Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO);
}
