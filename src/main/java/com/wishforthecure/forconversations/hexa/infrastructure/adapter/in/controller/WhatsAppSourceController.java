package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller;

import com.wishforthecure.forconversations.hexa.application.port.in.WhatsAppSourcePort;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import reactor.core.publisher.Mono;

public class WhatsAppSourceController {

    private static final Logger LOG = LoggerFactory.getLogger(WhatsAppSourceController.class);
    private final WhatsAppSourcePort whatsAppSourcePort;

    public WhatsAppSourceController(WhatsAppSourcePort whatsAppSourcePort) {
        this.whatsAppSourcePort = whatsAppSourcePort;
    }

    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO) {
        LOG.info("REST request to initiate chat data upload");
        return whatsAppSourcePort.upload(whatsAppSourceUploadDTO);
    }

    @PostMapping(value = "/save")
    public Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO) {
        LOG.info("REST request to initiate chat data upload");
        return whatsAppSourcePort.save(whatsAppSourceSaveDTO);
    }
}
