package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.User;

/**
 * Interface for mail service.
 */
public interface IMailService {
    void sendEmail(String to, String subject, String content, boolean isMultipart, boolean isHtml);

    void sendEmailFromTemplate(User user, String templateName, String titleKey);

    void sendActivationEmail(User user);

    void sendCreationEmail(User user);

    void sendPasswordResetMail(User user);

    void sendUserRegistrationNotificationToAdmin(User user);
}
