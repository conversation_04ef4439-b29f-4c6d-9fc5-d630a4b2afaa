package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.Source;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing {@link Source}.
 */
public interface SourceService {
    /**
     * Save a source.
     *
     * @param sourceDTO the entity to save.
     * @return the persisted entity.
     */
    Mono<SourceDTO> save(SourceDTO sourceDTO);

    /**
     * Updates a source.
     *
     * @param sourceDTO the entity to update.
     * @return the persisted entity.
     */
    Mono<SourceDTO> update(SourceDTO sourceDTO);

    /**
     * Partially updates a source.
     *
     * @param sourceDTO the entity to update partially.
     * @return the persisted entity.
     */
    Mono<SourceDTO> partialUpdate(SourceDTO sourceDTO);

    /**
     * Get all the sources.
     *
     * @return the list of entities.
     */
    Flux<SourceDTO> findAll();

    /**
     * Returns the number of sources available.
     * @return the number of entities in the database.
     *
     */
    Mono<Long> countAll();

    /**
     * Returns the number of sources available in search repository.
     *
     */
    Mono<Long> searchCount();

    /**
     * Get the "id" source.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Mono<SourceDTO> findOne(String id);

    /**
     * Delete the "id" source.
     *
     * @param id the id of the entity.
     * @return a Mono to signal the deletion
     */
    Mono<Void> delete(String id);

    /**
     * Search for the source corresponding to the query.
     *
     * @param query the query of the search.
     * @return the list of entities.
     */
    Flux<SourceDTO> search(String query);
}
