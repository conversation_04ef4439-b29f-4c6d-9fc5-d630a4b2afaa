package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.Conversation;
import com.wishforthecure.forconversations.repository.ConversationRepository;
import com.wishforthecure.forconversations.service.ConversationService;
import com.wishforthecure.forconversations.service.dto.ConversationDTO;
import com.wishforthecure.forconversations.service.mapper.ConversationMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing {@link Conversation}.
 */
@Service
public class ConversationServiceImpl implements ConversationService {

    private static final Logger LOG = LoggerFactory.getLogger(ConversationServiceImpl.class);

    private final ConversationRepository conversationRepository;

    private final ConversationMapper conversationMapper;

    public ConversationServiceImpl(ConversationRepository conversationRepository, ConversationMapper conversationMapper) {
        this.conversationRepository = conversationRepository;
        this.conversationMapper = conversationMapper;
    }

    @Override
    public Mono<ConversationDTO> save(ConversationDTO conversationDTO) {
        LOG.debug("Request to save Conversation : {}", conversationDTO);
        return conversationRepository.save(conversationMapper.toEntity(conversationDTO)).map(conversationMapper::toDto);
    }

    @Override
    public Mono<ConversationDTO> update(ConversationDTO conversationDTO) {
        LOG.debug("Request to update Conversation : {}", conversationDTO);
        return conversationRepository.save(conversationMapper.toEntity(conversationDTO)).map(conversationMapper::toDto);
    }

    @Override
    public Mono<ConversationDTO> partialUpdate(ConversationDTO conversationDTO) {
        LOG.debug("Request to partially update Conversation : {}", conversationDTO);

        return conversationRepository
            .findById(conversationDTO.getId())
            .map(existingConversation -> {
                conversationMapper.partialUpdate(existingConversation, conversationDTO);
                return existingConversation;
            })
            .flatMap(conversationRepository::save)
            .map(conversationMapper::toDto);
    }

    @Override
    public Flux<ConversationDTO> findAll() {
        LOG.debug("Request to get all Conversations");
        return conversationRepository.findAll().map(conversationMapper::toDto);
    }

    @Override
    public Mono<ConversationDTO> findOne(String id) {
        LOG.debug("Request to get Conversation : {}", id);
        return conversationRepository.findById(id).map(conversationMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Conversation : {}", id);
        return conversationRepository.deleteById(id);
    }

    @Override
    public Flux<ConversationDTO> findByUserId(String userId) {
        LOG.debug("Request to get all Conversations for user: {}", userId);
        return conversationRepository.findByUserIdOrderByStartDesc(userId).map(conversationMapper::toDto);
    }

    @Override
    public Mono<ConversationDTO> createForUser(ConversationDTO conversationDTO, String userId) {
        LOG.debug("Request to create Conversation for user: {}", userId);
        conversationDTO.setUserId(userId);
        return save(conversationDTO);
    }
}
