package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.service.AudioMessageSourceService;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.dto.AudioMessageSourceResponseDto;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import com.wishforthecure.forconversations.service.infra.GoogleSpeechAdapter;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

@Service
public class AudioMessageSourceServiceImpl implements AudioMessageSourceService {

    private static final Logger LOG = LoggerFactory.getLogger(AudioMessageSourceServiceImpl.class);
    private static final String DUMMY_TEXT_TRASNSCRIPTION = "Es una texto de prueba";

    @Value("#{environment.acceptsProfiles('prod')}")
    private boolean isProduction;

    private final MessageService messageService;
    private final SourceService sourceService;
    private final GoogleSpeechAdapter googleSpeechAdapter;

    public AudioMessageSourceServiceImpl(
        MessageService messageService,
        SourceService sourceService,
        GoogleSpeechAdapter googleSpeechAdapter
    ) {
        this.messageService = messageService;
        this.sourceService = sourceService;
        this.googleSpeechAdapter = googleSpeechAdapter;
    }

    private static final Set<String> SUPPORTED_MIME_TYPES = new HashSet<>(
        Arrays.asList(
            "audio/wav",
            "audio/x-wav",
            "audio/vnd.wave", // Type added to support more WAV variants
            "audio/flac",
            "audio/mpeg",
            "audio/opus",
            "audio/ogg",
            "audio/webm",
            "application/ogg"
        )
    );

    @Override
    public Mono<AudioMessageSourceResponseDto> loadFile(byte[] audioBytes) {
        if (audioBytes == null || audioBytes.length == 0) {
            return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST, "Audio content cannot be null or empty."));
        }

        String mimeType = new Tika().detect(audioBytes);
        if (!SUPPORTED_MIME_TYPES.contains(mimeType)) {
            return Mono.error(new ResponseStatusException(HttpStatus.UNSUPPORTED_MEDIA_TYPE, "Unsupported audio format: " + mimeType));
        }
        LOG.info("Detected MIME Type: {}", mimeType);

        return Mono.fromCallable(() -> {
            String transcription;
            if (isProduction) {
                transcription = googleSpeechAdapter.transcribe(audioBytes);
            } else {
                transcription = DUMMY_TEXT_TRASNSCRIPTION;
                LOG.info("Non-production environment: using mock transcription.");
            }

            AudioMessageSourceResponseDto responseDto = new AudioMessageSourceResponseDto();
            responseDto.setText(transcription);
            responseDto.setTime(Instant.now());
            responseDto.setFile(audioBytes);
            responseDto.setFileContentType(mimeType);
            return responseDto;
        }).onErrorMap(e -> new RuntimeException("Error transcribing audio", e));
    }

    @Override
    public Mono<Void> save(AudioMessageSourceResponseDto audioMessageSourceResponseDto) {
        return Mono.just(audioMessageSourceResponseDto).flatMap(this::validateSaveRequest).flatMap(this::processSaveRequest).then();
    }

    private Mono<AudioMessageSourceResponseDto> validateSaveRequest(AudioMessageSourceResponseDto request) {
        if (request == null) {
            return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST, "Request body cannot be null."));
        }
        if (
            request.getAliasSender() == null || request.getAliasSender().getValue() == null || request.getAliasSender().getValue().isBlank()
        ) {
            return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST, "Sender alias cannot be null or empty."));
        }
        if (
            request.getAliasReceiver() == null ||
            request.getAliasReceiver().getValue() == null ||
            request.getAliasReceiver().getValue().isBlank()
        ) {
            return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST, "Receiver alias cannot be null or empty."));
        }
        return Mono.just(request);
    }

    private Mono<Void> processSaveRequest(AudioMessageSourceResponseDto request) {
        String senderValue = request.getAliasSender().getValue();
        String receiverValue = request.getAliasReceiver().getValue();

        MessageDTO messageDTO = new MessageDTO(request.getTime(), senderValue, receiverValue, request.getText(), SourceType.AUDIO);

        return messageService
            .save(messageDTO)
            .flatMap(savedMessage -> {
                SourceDTO sourceDTO = new SourceDTO(
                    request.getTime(),
                    List.of(savedMessage.getId()),
                    SourceType.AUDIO,
                    request.getFile(),
                    request.getFileContentType()
                );
                return sourceService.save(sourceDTO);
            })
            .then();
    }
}
