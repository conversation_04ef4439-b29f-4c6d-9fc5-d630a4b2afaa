package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.Participant;
import com.wishforthecure.forconversations.repository.ParticipantRepository;
import com.wishforthecure.forconversations.service.ParticipantService;
import com.wishforthecure.forconversations.service.dto.ParticipantDTO;
import com.wishforthecure.forconversations.service.mapper.ParticipantMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing
 * {@link Participant}.
 */
@Service
public class ParticipantServiceImpl implements ParticipantService {

    private static final Logger LOG = LoggerFactory.getLogger(ParticipantServiceImpl.class);

    private final ParticipantRepository participantRepository;

    private final ParticipantMapper participantMapper;

    public ParticipantServiceImpl(ParticipantRepository participantRepository, ParticipantMapper participantMapper) {
        this.participantRepository = participantRepository;
        this.participantMapper = participantMapper;
    }

    @Override
    public Mono<ParticipantDTO> save(ParticipantDTO participantDTO) {
        LOG.debug("Request to save Participant : {}", participantDTO);
        return participantRepository.save(participantMapper.toEntity(participantDTO)).map(participantMapper::toDto);
    }

    @Override
    public Mono<ParticipantDTO> update(ParticipantDTO participantDTO) {
        LOG.debug("Request to update Participant : {}", participantDTO);
        return participantRepository.save(participantMapper.toEntity(participantDTO)).map(participantMapper::toDto);
    }

    @Override
    public Mono<ParticipantDTO> partialUpdate(ParticipantDTO participantDTO) {
        LOG.debug("Request to partially update Participant : {}", participantDTO);

        return participantRepository
            .findById(participantDTO.getId())
            .map(existingParticipant -> {
                participantMapper.partialUpdate(existingParticipant, participantDTO);

                return existingParticipant;
            })
            .flatMap(participantRepository::save)
            .map(participantMapper::toDto);
    }

    @Override
    public Flux<ParticipantDTO> findAll() {
        LOG.debug("Request to get all Participants");
        return participantRepository.findAll().map(participantMapper::toDto);
    }

    public Mono<Long> countAll() {
        return participantRepository.count();
    }

    @Override
    public Mono<ParticipantDTO> findOne(String id) {
        LOG.debug("Request to get Participant : {}", id);
        return participantRepository.findById(id).map(participantMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Participant : {}", id);
        return participantRepository.deleteById(id);
    }

    @Override
    public Flux<ParticipantDTO> search(String query) {
        LOG.debug("Request to search Participants for query {}", query);
        // Participant entity no longer supports Elasticsearch search
        return Flux.empty();
    }

    @Override
    public Flux<ParticipantDTO> findByUserId(String userId) {
        LOG.debug("Request to get all Participants for user: {}", userId);
        return participantRepository.findByUserId(userId).map(participantMapper::toDto);
    }

    @Override
    public Flux<ParticipantDTO> searchByUserIdAndText(String userId, String searchText) {
        LOG.debug("Request to search Participants for user: {} with text: {}", userId, searchText);
        return participantRepository
            .findByUserIdAndNameContainingIgnoreCaseOrSurnameContainingIgnoreCase(userId, searchText, searchText)
            .map(participantMapper::toDto);
    }
}
