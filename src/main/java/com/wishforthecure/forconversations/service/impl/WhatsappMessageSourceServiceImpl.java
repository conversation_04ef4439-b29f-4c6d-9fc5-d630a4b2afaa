package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.WhatsappMessageSource;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.domain.enumeration.UploadStatus;
import com.wishforthecure.forconversations.repository.WhatsappMessageSourceRepository;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.WhatsappMessageSourceService;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import com.wishforthecure.forconversations.service.dto.WhatsAppConfirmUploadRequestDto;
import com.wishforthecure.forconversations.service.dto.WhatsAppInitiateUploadResponseDto;
import com.wishforthecure.forconversations.service.dto.WhatsAppUploadParseDTO;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

@Service
public class WhatsappMessageSourceServiceImpl implements WhatsappMessageSourceService {

    private static final Logger LOG = LoggerFactory.getLogger(WhatsappMessageSourceServiceImpl.class);

    private static final Pattern WHATSAPP_SENDER_PATTERN = Pattern.compile("^\\d{1,2}/\\d{1,2}/\\d{2,4}, \\d{1,2}:\\d{2}\\s*-\\s*([^:]+):");
    private static final Pattern WHATSAPP_MESSAGE_PATTERN = Pattern.compile(
        "(\\d{1,2}/\\d{1,2}/\\d{2}),\\s*(\\d{1,2}:\\d{2})\\s*-\\s*([^:]+):\\s*(.*)"
    );
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("d/M/yy, H:m").withZone(
        ZoneId.systemDefault()
    );

    private final MessageService messageService;
    private final SourceService sourceService;
    private final WhatsappMessageSourceRepository whatsappMessageSourceRepository;

    public WhatsappMessageSourceServiceImpl(
        MessageService messageService,
        SourceService sourceService,
        WhatsappMessageSourceRepository whatsappMessageSourceRepository
    ) {
        this.messageService = messageService;
        this.sourceService = sourceService;
        this.whatsappMessageSourceRepository = whatsappMessageSourceRepository;
    }

    @Override
    public Mono<WhatsAppInitiateUploadResponseDto> initiateUpload(byte[] bytes) {
        String namePersonOne = null;
        String namePersonTwo = null;

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(bytes), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Matcher matcher = WHATSAPP_SENDER_PATTERN.matcher(line);
                if (matcher.find()) {
                    String currentSender = matcher.group(1).trim();
                    if (namePersonOne == null) {
                        namePersonOne = currentSender;
                    } else if (namePersonTwo == null && !namePersonOne.equals(currentSender)) {
                        namePersonTwo = currentSender;
                    }
                }
                if (namePersonOne != null && namePersonTwo != null) {
                    break;
                }
            }
        } catch (Exception e) {
            LOG.error("Error during participant name extraction: {}", e.getMessage(), e);
            return Mono.error(
                new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to parse participant names: " + e.getMessage(), e)
            );
        }

        if (namePersonOne == null) {
            return Mono.error(
                new ResponseStatusException(HttpStatus.BAD_REQUEST, "The provided file is not a valid WhatsApp chat export.")
            );
        }

        WhatsappMessageSource source = new WhatsappMessageSource();
        source.setUploadId(UUID.randomUUID());
        source.setStatus(UploadStatus.PENDING_CONFIRMATION);
        source.setCreatedAt(Instant.now());
        source.setFile(bytes);
        source.setFileContentType("text/plain");
        source.setNamePersonOne(namePersonOne);
        source.setNamePersonTwo(namePersonTwo);

        return whatsappMessageSourceRepository
            .save(source)
            .map(savedSource -> {
                WhatsAppInitiateUploadResponseDto response = new WhatsAppInitiateUploadResponseDto();
                response.setUploadId(savedSource.getUploadId());
                response.setNamePersonOne(savedSource.getNamePersonOne());
                response.setNamePersonTwo(savedSource.getNamePersonTwo());
                return response;
            });
    }

    @Override
    public Mono<Void> confirmAndProcessUpload(WhatsAppConfirmUploadRequestDto confirmRequest) {
        return Mono.just(confirmRequest)
            .flatMap(this::validateConfirmRequest)
            .flatMap(req -> whatsappMessageSourceRepository.findByUploadId(req.getUploadId()))
            .switchIfEmpty(Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND, "Upload process not found.")))
            .flatMap(this::validateUploadStatus)
            .flatMap(provisionalSource -> processValidatedUpload(provisionalSource, confirmRequest))
            .then();
    }

    private Mono<WhatsAppConfirmUploadRequestDto> validateConfirmRequest(WhatsAppConfirmUploadRequestDto request) {
        if (
            request.getAliasPersonOne() == null ||
            request.getAliasPersonOne().getValue() == null ||
            request.getAliasPersonOne().getValue().isBlank()
        ) {
            return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST, "Alias for person one cannot be empty."));
        }
        if (
            request.getAliasPersonTwo() != null &&
            (request.getAliasPersonTwo().getValue() == null || request.getAliasPersonTwo().getValue().isBlank())
        ) {
            return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST, "Alias for person two, if provided, cannot be empty."));
        }
        return Mono.just(request);
    }

    private Mono<WhatsappMessageSource> validateUploadStatus(WhatsappMessageSource source) {
        if (source.getStatus() == UploadStatus.PENDING_CONFIRMATION) {
            return Mono.just(source);
        }

        if (source.getStatus() == UploadStatus.PROCESSED) {
            LOG.warn("Attempted to process an already processed upload with ID: {}", source.getUploadId());
            return Mono.empty();
        }

        return Mono.error(new ResponseStatusException(HttpStatus.BAD_REQUEST, "Upload is in an invalid state: " + source.getStatus()));
    }

    private Mono<Void> processValidatedUpload(WhatsappMessageSource provisionalSource, WhatsAppConfirmUploadRequestDto confirmRequest) {
        List<WhatsAppUploadParseDTO> parsedLines = parseChatContent(provisionalSource.getFile());

        if (parsedLines.isEmpty()) {
            return Mono.error(
                new ResponseStatusException(HttpStatus.BAD_REQUEST, "No valid messages could be parsed from the provided file.")
            );
        }

        List<MessageDTO> messageDTOList = createMessageDTOs(parsedLines, provisionalSource, confirmRequest);

        return saveMessagesAndSource(messageDTOList, provisionalSource).then(updateProvisionalSource(provisionalSource, confirmRequest));
    }

    private List<MessageDTO> createMessageDTOs(
        List<WhatsAppUploadParseDTO> parsedLines,
        WhatsappMessageSource source,
        WhatsAppConfirmUploadRequestDto confirmRequest
    ) {
        String namePersonOne = source.getNamePersonOne();
        AliasDTO aliasPersonOne = confirmRequest.getAliasPersonOne();
        AliasDTO aliasPersonTwo = confirmRequest.getAliasPersonTwo();

        List<MessageDTO> messageDTOList = new ArrayList<>();
        for (WhatsAppUploadParseDTO parsedLine : parsedLines) {
            String sender, recipient;
            if (namePersonOne.equals(parsedLine.getSender())) {
                sender = aliasPersonOne.getValue();
                recipient = aliasPersonTwo != null ? aliasPersonTwo.getValue() : "";
            } else {
                sender = aliasPersonTwo != null ? aliasPersonTwo.getValue() : "";
                recipient = aliasPersonOne.getValue();
            }
            // CORRECTED ARGUMENT ORDER
            MessageDTO messageDTO = new MessageDTO(parsedLine.getTime(), sender, recipient, parsedLine.getContent(), SourceType.WHATSAPP);
            messageDTOList.add(messageDTO);
        }
        return messageDTOList;
    }

    private Mono<Void> saveMessagesAndSource(List<MessageDTO> messageDTOList, WhatsappMessageSource provisionalSource) {
        return messageService
            .saveAll(messageDTOList)
            .flatMap(savedMessages -> {
                List<String> messageIds = savedMessages.stream().map(MessageDTO::getId).collect(Collectors.toList());
                SourceDTO sourceDTO = new SourceDTO(
                    Instant.now(),
                    messageIds,
                    SourceType.WHATSAPP,
                    provisionalSource.getFile(),
                    provisionalSource.getFileContentType()
                );
                return sourceService.save(sourceDTO);
            })
            .then();
    }

    private Mono<Void> updateProvisionalSource(WhatsappMessageSource provisionalSource, WhatsAppConfirmUploadRequestDto confirmRequest) {
        provisionalSource.setStatus(UploadStatus.PROCESSED);
        provisionalSource.setAliasPersonOne(confirmRequest.getAliasPersonOne().getValue());
        if (confirmRequest.getAliasPersonTwo() != null) {
            provisionalSource.setAliasPersonTwo(confirmRequest.getAliasPersonTwo().getValue());
        }
        provisionalSource.setFile(null);
        return whatsappMessageSourceRepository.save(provisionalSource).then();
    }

    private List<WhatsAppUploadParseDTO> parseChatContent(byte[] bytes) {
        if (bytes == null) {
            return Collections.emptyList();
        }
        List<WhatsAppUploadParseDTO> parsedLines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(bytes), StandardCharsets.UTF_8))) {
            String line;
            StringBuilder currentMessageContent = new StringBuilder();
            WhatsAppUploadParseDTO currentMessage = null;
            int lineNumber = 0;

            while ((line = reader.readLine()) != null) {
                Matcher matcher = WHATSAPP_MESSAGE_PATTERN.matcher(line);
                if (matcher.matches()) {
                    if (currentMessage != null) {
                        currentMessage.setContent(currentMessageContent.toString().trim());
                        parsedLines.add(currentMessage);
                    }
                    try {
                        LocalDateTime ldt = LocalDateTime.parse(matcher.group(1) + ", " + matcher.group(2), DATE_TIME_FORMATTER);
                        Instant instant = ldt.atZone(ZoneId.systemDefault()).toInstant();
                        currentMessage = WhatsAppUploadParseDTO.of(instant, matcher.group(3), "", ++lineNumber);
                        currentMessageContent = new StringBuilder(matcher.group(4));
                    } catch (DateTimeParseException e) {
                        LOG.warn("Could not parse date-time for line, skipping: {}", line);
                        currentMessage = null;
                    }
                } else if (currentMessage != null) {
                    currentMessageContent.append("\n").append(line);
                }
            }
            if (currentMessage != null) {
                currentMessage.setContent(currentMessageContent.toString().trim());
                parsedLines.add(currentMessage);
            }
        } catch (Exception e) {
            LOG.error("Failed to parse full chat content: {}", e.getMessage(), e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to parse chat file: " + e.getMessage(), e);
        }
        return parsedLines;
    }
}
