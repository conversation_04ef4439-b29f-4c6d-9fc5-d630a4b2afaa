package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.Alias;
import com.wishforthecure.forconversations.domain.enumeration.AliasType;
import com.wishforthecure.forconversations.repository.AliasRepository;
import com.wishforthecure.forconversations.service.AliasService;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import com.wishforthecure.forconversations.service.mapper.AliasMapper;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing
 * {@link Alias}.
 */
@Service
public class AliasServiceImpl implements AliasService {

    private static final Logger LOG = LoggerFactory.getLogger(AliasServiceImpl.class);

    private final AliasRepository aliasRepository;

    private final AliasMapper aliasMapper;

    public AliasServiceImpl(AliasRepository aliasRepository, AliasMapper aliasMapper) {
        this.aliasRepository = aliasRepository;
        this.aliasMapper = aliasMapper;
    }

    @Override
    public Mono<AliasDTO> save(AliasDTO aliasDTO) {
        LOG.debug("Request to save Alias : {}", aliasDTO);
        return aliasRepository.save(aliasMapper.toEntity(aliasDTO)).map(aliasMapper::toDto);
    }

    @Override
    public Mono<AliasDTO> update(AliasDTO aliasDTO) {
        LOG.debug("Request to update Alias : {}", aliasDTO);
        return aliasRepository.save(aliasMapper.toEntity(aliasDTO)).map(aliasMapper::toDto);
    }

    @Override
    public Mono<AliasDTO> partialUpdate(AliasDTO aliasDTO) {
        LOG.debug("Request to partially update Alias : {}", aliasDTO);

        return aliasRepository
            .findById(aliasDTO.getId())
            .map(existingAlias -> {
                aliasMapper.partialUpdate(existingAlias, aliasDTO);
                return existingAlias;
            })
            .flatMap(aliasRepository::save)
            .map(aliasMapper::toDto);
    }

    @Override
    public Flux<AliasDTO> findAll() {
        LOG.debug("Request to get all Aliases");
        return aliasRepository.findAll().map(aliasMapper::toDto);
    }

    public Mono<Long> countAll() {
        return aliasRepository.count();
    }

    public Mono<Long> searchCount() {
        LOG.debug("Request to count searchable Aliases");
        return Mono.just(0L);
    }

    @Override
    public Mono<AliasDTO> findOne(String id) {
        LOG.debug("Request to get Alias : {}", id);
        return aliasRepository.findById(id).map(aliasMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Alias : {}", id);
        return aliasRepository.deleteById(id);
    }

    @Override
    public Flux<AliasDTO> search(String query) {
        LOG.debug("Request to search Aliases for query {}", query);
        // Alias entity no longer supports Elasticsearch search
        return Flux.empty();
    }

    @Override
    public Mono<Map<String, Alias>> findOrCreateAliases(Set<String> values, AliasType type) {
        if (values == null || values.isEmpty()) {
            return Mono.just(Map.of());
        }

        return aliasRepository
            .findByValueIn(values)
            .collectMap(Alias::getValue)
            .flatMap(existingAliasesMap -> {
                Set<String> newAliasValues = values
                    .stream()
                    .filter(value -> !existingAliasesMap.containsKey(value))
                    .collect(Collectors.toSet());

                if (newAliasValues.isEmpty()) {
                    return Mono.just(existingAliasesMap);
                }

                List<Alias> newAliases = newAliasValues
                    .stream()
                    .map(value -> {
                        Alias newAlias = new Alias();
                        newAlias.setValue(value);
                        newAlias.setType(type);
                        return newAlias;
                    })
                    .collect(Collectors.toList());

                return aliasRepository
                    .saveAll(newAliases)
                    .collectMap(Alias::getValue)
                    .map(newlyCreatedAliasesMap -> {
                        existingAliasesMap.putAll(newlyCreatedAliasesMap);
                        return existingAliasesMap;
                    });
            });
    }

    @Override
    public Flux<AliasDTO> findByUserId(String userId) {
        LOG.debug("Request to get all Aliases for user: {}", userId);
        return aliasRepository.findByUserId(userId).map(aliasMapper::toDto);
    }

    @Override
    public Flux<AliasDTO> searchByUserIdAndValue(String userId, String value) {
        LOG.debug("Request to search Aliases for user: {} with value: {}", userId, value);
        return aliasRepository.findByUserIdAndValueContainingIgnoreCase(userId, value).map(aliasMapper::toDto);
    }
}
