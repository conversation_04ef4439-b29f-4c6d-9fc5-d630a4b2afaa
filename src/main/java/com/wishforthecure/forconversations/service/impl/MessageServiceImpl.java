package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.Message;
import com.wishforthecure.forconversations.repository.MessageRepository;
import com.wishforthecure.forconversations.repository.search.MessageSearchRepository;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.dto.DashboardFilterDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.mapper.MessageMapper;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing
 * {@link Message}.
 * <p>
 * This service coordinates domain operations and manages transactions.
 * It belongs to the Application Layer in a layered architecture.
 */
@Service
public class MessageServiceImpl implements MessageService {

    private static final Logger LOG = LoggerFactory.getLogger(MessageServiceImpl.class);

    private final MessageRepository messageRepository;

    private final MessageMapper messageMapper;

    private final MessageSearchRepository messageSearchRepository;

    private final ReactiveMongoTemplate mongoTemplate;

    public MessageServiceImpl(
        MessageRepository messageRepository,
        MessageMapper messageMapper,
        @Autowired(required = false) MessageSearchRepository messageSearchRepository,
        ReactiveMongoTemplate mongoTemplate
    ) {
        this.messageRepository = messageRepository;
        this.messageMapper = messageMapper;
        this.messageSearchRepository = messageSearchRepository;
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public Mono<MessageDTO> save(MessageDTO messageDTO) {
        LOG.debug("Request to save Message : {}", messageDTO);
        return messageRepository
            .save(messageMapper.toEntity(messageDTO))
            .flatMap(savedEntity -> {
                if (messageSearchRepository != null) {
                    return messageSearchRepository.save(savedEntity);
                }
                return Mono.just(savedEntity);
            })
            .map(messageMapper::toDto);
    }

    @Override
    public Mono<List<MessageDTO>> saveAll(List<MessageDTO> messageDTOList) {
        LOG.debug("Request to save Message : {}", messageDTOList);
        return messageRepository
            .saveAll(messageMapper.toEntity(messageDTOList))
            .collectList()
            .flatMap(entities -> {
                if (messageSearchRepository != null) {
                    return messageSearchRepository.saveAll(entities).then(Mono.just(entities));
                }
                return Mono.just(entities);
            })
            .map(messageMapper::toDto);
    }

    @Override
    public Mono<MessageDTO> update(MessageDTO messageDTO) {
        LOG.debug("Request to update Message : {}", messageDTO);
        return messageRepository
            .save(messageMapper.toEntity(messageDTO))
            .flatMap(savedEntity -> {
                if (messageSearchRepository != null) {
                    return messageSearchRepository.save(savedEntity);
                }
                return Mono.just(savedEntity);
            })
            .map(messageMapper::toDto);
    }

    @Override
    public Mono<MessageDTO> partialUpdate(MessageDTO messageDTO) {
        LOG.debug("Request to partially update Message : {}", messageDTO);

        return messageRepository
            .findById(messageDTO.getId())
            .map(existingMessage -> {
                messageMapper.partialUpdate(existingMessage, messageDTO);

                return existingMessage;
            })
            .flatMap(messageRepository::save)
            .flatMap(savedMessage -> {
                if (messageSearchRepository != null) {
                    messageSearchRepository.save(savedMessage);
                }
                return Mono.just(savedMessage);
            })
            .map(messageMapper::toDto);
    }

    @Override
    public Flux<MessageDTO> findAll() {
        LOG.debug("Request to get all Messages");
        return messageRepository.findAll().map(messageMapper::toDto);
    }

    public Mono<Long> countAll() {
        return messageRepository.count();
    }

    public Mono<Long> searchCount() {
        if (messageSearchRepository != null) {
            return messageSearchRepository.count();
        }
        return Mono.just(0L);
    }

    @Override
    public Mono<MessageDTO> findOne(String id) {
        LOG.debug("Request to get Message : {}", id);
        return messageRepository.findById(id).map(messageMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Message : {}", id);
        return messageRepository
            .deleteById(id)
            .then(messageSearchRepository != null ? messageSearchRepository.deleteById(id) : Mono.empty());
    }

    @Override
    public Flux<MessageDTO> search(String query) {
        LOG.debug("Request to search Messages for query {}", query);
        if (messageSearchRepository != null) {
            try {
                return messageSearchRepository.search(query).map(messageMapper::toDto);
            } catch (RuntimeException e) {
                LOG.warn("Elasticsearch search failed, returning empty results: {}", e.getMessage());
                return Flux.empty();
            }
        }
        LOG.warn("Elasticsearch not available, returning empty search results");
        return Flux.empty();
    }

    @Override
    public Flux<MessageDTO> searchByFilter(DashboardFilterDTO filter) {
        LOG.debug("Request to search Messages by filter: {}", filter);

        Query query = new Query();

        // Content filter (case-insensitive)
        if (filter.getContent() != null && !filter.getContent().trim().isEmpty()) {
            query.addCriteria(Criteria.where("content").regex(filter.getContent(), "i"));
        }

        // Date range filter
        if (filter.getStartTime() != null && filter.getEndTime() != null) {
            query.addCriteria(Criteria.where("time").gte(filter.getStartTime()).lte(filter.getEndTime()));
        } else if (filter.getStartTime() != null) {
            query.addCriteria(Criteria.where("time").gte(filter.getStartTime()));
        } else if (filter.getEndTime() != null) {
            query.addCriteria(Criteria.where("time").lte(filter.getEndTime()));
        }

        // Tags filter (if tags are stored as a field in Message entity)
        if (filter.getTags() != null && !filter.getTags().isEmpty()) {
            query.addCriteria(Criteria.where("tags").in(filter.getTags()));
        }

        // Feelings filter (if feelings are stored as a field in Message entity)
        if (filter.getFeelings() != null && !filter.getFeelings().isEmpty()) {
            query.addCriteria(Criteria.where("feelings").in(filter.getFeelings()));
        }

        return mongoTemplate.find(query, Message.class).map(messageMapper::toDto);
    }
}
