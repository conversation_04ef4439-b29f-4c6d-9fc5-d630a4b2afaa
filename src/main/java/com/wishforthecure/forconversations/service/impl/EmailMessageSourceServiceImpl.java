package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.EmailMessageSource;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.service.EmailMessageSourceService;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.dto.EmailMessageDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import com.wishforthecure.forconversations.service.infra.MBoxAdapter;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing
 * {@link EmailMessageSource}.
 */
@Service
public class EmailMessageSourceServiceImpl implements EmailMessageSourceService {

    private static final Logger LOG = LoggerFactory.getLogger(EmailMessageSourceServiceImpl.class);

    private final MessageService messageService;
    private final SourceService sourceService;
    private final MBoxAdapter mboxAdapter;

    public EmailMessageSourceServiceImpl(MessageService messageService, SourceService sourceService, MBoxAdapter mboxAdapter) {
        this.messageService = messageService;
        this.sourceService = sourceService;
        this.mboxAdapter = mboxAdapter;
    }

    @Override
    public Mono<Void> loadFile(byte[] bytes) {
        // Parse MBOX file using MBoxAdapter
        List<EmailMessageDTO> emailMessages = mboxAdapter.parse(bytes);

        // Convert EmailMessageDTO to MessageDTO
        List<MessageDTO> messagesDTOList = emailMessages
            .stream()
            .map(emailDto ->
                new MessageDTO(emailDto.getTime(), emailDto.getSender(), emailDto.getRecipients(), emailDto.getContent(), SourceType.EMAIL)
            )
            .collect(Collectors.toList());

        LOG.debug("Parsed {} email messages from MBOX file", messagesDTOList.size());

        return messageService
            .saveAll(messagesDTOList)
            .flatMap(savedMessages -> {
                // Extract all message IDs
                List<String> messageIds = savedMessages.stream().map(MessageDTO::getId).collect(Collectors.toList());

                // Set required fields on sourceDTO
                SourceDTO sourceDTO = new SourceDTO(Instant.now(), messageIds, SourceType.EMAIL, bytes, null);

                // Generate sourceId and save the source
                return sourceDTO
                    .withGeneratedIds()
                    .flatMap(updatedSource -> {
                        LOG.debug("Saving source with message IDs: {}", messageIds);
                        return sourceService.save(updatedSource);
                    });
            })
            .then();
    }
}
