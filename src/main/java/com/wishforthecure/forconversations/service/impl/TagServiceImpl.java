package com.wishforthecure.forconversations.service.impl;

import com.wishforthecure.forconversations.domain.Tag;
import com.wishforthecure.forconversations.repository.TagRepository;
import com.wishforthecure.forconversations.service.TagService;
import com.wishforthecure.forconversations.service.dto.TagDTO;
import com.wishforthecure.forconversations.service.mapper.TagMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Implementation for managing
 * {@link Tag}.
 */
@Service
public class TagServiceImpl implements TagService {

    private static final Logger LOG = LoggerFactory.getLogger(TagServiceImpl.class);

    private final TagRepository tagRepository;

    private final TagMapper tagMapper;

    public TagServiceImpl(TagRepository tagRepository, TagMapper tagMapper) {
        this.tagRepository = tagRepository;
        this.tagMapper = tagMapper;
    }

    @Override
    public Mono<TagDTO> save(TagDTO tagDTO) {
        LOG.debug("Request to save Tag : {}", tagDTO);
        return tagRepository.save(tagMapper.toEntity(tagDTO)).map(tagMapper::toDto);
    }

    @Override
    public Mono<TagDTO> update(TagDTO tagDTO) {
        LOG.debug("Request to update Tag : {}", tagDTO);
        return tagRepository.save(tagMapper.toEntity(tagDTO)).map(tagMapper::toDto);
    }

    @Override
    public Mono<TagDTO> partialUpdate(TagDTO tagDTO) {
        LOG.debug("Request to partially update Tag : {}", tagDTO);

        return tagRepository
            .findById(tagDTO.getId())
            .map(existingTag -> {
                tagMapper.partialUpdate(existingTag, tagDTO);
                return existingTag;
            })
            .flatMap(tagRepository::save)
            .map(tagMapper::toDto);
    }

    @Override
    public Flux<TagDTO> findAll() {
        LOG.debug("Request to get all Tags");
        return tagRepository.findAll().map(tagMapper::toDto);
    }

    public Mono<Long> countAll() {
        return tagRepository.count();
    }

    @Override
    public Mono<TagDTO> findOne(String id) {
        LOG.debug("Request to get Tag : {}", id);
        return tagRepository.findById(id).map(tagMapper::toDto);
    }

    @Override
    public Mono<Void> delete(String id) {
        LOG.debug("Request to delete Tag : {}", id);
        return tagRepository.deleteById(id);
    }

    @Override
    public Flux<TagDTO> findByUserId(String userId) {
        LOG.debug("Request to get all Tags for user: {}", userId);
        return tagRepository.findByUserId(userId).map(tagMapper::toDto);
    }

    @Override
    public Flux<TagDTO> searchByUserIdAndValue(String userId, String value) {
        LOG.debug("Request to search Tags for user: {} with value: {}", userId, value);
        return tagRepository.findByUserIdAndValueContainingIgnoreCase(userId, value).map(tagMapper::toDto);
    }
}
