package com.wishforthecure.forconversations.service.infra;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.service.dto.EmailMessageDTO;
import jakarta.mail.Address;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.james.mime4j.mboxiterator.CharBufferWrapper;
import org.apache.james.mime4j.mboxiterator.MboxIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class MBoxAdapter {

    private static final Logger LOG = LoggerFactory.getLogger(MBoxAdapter.class);

    /**
     * Parsea un array de bytes de un fichero MBOX y lo convierte en una lista de
     * EmailMessageDTO.
     *
     * @param mboxBytes El contenido del fichero MBOX como un array de bytes.
     * @return Una lista de EmailMessageDTO.
     */
    public List<EmailMessageDTO> parse(byte[] mboxBytes) {
        List<EmailMessageDTO> emailList = new ArrayList<>();
        final Session session = Session.getDefaultInstance(new Properties());

        // Handle empty or null input
        if (mboxBytes == null || mboxBytes.length == 0) {
            LOG.debug("Empty or null MBOX content provided");
            return emailList;
        }

        LOG.debug("Processing MBOX file with {} bytes", mboxBytes.length);

        // Log first few lines to debug format issues
        String preview = new String(mboxBytes, StandardCharsets.UTF_8);
        String[] lines = preview.split("\n", 5);
        LOG.debug("First few lines of MBOX:");
        for (int i = 0; i < Math.min(lines.length, 3); i++) {
            LOG.debug("Line {}: '{}'", i + 1, lines[i]);
        }

        // Create a temporary file since MboxIterator works with files
        Path tempFile = null;
        try {
            tempFile = Files.createTempFile("mbox", ".tmp");
            try (OutputStream os = Files.newOutputStream(tempFile)) {
                os.write(mboxBytes);
            }

            LOG.debug("Created temporary file: {}", tempFile.toAbsolutePath());

            // MboxIterator se encarga de separar cada mensaje del fichero MBOX
            int messageCount = 0;
            for (final CharBufferWrapper messageCharBuffer : MboxIterator.fromFile(tempFile.toFile())
                .charset(StandardCharsets.UTF_8)
                .build()) {
                messageCount++;
                LOG.debug("Processing message #{}", messageCount);

                EmailMessageDTO dto = processMessage(messageCharBuffer, session);
                if (dto != null) {
                    emailList.add(dto);
                }
            }

            LOG.info("Successfully parsed {} messages from MBOX file", emailList.size());
        } catch (IllegalArgumentException e) {
            LOG.warn("Invalid MBOX format detected: {}", e.getMessage());
            LOG.debug("MBOX content preview (first 500 chars): {}", preview.length() > 500 ? preview.substring(0, 500) + "..." : preview);
            // Return empty list for invalid MBOX format instead of throwing exception
        } catch (IOException e) {
            LOG.error("Error al leer el stream del MBOX: {}", e.getMessage(), e);
        } finally {
            // Clean up temporary file
            if (tempFile != null && Files.exists(tempFile)) {
                try {
                    Files.delete(tempFile);
                } catch (IOException e) {
                    LOG.warn("Failed to delete temporary file: {}", tempFile.toAbsolutePath(), e);
                }
            }
        }

        return emailList;
    }

    /**
     * Procesa un mensaje individual del MBOX.
     */
    private EmailMessageDTO processMessage(CharBufferWrapper messageCharBuffer, Session session) {
        try {
            // Convertimos el buffer de cada mensaje a un InputStream para que Jakarta Mail lo procese
            String messageContent = messageCharBuffer.toString();
            InputStream messageInputStream = new ByteArrayInputStream(messageContent.getBytes(StandardCharsets.UTF_8));
            MimeMessage message = new MimeMessage(session, messageInputStream);

            EmailMessageDTO dto = new EmailMessageDTO();

            // ID del mensaje
            dto.setId(message.getMessageID());

            // Fecha y hora
            Date sentDate = message.getSentDate();
            dto.setTime(sentDate != null ? sentDate.toInstant() : Instant.now());

            // Remitente (Sender)
            Address[] fromAddresses = message.getFrom();
            if (fromAddresses != null && fromAddresses.length > 0) {
                dto.setSender(fromAddresses[0].toString());
            }

            // Destinatarios (Recipients), incluyendo To, Cc y Bcc
            dto.setRecipients(getAllRecipients(message));

            // Contenido (Asunto##_##Cuerpo)
            String subject = message.getSubject() != null ? message.getSubject() : "";
            String body = getTextFromMessage(message);
            dto.setContent(subject + "##_##" + body);

            // Tipo de fuente
            dto.setType(SourceType.EMAIL);

            return dto;
        } catch (MessagingException | IOException e) {
            LOG.warn("Error procesando un mensaje, se saltará: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extrae el contenido de texto plano de un mensaje, incluso si es multipart.
     */
    private String getTextFromMessage(Message message) throws MessagingException, IOException {
        if (message.isMimeType("text/plain")) {
            return message.getContent().toString();
        }
        if (message.isMimeType("multipart/*")) {
            return getTextFromMultipart((Multipart) message.getContent());
        }
        return "";
    }

    private String getTextFromMultipart(Multipart multipart) throws MessagingException, IOException {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < multipart.getCount(); i++) {
            Part bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/plain")) {
                // Devolvemos la primera parte de texto plano que encontremos
                return bodyPart.getContent().toString();
            } else if (bodyPart.isMimeType("text/html")) {
                // Podríamos añadir lógica para convertir HTML a texto si fuera necesario
            } else if (bodyPart.getContent() instanceof Multipart nestedMultipart) {
                // Búsqueda recursiva si hay partes anidadas
                String nestedText = getTextFromMultipart(nestedMultipart);
                if (!nestedText.isEmpty()) {
                    return nestedText;
                }
            }
        }
        return result.toString();
    }

    /**
     * Combina todos los destinatarios (To, Cc, Bcc) en un único String separado por
     * comas.
     */
    private String getAllRecipients(Message message) throws MessagingException {
        Stream<Address> to = safeStream(message.getRecipients(Message.RecipientType.TO));
        Stream<Address> cc = safeStream(message.getRecipients(Message.RecipientType.CC));
        Stream<Address> bcc = safeStream(message.getRecipients(Message.RecipientType.BCC));

        return Stream.concat(to, Stream.concat(cc, bcc)).map(Address::toString).collect(Collectors.joining(", "));
    }

    /**
     * Convierte un array (que puede ser nulo) en un Stream seguro.
     */
    private Stream<Address> safeStream(Address[] addresses) {
        return addresses == null ? Stream.empty() : Arrays.stream(addresses);
    }
}
