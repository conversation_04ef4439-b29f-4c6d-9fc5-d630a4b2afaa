package com.wishforthecure.forconversations.service.infra;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.speech.v2.AutoDetectDecodingConfig;
import com.google.cloud.speech.v2.RecognitionConfig;
import com.google.cloud.speech.v2.RecognitionFeatures;
import com.google.cloud.speech.v2.RecognizeRequest;
import com.google.cloud.speech.v2.RecognizeResponse;
import com.google.cloud.speech.v2.SpeechClient;
import com.google.cloud.speech.v2.SpeechSettings;
import com.google.protobuf.ByteString;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class GoogleSpeechAdapter {

    private static final Logger LOG = LoggerFactory.getLogger(GoogleSpeechAdapter.class);

    @Value("${application.google.speech.language-code:es-ES}")
    private String languageCode;

    @Value("${application.google.speech.credentials-path:}")
    private String credentialsPath;

    @Value("${application.google.speech.location:global}")
    private String location;

    @Value("${application.google.speech.project-id:}")
    private String projectId;

    public String transcribe(byte[] bytes) throws IOException {
        LOG.info("Transcribing audio with Google Cloud Speech V2 via Adapter...");
        try (SpeechClient speechClient = createSpeechClient()) {
            if (projectId == null || projectId.isBlank()) {
                throw new IllegalStateException("Project ID must be configured for Google Speech API");
            }

            String recognizerName = String.format("projects/%s/locations/%s/recognizers/_", projectId, location);
            ByteString audioBytes = ByteString.copyFrom(bytes);
            RecognitionFeatures features = RecognitionFeatures.newBuilder().setEnableAutomaticPunctuation(true).build();
            AutoDetectDecodingConfig autoDetectConfig = AutoDetectDecodingConfig.newBuilder().build();
            RecognitionConfig config = RecognitionConfig.newBuilder()
                .setAutoDecodingConfig(autoDetectConfig)
                .addLanguageCodes(languageCode)
                .setModel("latest_long")
                .setFeatures(features)
                .build();

            RecognizeRequest request = RecognizeRequest.newBuilder()
                .setRecognizer(recognizerName)
                .setConfig(config)
                .setContent(audioBytes)
                .build();

            RecognizeResponse response = speechClient.recognize(request);

            return response
                .getResultsList()
                .stream()
                .map(result -> result.getAlternativesList().isEmpty() ? "" : result.getAlternativesList().get(0).getTranscript())
                .filter(transcript -> !transcript.isEmpty())
                .collect(Collectors.joining(" "))
                .trim();
        } catch (Exception e) {
            LOG.error("Failed to transcribe with Google Cloud Speech V2: {}", e.getMessage(), e);
            throw new IOException("Error transcribing audio with Google Cloud Speech V2", e);
        }
    }

    private GoogleCredentials getCredentials() throws IOException {
        if (credentialsPath != null && !credentialsPath.trim().isEmpty()) {
            Path credentialsFile = Path.of(credentialsPath);
            if (Files.exists(credentialsFile)) {
                LOG.info("Loading Google Cloud credentials from: {}", credentialsPath);
                return GoogleCredentials.fromStream(Files.newInputStream(credentialsFile));
            } else {
                LOG.warn("Credentials file not found at: {}, falling back to default credentials", credentialsPath);
            }
        }
        LOG.info("Using Application Default Credentials for Google Cloud");
        return GoogleCredentials.getApplicationDefault();
    }

    private SpeechClient createSpeechClient() throws IOException {
        try {
            GoogleCredentials credentials = getCredentials();
            SpeechSettings settings = SpeechSettings.newBuilder().setCredentialsProvider(() -> credentials).build();
            return SpeechClient.create(settings);
        } catch (IOException e) {
            LOG.error("Failed to create Google Cloud Speech V2 client", e);
            throw new IOException("Failed to create Google Cloud Speech V2 client", e);
        }
    }
}
