package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.EmailMessage;
import com.wishforthecure.forconversations.service.dto.EmailMessageDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing {@link EmailMessage}.
 */
public interface EmailMessageService {
    /**
     * Save a emailMessage.
     *
     * @param emailMessageDTO the entity to save.
     * @return the persisted entity.
     */
    Mono<EmailMessageDTO> save(EmailMessageDTO emailMessageDTO);

    /**
     * Updates a emailMessage.
     *
     * @param emailMessageDTO the entity to update.
     * @return the persisted entity.
     */
    Mono<EmailMessageDTO> update(EmailMessageDTO emailMessageDTO);

    /**
     * Partially updates a emailMessage.
     *
     * @param emailMessageDTO the entity to update partially.
     * @return the persisted entity.
     */
    Mono<EmailMessageDTO> partialUpdate(EmailMessageDTO emailMessageDTO);

    /**
     * Get all the emailMessages.
     *
     * @return the list of entities.
     */
    Flux<EmailMessageDTO> findAll();

    /**
     * Returns the number of emailMessages available.
     * @return the number of entities in the database.
     *
     */
    Mono<Long> countAll();

    /**
     * Returns the number of emailMessages available in search repository.
     *
     */
    Mono<Long> searchCount();

    /**
     * Get the "id" emailMessage.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Mono<EmailMessageDTO> findOne(String id);

    /**
     * Delete the "id" emailMessage.
     *
     * @param id the id of the entity.
     * @return a Mono to signal the deletion
     */
    Mono<Void> delete(String id);

    /**
     * Search for the emailMessage corresponding to the query.
     *
     * @param query the query of the search.
     * @return the list of entities.
     */
    Flux<EmailMessageDTO> search(String query);
}
