package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.Message;
import com.wishforthecure.forconversations.service.dto.DashboardFilterDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import java.util.List;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing
 * {@link Message}.
 */
public interface MessageService {
    /**
     * Save a message.
     *
     * @param messageDTO the entity to save.
     * @return the persisted entity.
     */
    Mono<MessageDTO> save(MessageDTO messageDTO);

    Mono<List<MessageDTO>> saveAll(List<MessageDTO> messageDTOList);

    /**
     * Updates a message.
     *
     * @param messageDTO the entity to update.
     * @return the persisted entity.
     */
    Mono<MessageDTO> update(MessageDTO messageDTO);

    /**
     * Partially updates a message.
     *
     * @param messageDTO the entity to update partially.
     * @return the persisted entity.
     */
    Mono<MessageDTO> partialUpdate(MessageDTO messageDTO);

    /**
     * Get all the messages.
     *
     * @return the list of entities.
     */
    Flux<MessageDTO> findAll();

    /**
     * Returns the number of messages available.
     *
     * @return the number of entities in the database.
     *
     */
    Mono<Long> countAll();

    /**
     * Returns the number of messages available in search repository.
     *
     */
    Mono<Long> searchCount();

    /**
     * Get the "id" message.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Mono<MessageDTO> findOne(String id);

    /**
     * Delete the "id" message.
     *
     * @param id the id of the entity.
     * @return a Mono to signal the deletion
     */
    Mono<Void> delete(String id);

    /**
     * Search for the message corresponding to the query.
     *
     * @param query the query of the search.
     * @return the list of entities.
     */
    Flux<MessageDTO> search(String query);

    /**
     * Search for messages using dashboard filter criteria.
     *
     * @param filter the filter criteria.
     * @return the list of entities.
     */
    Flux<MessageDTO> searchByFilter(DashboardFilterDTO filter);
}
