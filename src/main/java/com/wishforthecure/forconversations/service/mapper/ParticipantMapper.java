package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.Participant;
import com.wishforthecure.forconversations.service.dto.ParticipantDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Participant} and its DTO {@link ParticipantDTO}.
 */
@Mapper(componentModel = "spring")
public interface ParticipantMapper extends EntityMapper<ParticipantDTO, Participant> {}
