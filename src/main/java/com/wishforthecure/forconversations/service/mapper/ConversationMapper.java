package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.Conversation;
import com.wishforthecure.forconversations.service.dto.ConversationDTO;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity {@link Conversation} and its DTO {@link ConversationDTO}.
 */
@Mapper(componentModel = "spring", uses = { MessageMapper.class, TagMapper.class })
public interface ConversationMapper extends EntityMapper<ConversationDTO, Conversation> {}
