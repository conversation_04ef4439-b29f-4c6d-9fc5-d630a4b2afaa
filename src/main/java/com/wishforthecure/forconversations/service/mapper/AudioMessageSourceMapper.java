package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.AudioMessageSource;
import com.wishforthecure.forconversations.service.dto.AudioMessageSourceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link AudioMessageSource} and its DTO {@link AudioMessageSourceDTO}.
 */
@Mapper(componentModel = "spring")
public interface AudioMessageSourceMapper extends EntityMapper<AudioMessageSourceDTO, AudioMessageSource> {}
