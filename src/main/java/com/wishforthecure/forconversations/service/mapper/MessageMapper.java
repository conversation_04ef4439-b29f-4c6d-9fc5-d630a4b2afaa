package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.Message;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Message} and its DTO {@link MessageDTO}.
 */
@Mapper(componentModel = "spring")
public interface MessageMapper extends EntityMapper<MessageDTO, Message> {}
