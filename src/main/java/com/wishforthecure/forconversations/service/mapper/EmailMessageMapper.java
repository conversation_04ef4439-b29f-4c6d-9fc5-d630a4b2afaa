package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.EmailMessage;
import com.wishforthecure.forconversations.domain.EmailMessageSource;
import com.wishforthecure.forconversations.service.dto.EmailMessageDTO;
import com.wishforthecure.forconversations.service.dto.EmailMessageSourceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link EmailMessage} and its DTO {@link EmailMessageDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmailMessageMapper extends EntityMapper<EmailMessageDTO, EmailMessage> {
    @Mapping(target = "emailMessageSource", source = "emailMessageSource", qualifiedByName = "emailMessageSourceId")
    EmailMessageDTO toDto(EmailMessage s);

    @Named("emailMessageSourceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmailMessageSourceDTO toDtoEmailMessageSourceId(EmailMessageSource emailMessageSource);
}
