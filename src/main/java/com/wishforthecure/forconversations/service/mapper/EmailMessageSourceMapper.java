package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.EmailMessageSource;
import com.wishforthecure.forconversations.service.dto.EmailMessageSourceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link EmailMessageSource} and its DTO {@link EmailMessageSourceDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmailMessageSourceMapper extends EntityMapper<EmailMessageSourceDTO, EmailMessageSource> {}
