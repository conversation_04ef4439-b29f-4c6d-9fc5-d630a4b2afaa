package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.Conversation;
import com.wishforthecure.forconversations.domain.Message;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.service.dto.ConversationDTO;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class CustomConversationMapper {

    private final TagMapper tagMapper;

    public CustomConversationMapper(TagMapper tagMapper) {
        this.tagMapper = tagMapper;
    }

    public Conversation toEntity(ConversationDTO dto) {
        if (dto == null) {
            return null;
        }

        Conversation conversation = new Conversation();
        conversation.setId(dto.getId());
        conversation.setName(dto.getName());
        conversation.setStart(dto.getStart());
        conversation.setEnd(dto.getEnd());
        conversation.setContext(dto.getContext());
        conversation.setFeelingList(dto.getFeelingList());
        conversation.setUserId(dto.getUserId());

        // Map tags
        if (dto.getTagList() != null) {
            conversation.setTagList(tagMapper.toEntity(dto.getTagList()));
        }

        // Map messages with default values for required fields
        if (dto.getMessageList() != null) {
            List<Message> messages = dto.getMessageList().stream().map(this::toMessageEntity).collect(Collectors.toList());
            conversation.setMessageList(messages);
        }

        return conversation;
    }

    public ConversationDTO toDto(Conversation entity) {
        if (entity == null) {
            return null;
        }

        ConversationDTO dto = new ConversationDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setStart(entity.getStart());
        dto.setEnd(entity.getEnd());
        dto.setContext(entity.getContext());
        dto.setFeelingList(entity.getFeelingList());
        dto.setUserId(entity.getUserId());

        // Map tags
        if (entity.getTagList() != null) {
            dto.setTagList(tagMapper.toDto(entity.getTagList()));
        }

        // Map messages
        if (entity.getMessageList() != null) {
            List<MessageDTO> messageDTOs = entity.getMessageList().stream().map(this::toMessageDto).collect(Collectors.toList());
            dto.setMessageList(messageDTOs);
        }

        return dto;
    }

    private Message toMessageEntity(MessageDTO dto) {
        if (dto == null) {
            return null;
        }

        Message message = new Message();
        message.setId(dto.getId());
        message.setContent(dto.getContent());
        message.setTime(dto.getTime());
        message.setSender(dto.getSender() != null ? dto.getSender() : "Sistema");

        // Handle recipient/recipients
        if (dto.getRecipients() != null) {
            message.setRecipients(dto.getRecipients());
        } else {
            message.setRecipients("Todos");
        }

        // Set default type if not provided
        message.setType(dto.getType() != null ? dto.getType() : SourceType.WHATSAPP);

        // Handle feeling
        if (dto.getFeelingList() != null) {
            message.setFeelingList(dto.getFeelingList());
        }

        return message;
    }

    private MessageDTO toMessageDto(Message entity) {
        if (entity == null) {
            return null;
        }

        MessageDTO dto = new MessageDTO();
        dto.setId(entity.getId());
        dto.setContent(entity.getContent());
        dto.setTime(entity.getTime());
        dto.setSender(entity.getSender());
        dto.setRecipients(entity.getRecipients());
        dto.setType(entity.getType());
        dto.setFeelingList(entity.getFeelingList());

        return dto;
    }
}
