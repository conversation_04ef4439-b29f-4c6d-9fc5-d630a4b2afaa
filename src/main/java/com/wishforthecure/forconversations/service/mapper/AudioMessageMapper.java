package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.AudioMessage;
import com.wishforthecure.forconversations.domain.AudioMessageSource;
import com.wishforthecure.forconversations.service.dto.AudioMessageDTO;
import com.wishforthecure.forconversations.service.dto.AudioMessageSourceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link AudioMessage} and its DTO {@link AudioMessageDTO}.
 */
@Mapper(componentModel = "spring")
public interface AudioMessageMapper extends EntityMapper<AudioMessageDTO, AudioMessage> {
    @Mapping(target = "audioMessageSource", source = "audioMessageSource", qualifiedByName = "audioMessageSourceId")
    AudioMessageDTO toDto(AudioMessage s);

    @Named("audioMessageSourceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    AudioMessageSourceDTO toDtoAudioMessageSourceId(AudioMessageSource audioMessageSource);
}
