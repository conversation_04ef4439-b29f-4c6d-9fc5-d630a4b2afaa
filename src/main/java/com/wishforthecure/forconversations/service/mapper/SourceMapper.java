package com.wishforthecure.forconversations.service.mapper;

import com.wishforthecure.forconversations.domain.Source;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Source} and its DTO {@link SourceDTO}.
 */
@Mapper(componentModel = "spring")
public interface SourceMapper extends EntityMapper<SourceDTO, Source> {
    @Override
    @Mapping(target = "messages", ignore = true) // Ignoramos el campo messages ya que no lo mapeamos directamente
    @Mapping(target = "messagesIds", source = "messagesIds") // Mapeamos los IDs de los mensajes
    Source toEntity(SourceDTO dto);

    @Override
    @Mapping(target = "messagesIds", source = "messagesIds") // Mapeamos los IDs de los mensajes
    SourceDTO toDto(Source entity);

    @Override
    @Mapping(target = "messages", ignore = true) // Ignoramos el campo messages en el partial update
    @Mapping(target = "messagesIds", source = "messagesIds", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget Source entity, SourceDTO dto);
}
