package com.wishforthecure.forconversations.service.dto;

import java.time.Instant;

public class EmailUploadParseDTO {

    Instant time;
    String sender;
    // mail1, mail2, mail3
    String receivers;
    String content;
    int lineNumber;

    public EmailUploadParseDTO(Instant time, String sender, String receivers, String content, int lineNumber) {
        this.time = time;
        this.sender = sender;
        this.receivers = receivers;
        this.content = content;
        this.lineNumber = lineNumber;
    }

    public static EmailUploadParseDTO of(Instant time, String sender, String receivers, String content, int lineNumber) {
        return new EmailUploadParseDTO(time, sender, receivers, content, lineNumber);
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getReceivers() {
        return receivers;
    }

    public void setReceivers(String receivers) {
        this.receivers = receivers;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(int lineNumber) {
        this.lineNumber = lineNumber;
    }
}
