package com.wishforthecure.forconversations.service.dto;

import java.time.Instant;

public class WhatsAppUploadParseDTO {

    Instant time;
    String sender;
    String content;
    int lineNumber;

    public WhatsAppUploadParseDTO(Instant time, String sender, String content, int lineNumber) {
        this.time = time;
        this.sender = sender;
        this.content = content;
        this.lineNumber = lineNumber;
    }

    public static WhatsAppUploadParseDTO of(Instant time, String sender, String content, int lineNumber) {
        return new WhatsAppUploadParseDTO(time, sender, content, lineNumber);
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(int lineNumber) {
        this.lineNumber = lineNumber;
    }
}
