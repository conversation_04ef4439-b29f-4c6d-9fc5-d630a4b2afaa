package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.AudioMessage;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link AudioMessage} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AudioMessageDTO implements Serializable {

    private String id;

    @NotNull(message = "must not be null")
    private Instant time;

    @NotNull(message = "must not be null")
    private String sender;

    @NotNull(message = "must not be null")
    private String recipient;

    @NotNull(message = "must not be null")
    private String content;

    @NotNull(message = "must not be null")
    private SourceType type;

    private AudioMessageSourceDTO audioMessageSource;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getrecipient() {
        return recipient;
    }

    public void setrecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public SourceType getType() {
        return type;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    public AudioMessageSourceDTO getAudioMessageSource() {
        return audioMessageSource;
    }

    public void setAudioMessageSource(AudioMessageSourceDTO audioMessageSource) {
        this.audioMessageSource = audioMessageSource;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AudioMessageDTO)) {
            return false;
        }

        AudioMessageDTO audioMessageDTO = (AudioMessageDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, audioMessageDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AudioMessageDTO{" +
            "id='" + getId() + "'" +
            ", time='" + getTime() + "'" +
            ", sender='" + getSender() + "'" +
            ", recipient='" + getrecipient() + "'" +
            ", content='" + getContent() + "'" +
            ", type='" + getType() + "'" +
            ", audioMessageSource=" + getAudioMessageSource() +
            "}";
    }
}
