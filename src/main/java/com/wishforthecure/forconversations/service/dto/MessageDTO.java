package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.Message;
import com.wishforthecure.forconversations.domain.enumeration.Feeling;
import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * A DTO for the {@link Message}
 * entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MessageDTO implements Serializable {

    private String id;

    @NotNull(message = "must not be null")
    private Instant time;

    @NotNull(message = "must not be null")
    private String sender;

    @NotNull(message = "must not be null")
    private String recipients;

    @NotNull(message = "must not be null")
    private String content;

    @NotNull(message = "must not be null")
    private SourceType type;

    private List<Feeling> feelingList;

    // Campos de auditoría
    private String createdBy;
    private Instant createdDate;
    private String lastModifiedBy;
    private Instant lastModifiedDate;

    // Constructor vacío
    public MessageDTO() {}

    // Constructor
    public MessageDTO(Instant time, String sender, String recipients, String content, SourceType type) {
        this.time = time;
        this.sender = sender;
        this.recipients = recipients;
        this.content = content;
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public SourceType getType() {
        return type;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    public List<Feeling> getFeelingList() {
        return feelingList;
    }

    public void setFeelingList(List<Feeling> feelingList) {
        this.feelingList = feelingList;
    }

    // Getters y setters para campos de auditoría
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Instant lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MessageDTO)) {
            return false;
        }

        MessageDTO messageDTO = (MessageDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, messageDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MessageDTO{" +
                "id='" + getId() + "'" +
                ", time='" + getTime() + "'" +
                ", sender='" + getSender() + "'" +
                ", recipients='" + getRecipients() + "'" +
                ", content='" + getContent() + "'" +
                ", type='" + getType() + "'" +
                ", feelingList='" + getFeelingList() + "'" +
                "}";
    }
}
