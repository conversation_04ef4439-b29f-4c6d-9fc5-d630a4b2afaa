package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import java.time.Instant;
import java.util.List;

/**
 * DTO for dashboard filter criteria
 */
public class DashboardFilterDTO {

    private String content;
    private Instant startTime;
    private Instant endTime;
    private List<String> tags;
    private List<String> feelings;

    public DashboardFilterDTO() {}

    public DashboardFilterDTO(
        String content,
        Instant startTime,
        Instant endTime,
        List<String> tags,
        List<String> feelings,
        String sender,
        String recipient,
        SourceType sourceType
    ) {
        this.content = content;
        this.startTime = startTime;
        this.endTime = endTime;
        this.tags = tags;
        this.feelings = feelings;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Instant getStartTime() {
        return startTime;
    }

    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }

    public Instant getEndTime() {
        return endTime;
    }

    public void setEndTime(Instant endTime) {
        this.endTime = endTime;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public List<String> getFeelings() {
        return feelings;
    }

    public void setFeelings(List<String> feelings) {
        this.feelings = feelings;
    }

    @Override
    public String toString() {
        return (
            "DashboardFilterDTO{" +
            "content='" +
            content +
            '\'' +
            ", startTime=" +
            startTime +
            ", endTime=" +
            endTime +
            ", tags=" +
            tags +
            ", feelings=" +
            feelings +
            '}'
        );
    }
}
