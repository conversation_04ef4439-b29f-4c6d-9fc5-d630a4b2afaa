package com.wishforthecure.forconversations.service.dto;

import jakarta.validation.constraints.NotNull;
import java.util.UUID;

public class WhatsAppConfirmUploadRequestDto {

    @NotNull
    private UUID uploadId;

    @NotNull
    private AliasDTO aliasPersonOne;

    private AliasDTO aliasPersonTwo;

    public UUID getUploadId() {
        return uploadId;
    }

    public void setUploadId(UUID uploadId) {
        this.uploadId = uploadId;
    }

    public AliasDTO getAliasPersonOne() {
        return aliasPersonOne;
    }

    public void setAliasPersonOne(AliasDTO aliasPersonOne) {
        this.aliasPersonOne = aliasPersonOne;
    }

    public AliasDTO getAliasPersonTwo() {
        return aliasPersonTwo;
    }

    public void setAliasPersonTwo(AliasDTO aliasPersonTwo) {
        this.aliasPersonTwo = aliasPersonTwo;
    }
}
