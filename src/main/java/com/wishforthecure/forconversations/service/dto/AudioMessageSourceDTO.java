package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.AudioMessageSource;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link AudioMessageSource} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AudioMessageSourceDTO implements Serializable {

    private String id;

    @NotNull(message = "must not be null")
    private Instant time;

    private byte[] file;

    private String fileContentType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public byte[] getFile() {
        return file;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFileContentType() {
        return fileContentType;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AudioMessageSourceDTO)) {
            return false;
        }

        AudioMessageSourceDTO audioMessageSourceDTO = (AudioMessageSourceDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, audioMessageSourceDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AudioMessageSourceDTO{" +
            "id='" + getId() + "'" +
            ", time='" + getTime() + "'" +
            ", file='" + getFile() + "'" +
            "}";
    }
}
