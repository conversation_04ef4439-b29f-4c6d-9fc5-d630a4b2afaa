package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.Tag;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link Tag}
 * entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TagDTO implements Serializable {

    private String id;

    private String value;

    private String userId;

    public TagDTO() {
        // Empty constructor needed for Jackson.
    }

    public TagDTO(String value) {
        this.value = value;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TagDTO)) {
            return false;
        }

        TagDTO tagDTO = (TagDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, tagDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TagDTO{" +
            "id='" + getId() + "'" +
            ", value='" + getValue() + "'" +
            ", userId='" + getUserId() + "'" +
            "}";
    }
}
