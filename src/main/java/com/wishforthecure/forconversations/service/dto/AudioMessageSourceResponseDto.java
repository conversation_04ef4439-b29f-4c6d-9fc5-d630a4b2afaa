package com.wishforthecure.forconversations.service.dto;

import java.time.Instant;

public class AudioMessageSourceResponseDto {

    private Instant time;
    private AliasDTO aliasSender;
    private AliasDTO aliasReceiver;
    private String text;
    private byte[] file;
    private String fileContentType;

    // Constructor vacío para la deserialización JSON
    public AudioMessageSourceResponseDto() {}

    public AudioMessageSourceResponseDto(
        Instant time,
        AliasDTO aliasSender,
        AliasDTO aliasReceiver,
        String text,
        byte[] file,
        String fileContentType
    ) {
        this.time = time;
        this.aliasSender = aliasSender;
        this.aliasReceiver = aliasReceiver;
        this.text = text;
        this.file = file;
        this.fileContentType = fileContentType;
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public AliasDTO getAliasSender() {
        return aliasSender;
    }

    public void setAliasSender(AliasDTO aliasSender) {
        this.aliasSender = aliasSender;
    }

    public AliasDTO getAliasReceiver() {
        return aliasReceiver;
    }

    public void setAliasReceiver(AliasDTO aliasReceiver) {
        this.aliasReceiver = aliasReceiver;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public byte[] getFile() {
        return file;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFileContentType() {
        return fileContentType;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    @Override
    public String toString() {
        return (
            "AudioMessageSourceResponseDto{" +
            "time=" +
            time +
            ", aliasSender=" +
            (aliasSender != null ? aliasSender.toString() : "null") +
            ", aliasReceiver=" +
            (aliasReceiver != null ? aliasReceiver.toString() : "null") +
            ", text='" +
            text +
            '\'' +
            ", fileContentType='" +
            fileContentType +
            '\'' +
            '}'
        );
    }
}
