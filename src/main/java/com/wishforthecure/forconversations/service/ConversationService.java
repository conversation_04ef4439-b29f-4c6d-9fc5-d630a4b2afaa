package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.Conversation;
import com.wishforthecure.forconversations.service.dto.ConversationDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing {@link Conversation}.
 */
public interface ConversationService {
    /**
     * Save a conversation.
     *
     * @param conversationDTO the entity to save.
     * @return the persisted entity.
     */
    Mono<ConversationDTO> save(ConversationDTO conversationDTO);

    /**
     * Updates a conversation.
     *
     * @param conversationDTO the entity to update.
     * @return the persisted entity.
     */
    Mono<ConversationDTO> update(ConversationDTO conversationDTO);

    /**
     * Partially updates a conversation.
     *
     * @param conversationDTO the entity to update partially.
     * @return the persisted entity.
     */
    Mono<ConversationDTO> partialUpdate(ConversationDTO conversationDTO);

    /**
     * Get all the conversations.
     *
     * @return the list of entities.
     */
    Flux<ConversationDTO> findAll();

    /**
     * Get the "id" conversation.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Mono<ConversationDTO> findOne(String id);

    /**
     * Delete the "id" conversation.
     *
     * @param id the id of the entity.
     * @return a Mono to signal the deletion
     */
    Mono<Void> delete(String id);

    /**
     * Get all conversations for the current user.
     *
     * @param userId the user ID.
     * @return the list of entities.
     */
    Flux<ConversationDTO> findByUserId(String userId);

    /**
     * Create a new conversation for the current user.
     *
     * @param conversationDTO the entity to create.
     * @param userId the user ID.
     * @return the persisted entity.
     */
    Mono<ConversationDTO> createForUser(ConversationDTO conversationDTO, String userId);
}
