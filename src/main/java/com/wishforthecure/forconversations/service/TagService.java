package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.Tag;
import com.wishforthecure.forconversations.service.dto.TagDTO;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing
 * {@link Tag}.
 */
public interface TagService {
    /**
     * Save a tag.
     *
     * @param tagDTO the entity to save.
     * @return the persisted entity.
     */
    Mono<TagDTO> save(TagDTO tagDTO);

    /**
     * Updates a tag.
     *
     * @param tagDTO the entity to update.
     * @return the persisted entity.
     */
    Mono<TagDTO> update(TagDTO tagDTO);

    /**
     * Partially updates a tag.
     *
     * @param tagDTO the entity to update partially.
     * @return the persisted entity.
     */
    Mono<TagDTO> partialUpdate(TagDTO tagDTO);

    /**
     * Get all the tags.
     *
     * @return the list of entities.
     */
    Flux<TagDTO> findAll();

    /**
     * Returns the number of tags available.
     *
     * @return the number of entities in the database.
     *
     */
    Mono<Long> countAll();

    /**
     * Get the "id" tag.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Mono<TagDTO> findOne(String id);

    /**
     * Delete the "id" tag.
     *
     * @param id the id of the entity.
     * @return a Mono to signal the deletion
     */
    Mono<Void> delete(String id);

    /**
     * Get all tags for the current user.
     *
     * @param userId the user ID.
     * @return the list of entities.
     */
    Flux<TagDTO> findByUserId(String userId);

    /**
     * Search tags for the current user by value.
     *
     * @param userId the user ID.
     * @param value the search value.
     * @return the list of entities.
     */
    Flux<TagDTO> searchByUserIdAndValue(String userId, String value);
}
