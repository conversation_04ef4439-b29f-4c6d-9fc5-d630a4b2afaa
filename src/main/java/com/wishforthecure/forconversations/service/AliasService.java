package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.Alias;
import com.wishforthecure.forconversations.domain.enumeration.AliasType;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import java.util.Map;
import java.util.Set;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Service Interface for managing
 * {@link Alias}.
 */
public interface AliasService {
    /**
     * Save a alias.
     *
     * @param aliasDTO the entity to save.
     * @return the persisted entity.
     */
    Mono<AliasDTO> save(AliasDTO aliasDTO);

    /**
     * Updates a alias.
     *
     * @param aliasDTO the entity to update.
     * @return the persisted entity.
     */
    Mono<AliasDTO> update(AliasDTO aliasDTO);

    /**
     * Partially updates a alias.
     *
     * @param aliasDTO the entity to update partially.
     * @return the persisted entity.
     */
    Mono<AliasDTO> partialUpdate(AliasDTO aliasDTO);

    /**
     * Get all the aliases.
     *
     * @return the list of entities.
     */
    Flux<AliasDTO> findAll();

    /**
     * Returns the number of aliases available.
     *
     * @return the number of entities in the database.
     *
     */
    Mono<Long> countAll();

    /**
     * Returns the number of aliases available in search repository.
     *
     */
    Mono<Long> searchCount();

    /**
     * Get the "id" alias.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Mono<AliasDTO> findOne(String id);

    /**
     * Delete the "id" alias.
     *
     * @param id the id of the entity.
     * @return a Mono to signal the deletion
     */
    Mono<Void> delete(String id);

    /**
     * Search for the alias corresponding to the query.
     *
     * @param query the query of the search.
     * @return the list of entities.
     */
    Flux<AliasDTO> search(String query);

    Mono<Map<String, Alias>> findOrCreateAliases(Set<String> values, AliasType type);

    /**
     * Get all aliases for the current user.
     *
     * @param userId the user ID.
     * @return the list of entities.
     */
    Flux<AliasDTO> findByUserId(String userId);

    /**
     * Search aliases for the current user by value.
     *
     * @param userId the user ID.
     * @param value the search value.
     * @return the list of entities.
     */
    Flux<AliasDTO> searchByUserIdAndValue(String userId, String value);
}
