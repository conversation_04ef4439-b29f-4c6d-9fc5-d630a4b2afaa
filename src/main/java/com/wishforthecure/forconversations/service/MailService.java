package com.wishforthecure.forconversations.service;

import com.wishforthecure.forconversations.domain.User;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.MessageSource;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import tech.jhipster.config.JHipsterProperties;

/**
 * Service for sending emails.
 * <p>
 * We use the {@link Async} annotation to send emails asynchronously.
 */
@Service
@ConditionalOnProperty(name = "application.feature.mail.enabled", havingValue = "true")
public class MailService implements IMailService {

    private static final Logger LOG = LoggerFactory.getLogger(MailService.class);

    private static final String USER = "user";
    private static final String BASE_URL = "baseUrl";

    private final JHipsterProperties jHipsterProperties;
    private final JavaMailSender javaMailSender;
    private final MessageSource messageSource;
    private final SpringTemplateEngine templateEngine;

    @Value("${application.feature.mail.enabled:false}")
    private boolean mailEnabled;

    @Value("${spring.profiles.active:}")
    private String activeProfiles;

    public MailService(
        JHipsterProperties jHipsterProperties,
        JavaMailSender javaMailSender,
        MessageSource messageSource,
        SpringTemplateEngine templateEngine
    ) {
        this.jHipsterProperties = jHipsterProperties;
        this.javaMailSender = javaMailSender;
        this.messageSource = messageSource;
        this.templateEngine = templateEngine;
    }

    @Async
    public void sendEmail(String to, String subject, String content, boolean isMultipart, boolean isHtml) {
        if (!mailEnabled) {
            LOG.debug("Mail service disabled - would send email to: {}, subject: {}", to, subject);
            return;
        }

        LOG.debug(
            "Send email[multipart '{}' and html '{}'] to '{}' with subject '{}' and content={}",
            isMultipart,
            isHtml,
            to,
            subject,
            content
        );

        // Prepare message using a Spring helper
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        try {
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, isMultipart, StandardCharsets.UTF_8.name());
            message.setTo(to);
            message.setFrom(jHipsterProperties.getMail().getFrom());
            message.setSubject(subject);
            message.setText(content, isHtml);
            javaMailSender.send(mimeMessage);
            LOG.debug("Sent email to User '{}'", to);
        } catch (MailException | MessagingException e) {
            LOG.warn("Email could not be sent to user '{}'", to, e);
        }
    }

    @Async
    public void sendEmailFromTemplate(User user, String templateName, String titleKey) {
        if (!mailEnabled) {
            LOG.debug("Mail service disabled - would send email template {} to: {}", templateName, user.getEmail());
            return;
        }

        if (user.getEmail() == null) {
            LOG.debug("Email doesn't exist for user '{}'", user.getLogin());
            return;
        }
        Locale locale = Locale.forLanguageTag(user.getLangKey());
        Context context = new Context(locale);
        context.setVariable(USER, user);
        context.setVariable(BASE_URL, jHipsterProperties.getMail().getBaseUrl());
        String content = templateEngine.process(templateName, context);
        String subject = messageSource.getMessage(titleKey, null, locale);
        sendEmail(user.getEmail(), subject, content, false, true);
    }

    @Async
    public void sendActivationEmail(User user) {
        LOG.debug("Sending activation email to '{}'", user.getEmail());
        sendEmailFromTemplate(user, "mail/activationEmail", "email.activation.title");
    }

    @Async
    public void sendCreationEmail(User user) {
        LOG.debug("Sending creation email to '{}'", user.getEmail());
        sendEmailFromTemplate(user, "mail/creationEmail", "email.activation.title");
    }

    @Async
    public void sendPasswordResetMail(User user) {
        LOG.debug("Sending password reset email to '{}'", user.getEmail());
        sendEmailFromTemplate(user, "mail/passwordResetEmail", "email.reset.title");
    }

    @Async
    public void sendUserRegistrationNotificationToAdmin(User user) {
        LOG.debug("Sending user registration notification to admin for user '{}'", user.getEmail());

        if (!mailEnabled) {
            LOG.debug("Mail service disabled - would send admin notification for user: {}", user.getEmail());
            return;
        }

        String adminEmail = "<EMAIL>";
        String subject = "Petición alta de \"" + user.getEmail() + "\"";

        // Add DEMO prefix if not in production
        if (!isProductionEnvironment()) {
            subject = "DEMO " + subject;
        }

        // Use template for HTML email
        Locale locale = Locale.forLanguageTag("es"); // Default to Spanish
        Context context = new Context(locale);
        context.setVariable(USER, user);
        context.setVariable(BASE_URL, jHipsterProperties.getMail().getBaseUrl());
        String content = templateEngine.process("mail/userRegistrationNotificationEmail", context);

        sendEmail(adminEmail, subject, content, false, true);
    }

    /**
     * Check if the application is running in production environment.
     */
    private boolean isProductionEnvironment() {
        return activeProfiles != null && activeProfiles.contains("prod");
    }
}

/**
 * Dummy Mail Service - emails are disabled.
 */
@Service
@ConditionalOnProperty(name = "application.feature.mail.enabled", havingValue = "false", matchIfMissing = true)
class DummyMailService implements IMailService {

    private static final Logger LOG = LoggerFactory.getLogger(DummyMailService.class);

    @Override
    public void sendEmail(String to, String subject, String content, boolean isMultipart, boolean isHtml) {
        LOG.debug("Mail service disabled - would send email to: {}, subject: {}", to, subject);
    }

    @Override
    public void sendEmailFromTemplate(User user, String templateName, String titleKey) {
        LOG.debug("Mail service disabled - would send email template {} to: {}", templateName, user.getEmail());
    }

    @Override
    public void sendActivationEmail(User user) {
        LOG.debug("Mail service disabled - would send activation email to '{}'", user.getEmail());
    }

    @Override
    public void sendCreationEmail(User user) {
        LOG.debug("Mail service disabled - would send creation email to '{}'", user.getEmail());
    }

    @Override
    public void sendPasswordResetMail(User user) {
        LOG.debug("Mail service disabled - would send password reset email to '{}'", user.getEmail());
    }

    @Override
    public void sendUserRegistrationNotificationToAdmin(User user) {
        LOG.debug("Mail service disabled - would send admin notification for user registration: '{}'", user.getEmail());
    }
}
