@if (user) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(user.login!)">
    <div class="modal-header">
      <h4 class="modal-title" jhiTranslate="entity.delete.title">Confirm delete operation</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error />

      <p jhiTranslate="userManagement.delete.question" [translateValues]="{ login: user.login }">
        Are you sure you want to delete user {{ user.login }}?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
      </button>

      <button type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Delete</span>
      </button>
    </div>
  </form>
}
