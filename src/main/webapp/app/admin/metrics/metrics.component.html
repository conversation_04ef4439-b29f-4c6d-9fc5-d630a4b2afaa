<div>
  <h2>
    <span id="metrics-page-heading" data-cy="metricsPageHeading" jhiTranslate="metrics.title">Application Metrics</span>

    <button class="btn btn-primary float-end" (click)="refresh()">
      <fa-icon icon="sync"></fa-icon> <span jhiTranslate="metrics.refresh.button">Refresh</span>
    </button>
  </h2>

  <h3 jhiTranslate="metrics.jvm.title">JVM Metrics</h3>

  @let metricsRef = metrics();
  @if (metricsRef && !updatingMetrics()) {
    <div class="row">
      <jhi-jvm-memory class="col-md-4" [updating]="updatingMetrics()" [jvmMemoryMetrics]="metricsRef.jvm" />

      <jhi-jvm-threads class="col-md-4" [threads]="threads()" />

      <jhi-metrics-system class="col-md-4" [updating]="updatingMetrics()" [systemMetrics]="metricsRef.processMetrics" />
    </div>
  }

  @if (metricsRef?.garbageCollector; as metricsRefGarbageCollector) {
    <jhi-metrics-garbagecollector [updating]="updatingMetrics()" [garbageCollectorMetrics]="metricsRefGarbageCollector" />
  }

  @if (updatingMetrics()) {
    <div class="well well-lg" jhiTranslate="metrics.updating">Updating...</div>
  }

  @if (metricsRef?.['http.server.requests']; as metricsRefHttpServerRequests) {
    <jhi-metrics-request [updating]="updatingMetrics()" [requestMetrics]="metricsRefHttpServerRequests" />
  }

  @if (metricsRef?.services; as metricsRefServices) {
    <jhi-metrics-endpoints-requests [updating]="updatingMetrics()" [endpointsRequestsMetrics]="metricsRefServices" />
  }

  @if (metricsRef?.cache; as metricsRefCache) {
    <jhi-metrics-cache [updating]="updatingMetrics()" [cacheMetrics]="metricsRefCache" />
  }

  @if (metricsRef && metricsKeyExistsAndObjectNotEmpty('databases')) {
    <jhi-metrics-datasource [updating]="updatingMetrics()" [datasourceMetrics]="metricsRef.databases" />
  }
</div>
