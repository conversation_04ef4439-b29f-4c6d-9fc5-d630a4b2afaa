@import 'bootstrap/scss/functions';
@import 'bootswatch/dist/journal/variables';
@import 'bootstrap/scss/variables';

/* ==========================================================================
Navbar
========================================================================== */

.navbar-version {
  font-size: 0.65em;
  color: $navbar-dark-color;
}

.profile-image {
  height: 1.75em;
  width: 1.75em;
}

.navbar {
  padding: 0.2rem 1rem;
  .dropdown-item.active,
  .dropdown-item.active:focus,
  .dropdown-item.active:hover {
    background-color: $dark;
  }

  a.nav-link {
    font-weight: 400;
  }

  .navbar-toggler {
    &:hover {
      color: $navbar-dark-hover-color;
    }
  }
}

/* ==========================================================================
Logo styles
========================================================================== */
.logo-img {
  height: 45px;
  width: 45px;
  display: inline-block;
  vertical-align: middle;
  background: url('/content/images/logo-jhipster.png') no-repeat center center;
  background-size: contain;
}
