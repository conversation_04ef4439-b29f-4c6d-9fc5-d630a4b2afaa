import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IWhatsappMessageSource } from '../model/whatsapp-message-source.model';
import { WhatsappMessageSourceService } from '../service/whatsapp-message-source.service';

const whatsappMessageSourceResolve = (route: ActivatedRouteSnapshot): Observable<IWhatsappMessageSource | null> => {
  const id = route.params['id'];
  if (id) {
    return inject(WhatsappMessageSourceService)
      .find(id)
      .pipe(
        mergeMap((whatsappMessageSource: HttpResponse<IWhatsappMessageSource>) => {
          if (whatsappMessageSource.body) {
            return of(whatsappMessageSource.body);
          } else {
            inject(Router).navigate(['404']);
            return EMPTY;
          }
        }),
      );
  }
  return of(null);
};

export default whatsappMessageSourceResolve;
