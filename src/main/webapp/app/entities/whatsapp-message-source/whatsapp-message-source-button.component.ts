import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { faWhatsapp } from '@fortawesome/free-brands-svg-icons';
import { faTimes, faUpload } from '@fortawesome/free-solid-svg-icons';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Account } from 'app/core/auth/account.model';
import { AccountService } from 'app/core/auth/account.service';
import { IAlias } from 'app/entities/alias/alias.model';
import { AliasService } from 'app/entities/alias/service/alias.service';
import { finalize, map, tap } from 'rxjs/operators';
import { IWhatsAppConfirmUploadRequestDto } from './model/whatsapp-confirm-upload-request.model';
import { IWhatsAppInitiateUploadResponseDto } from './model/whatsapp-initiate-upload-response.model';
import { WhatsappMessageSourceService } from './service/whatsapp-message-source.service';

@Component({
  selector: 'jhi-whatsapp-message-source-button',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FaIconComponent],
  templateUrl: './whatsapp-message-source-button.component.html',
})
export class WhatsappMessageSourceButtonComponent {
  @Output() uploadSuccess = new EventEmitter<void>();
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('content') modalContent!: ElementRef;

  account: Account | null = null;
  uploadForm!: FormGroup;
  faWhatsapp = faWhatsapp;
  faUpload = faUpload;
  faTimes = faTimes;

  isUploading = false;
  isSaving = false;
  showSuccessToast = false;
  showErrorToast = false;
  toastMessage = '';

  selectedFile: File | null = null;
  initiateResponse: IWhatsAppInitiateUploadResponseDto | null = null;
  availableAliases: IAlias[] = [];

  private modalRef: NgbModalRef | undefined;

  constructor(
    private modalService: NgbModal,
    private accountService: AccountService,
    private fb: FormBuilder,
    private aliasService: AliasService,
    private whatsappMessageSourceService: WhatsappMessageSourceService,
  ) {
    this.accountService.identity().subscribe(account => (this.account = account));
    // Initialize form without the second validator initially
    this.uploadForm = this.fb.group({
      personOne_aliasId: [null, Validators.required],
      personTwo_aliasId: [null], // Validator will be set dynamically
    });
  }

  openModal(): void {
    // Limpiar el estado antes de abrir el modal
    this.resetState();
    this.modalRef = this.modalService.open(this.modalContent, { size: 'lg', backdrop: 'static' });
    this.modalRef.closed.subscribe(() => this.resetState());
    this.modalRef.dismissed.subscribe(() => this.resetState());
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;

    this.selectedFile = input.files[0];
    this.isUploading = true;
    this.initiateResponse = null;

    this.whatsappMessageSourceService
      .initiateUpload(this.selectedFile)
      .pipe(
        tap(() => this.loadAliases()),
        finalize(() => (this.isUploading = false)),
      )
      .subscribe({
        next: response => {
          this.initiateResponse = response;
          // Dynamically set validator for personTwo
          const personTwoControl = this.uploadForm.get('personTwo_aliasId');
          if (personTwoControl) {
            if (response.namePersonTwo) {
              personTwoControl.setValidators(Validators.required);
            } else {
              personTwoControl.clearValidators();
            }
            personTwoControl.updateValueAndValidity();
          }
        },
        error: err => {
          this.toastMessage = 'Error al procesar el fichero.';
          this.showErrorToast = true;
        },
      });
  }

  onSave(): void {
    if (!this.uploadForm.valid || !this.initiateResponse?.uploadId) return;
    this.isSaving = true;

    const formValues = this.uploadForm.getRawValue();
    const aliasOne = this.availableAliases.find(a => a.id === formValues.personOne_aliasId);
    let aliasTwo: IAlias | null = null;

    // Only find aliasTwo if it's expected
    if (this.initiateResponse.namePersonTwo) {
      aliasTwo = this.availableAliases.find(a => a.id === formValues.personTwo_aliasId) ?? null;
    }

    // Validation check
    if (!aliasOne || (this.initiateResponse.namePersonTwo && !aliasTwo)) {
      this.isSaving = false;
      this.toastMessage = 'Uno o más alias seleccionados no son válidos.';
      this.showErrorToast = true;
      return;
    }

    const confirmRequest: IWhatsAppConfirmUploadRequestDto = {
      uploadId: this.initiateResponse.uploadId,
      aliasPersonOne: aliasOne,
      aliasPersonTwo: aliasTwo,
    };

    this.whatsappMessageSourceService
      .confirmUpload(confirmRequest)
      .pipe(finalize(() => (this.isSaving = false)))
      .subscribe({
        next: () => {
          this.toastMessage = '¡Conversación guardada correctamente!';
          this.showSuccessToast = true;
          this.uploadSuccess.emit();
          setTimeout(() => {
            if (this.modalRef) {
              this.modalRef.close();
            }
          }, 1500);
        },
        error: err => {
          this.toastMessage = 'Error al guardar la conversación.';
          this.showErrorToast = true;
        },
      });
  }

  triggerFileInput(): void {
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.click();
    }
  }

  onToastClosed(): void {
    this.showSuccessToast = false;
    this.showErrorToast = false;
  }

  private loadAliases(): void {
    this.aliasService
      .query()
      .pipe(map(res => res.body ?? []))
      .subscribe(aliases => (this.availableAliases = aliases));
  }

  private resetState(): void {
    this.uploadForm.reset();
    // Re-add the cleared validator to personTwo for the next upload
    this.uploadForm.get('personTwo_aliasId')?.clearValidators();
    this.uploadForm.get('personTwo_aliasId')?.updateValueAndValidity();

    this.selectedFile = null;
    this.initiateResponse = null;
    this.isUploading = false;
    this.isSaving = false;
    this.showSuccessToast = false;
    this.showErrorToast = false;
    this.toastMessage = '';

    // Asegurarse de que el input de archivo también se resetee
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }
}
