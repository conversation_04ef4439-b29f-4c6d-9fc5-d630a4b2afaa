import { IAlias } from '../../alias/alias.model';

export interface WhatsAppUploadParseDto {
  time: string; // En Java es Instant, en TypeScript usamos string
  sender: string;
  content: string;
  lineNumber: number;
}

export interface WhatsAppUploadResponseDto {
  parsedLines: WhatsAppUploadParseDto[];
  whatsappMessageSourceDTO: any; // Ajustar el tipo según corresponda
  namePersonOne: string;
  namePersonTwo: string;
  aliasPersonOne: IAlias | null;
  aliasPersonTwo: IAlias | null;
}
