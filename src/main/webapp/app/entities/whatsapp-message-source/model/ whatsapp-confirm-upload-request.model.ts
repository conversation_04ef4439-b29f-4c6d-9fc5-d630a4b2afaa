import { IAlias } from 'app/entities/alias/alias.model';

export interface IWhatsAppConfirmUploadRequestDto {
  uploadId: string;
  aliasPersonOne: IAlias;
  aliasPersonTwo: IAlias | null;
}

export class WhatsAppConfirmUploadRequestDto implements IWhatsAppConfirmUploadRequestDto {
  constructor(
    public uploadId: string,
    public aliasPersonOne: IAlias,
    public aliasPersonTwo: IAlias | null,
  ) {}
}
