import { WhatsAppUploadParseDto } from './whatsapp-upload-parse.model';
import { IAlias } from '../../alias/alias.model';
import { IWhatsappMessageSource } from './whatsapp-message-source.model';

export interface WhatsAppUploadResponseDto {
  parsedLines: WhatsAppUploadParseDto[];
  whatsappMessageSourceDTO: IWhatsappMessageSource | null;
  namePersonOne: string;
  namePersonTwo: string;
  aliasPersonOne: IAlias | null;
  aliasPersonTwo: IAlias | null;
}
