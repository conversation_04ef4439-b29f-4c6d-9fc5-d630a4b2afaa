@if (account) {
  <button
    id="whatsapp-create-button"
    class="btn btn-whatsapp me-2"
    style="background-color: #25d366; border-color: #25d366; color: white"
    (click)="openModal()"
    data-cy="whatsappUploadButton"
    aria-label="Cargar conversación de WhatsApp"
  >
    <fa-icon [icon]="faWhatsapp" class="me-2"></fa-icon>
    <span>Cargar WhatsApp</span>
  </button>
}

<ng-template #content let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Cargar conversación de WhatsApp</h4>
    <button type="button" class="btn-close" (click)="modal.dismiss('cancel')" aria-label="Cerrar"></button>
  </div>

  <div class="modal-body">
    <div *ngIf="!initiateResponse" class="text-center mb-4 p-4 border rounded bg-light">
      <input #fileInput type="file" class="d-none" (change)="onFileChange($event)" accept=".txt" [disabled]="isUploading" />
      <div *ngIf="!isUploading">
        <button type="button" class="btn btn-primary btn-lg" (click)="triggerFileInput()">
          <fa-icon [icon]="faUpload" class="me-2"></fa-icon>
          <span jhiTranslate="forconversationsApp.whatsappMessageSource.upload.selectFile">Upload .txt file</span>
        </button>
      </div>
      <div *ngIf="isUploading" class="w-100">
        <div class="spinner-border text-primary">
          <output class="visually-hidden">Procesando...</output>
        </div>
        <p class="text-muted mt-2 mb-0">Procesando fichero, por favor espera...</p>
      </div>
    </div>

    <div *ngIf="initiateResponse" class="mt-4">
      <h5>Asignar alias</h5>
      <p class="text-muted">Hemos detectado los siguientes participantes. Por favor, asígnales un alias.</p>

      <form [formGroup]="uploadForm">
        <div class="row g-3">
          <div class="col-md-6">
            <label class="form-label fw-bold" for="personOne_aliasId">{{ initiateResponse.namePersonOne }}</label>
            <input type="text" readonly class="form-control-plaintext" [value]="initiateResponse.namePersonOne" />
            <select class="form-select mt-1" formControlName="personOne_aliasId">
              <option [ngValue]="null" disabled>Seleccionar alias...</option>
              <option *ngFor="let alias of availableAliases" [ngValue]="alias.id">{{ alias.value }}</option>
            </select>
          </div>
          <div class="col-md-6" *ngIf="initiateResponse.namePersonTwo">
            <label class="form-label fw-bold" for="personTwo_aliasId">{{ initiateResponse.namePersonTwo }}</label>
            <input type="text" readonly class="form-control-plaintext" [value]="initiateResponse.namePersonTwo" />
            <select class="form-select mt-1" formControlName="personTwo_aliasId">
              <option [ngValue]="null" disabled>Seleccionar alias...</option>
              <option *ngFor="let alias of availableAliases" [ngValue]="alias.id">{{ alias.value }}</option>
            </select>
          </div>
        </div>
      </form>
    </div>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="modal.dismiss('cancel')" [disabled]="isSaving">
      <fa-icon [icon]="faTimes" class="me-1"></fa-icon>
      Cancelar
    </button>
    <button *ngIf="initiateResponse" type="button" class="btn btn-primary" (click)="onSave()" [disabled]="isSaving || uploadForm.invalid">
      <span *ngIf="!isSaving; else saving">
        <fa-icon [icon]="faUpload" class="me-1"></fa-icon>
        Guardar Conversación
      </span>
      <ng-template #saving>
        <span class="spinner-border spinner-border-sm me-2">
          <output class="visually-hidden">Guardando...</output>
        </span>
        Guardando...
      </ng-template>
    </button>
  </div>
</ng-template>
