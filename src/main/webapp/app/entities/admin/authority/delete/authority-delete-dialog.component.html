@if (authority) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(authority.name!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="authorityDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirm delete operation</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error />
      <p
        id="jhi-delete-authority-heading"
        jhiTranslate="forconversationsApp.adminAuthority.delete.question"
        [translateValues]="{ id: authority.name }"
      >
        Are you sure you want to delete Authority {{ authority.name }}?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
      </button>

      <button id="jhi-confirm-delete-authority" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Delete</span>
      </button>
    </div>
  </form>
}
