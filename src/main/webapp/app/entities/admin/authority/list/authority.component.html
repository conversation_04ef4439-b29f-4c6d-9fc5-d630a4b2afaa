<div>
  <h2 id="page-heading" data-cy="AuthorityHeading">
    <span jhiTranslate="forconversationsApp.adminAuthority.home.title">Authorities</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="forconversationsApp.adminAuthority.home.refreshListLabel">Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-authority"
        [routerLink]="['/authority/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span jhiTranslate="forconversationsApp.adminAuthority.home.createLabel">Create a new Authority</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error />

  <jhi-alert />

  @if (authorities().length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="forconversationsApp.adminAuthority.home.notFound">No Authorities found</span>
    </div>
  } @else {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [(sortState)]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="name">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.adminAuthority.name">Name</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (authority of authorities(); track trackName(authority)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/authority', authority.name, 'view']">{{ authority.name }}</a>
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <a [routerLink]="['/authority', authority.name, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">View</span>
                  </a>

                  <button type="submit" (click)="delete(authority)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Delete</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
</div>
