<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2
        id="jhi-authority-heading"
        data-cy="AuthorityCreateUpdateHeading"
        jhiTranslate="forconversationsApp.adminAuthority.home.createOrEditLabel"
      >
        Create or edit a Authority
      </h2>

      <div>
        <jhi-alert-error />

        @let nameRef = editForm.get('name')!;
        <div class="mb-3">
          <label class="form-label" for="field_name" jhiTranslate="forconversationsApp.adminAuthority.name">Name</label>
          <input
            type="text"
            class="form-control"
            name="name"
            id="field_name"
            data-cy="name"
            formControlName="name"
            [readonly]="editForm.get('name')!.disabled"
          />
          @if (nameRef.invalid && (nameRef.dirty || nameRef.touched)) {
            <div>
              @if (editForm.get('name')?.errors?.required) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
              }
              @if (editForm.get('name')?.errors?.maxlength) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.maxlength" [translateValues]="{ max: '50' }"
                  >This field cannot be longer than 50 characters.</small
                >
              }
            </div>
          }
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Save</span>
        </button>
      </div>
    </form>
  </div>
</div>
