import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IEmailMessageSource } from '../email-message-source.model';
import { EmailMessageSourceService } from '../service/email-message-source.service';

const emailMessageSourceResolve = (route: ActivatedRouteSnapshot): Observable<null | IEmailMessageSource> => {
  const id = route.params.id;
  if (id) {
    return inject(EmailMessageSourceService)
      .find(id)
      .pipe(
        mergeMap((emailMessageSource: HttpResponse<IEmailMessageSource>) => {
          if (emailMessageSource.body) {
            return of(emailMessageSource.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default emailMessageSourceResolve;
