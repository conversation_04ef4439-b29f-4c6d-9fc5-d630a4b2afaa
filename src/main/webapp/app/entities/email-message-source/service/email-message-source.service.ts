import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, asapScheduler, map, scheduled } from 'rxjs';

import { catchError } from 'rxjs/operators';

import dayjs from 'dayjs/esm';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { Search } from 'app/core/request/request.model';
import { IEmailMessageSource, NewEmailMessageSource } from '../email-message-source.model';

export type PartialUpdateEmailMessageSource = Partial<IEmailMessageSource> & Pick<IEmailMessageSource, 'id'>;

type RestOf<T extends IEmailMessageSource | NewEmailMessageSource> = Omit<T, 'time'> & {
  time?: string | null;
};

export type RestEmailMessageSource = RestOf<IEmailMessageSource>;

export type NewRestEmailMessageSource = RestOf<NewEmailMessageSource>;

export type PartialUpdateRestEmailMessageSource = RestOf<PartialUpdateEmailMessageSource>;

export type EntityResponseType = HttpResponse<IEmailMessageSource>;
export type EntityArrayResponseType = HttpResponse<IEmailMessageSource[]>;

@Injectable({ providedIn: 'root' })
export class EmailMessageSourceService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/email-message-sources');
  protected resourceSearchUrl = this.applicationConfigService.getEndpointFor('api/email-message-sources/_search');

  create(emailMessageSource: NewEmailMessageSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(emailMessageSource);
    return this.http
      .post<RestEmailMessageSource>(this.resourceUrl, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  update(emailMessageSource: IEmailMessageSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(emailMessageSource);
    return this.http
      .put<RestEmailMessageSource>(`${this.resourceUrl}/${this.getEmailMessageSourceIdentifier(emailMessageSource)}`, copy, {
        observe: 'response',
      })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  partialUpdate(emailMessageSource: PartialUpdateEmailMessageSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(emailMessageSource);
    return this.http
      .patch<RestEmailMessageSource>(`${this.resourceUrl}/${this.getEmailMessageSourceIdentifier(emailMessageSource)}`, copy, {
        observe: 'response',
      })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  find(id: string): Observable<EntityResponseType> {
    return this.http
      .get<RestEmailMessageSource>(`${this.resourceUrl}/${id}`, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestEmailMessageSource[]>(this.resourceUrl, { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }

  delete(id: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  uploadFile(file: FormData): Observable<any> {
    return this.http.post(`${this.resourceUrl}/load-data`, file, {
      reportProgress: true,
      observe: 'events',
    });
  }

  search(req: Search): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<RestEmailMessageSource[]>(this.resourceSearchUrl, { params: options, observe: 'response' }).pipe(
      map(res => this.convertResponseArrayFromServer(res)),

      catchError(() => scheduled([new HttpResponse<IEmailMessageSource[]>()], asapScheduler)),
    );
  }

  getEmailMessageSourceIdentifier(emailMessageSource: Pick<IEmailMessageSource, 'id'>): string {
    return emailMessageSource.id;
  }

  compareEmailMessageSource(o1: Pick<IEmailMessageSource, 'id'> | null, o2: Pick<IEmailMessageSource, 'id'> | null): boolean {
    return o1 && o2 ? this.getEmailMessageSourceIdentifier(o1) === this.getEmailMessageSourceIdentifier(o2) : o1 === o2;
  }

  addEmailMessageSourceToCollectionIfMissing<Type extends Pick<IEmailMessageSource, 'id'>>(
    emailMessageSourceCollection: Type[],
    ...emailMessageSourcesToCheck: (Type | null | undefined)[]
  ): Type[] {
    const emailMessageSources: Type[] = emailMessageSourcesToCheck.filter(isPresent);
    if (emailMessageSources.length > 0) {
      const emailMessageSourceCollectionIdentifiers = emailMessageSourceCollection.map(emailMessageSourceItem =>
        this.getEmailMessageSourceIdentifier(emailMessageSourceItem),
      );
      const emailMessageSourcesToAdd = emailMessageSources.filter(emailMessageSourceItem => {
        const emailMessageSourceIdentifier = this.getEmailMessageSourceIdentifier(emailMessageSourceItem);
        if (emailMessageSourceCollectionIdentifiers.includes(emailMessageSourceIdentifier)) {
          return false;
        }
        emailMessageSourceCollectionIdentifiers.push(emailMessageSourceIdentifier);
        return true;
      });
      return [...emailMessageSourcesToAdd, ...emailMessageSourceCollection];
    }
    return emailMessageSourceCollection;
  }

  protected convertDateFromClient<T extends IEmailMessageSource | NewEmailMessageSource | PartialUpdateEmailMessageSource>(
    emailMessageSource: T,
  ): RestOf<T> {
    return {
      ...emailMessageSource,
      time: emailMessageSource.time?.toJSON() ?? null,
    };
  }

  protected convertDateFromServer(restEmailMessageSource: RestEmailMessageSource): IEmailMessageSource {
    return {
      ...restEmailMessageSource,
      time: restEmailMessageSource.time ? dayjs(restEmailMessageSource.time) : undefined,
    };
  }

  protected convertResponseFromServer(res: HttpResponse<RestEmailMessageSource>): HttpResponse<IEmailMessageSource> {
    return res.clone({
      body: res.body ? this.convertDateFromServer(res.body) : null,
    });
  }

  protected convertResponseArrayFromServer(res: HttpResponse<RestEmailMessageSource[]>): HttpResponse<IEmailMessageSource[]> {
    return res.clone({
      body: res.body ? res.body.map(item => this.convertDateFromServer(item)) : null,
    });
  }
}
