@if (account) {
  <button
    id="email-message-source-create-button"
    class="btn btn-email me-2"
    style="background-color: #007bff; border-color: #007bff; color: white"
    (click)="openModal()"
    data-cy="emailMessageSourceUploadButton"
    aria-label="Cargar archivo de email"
  >
    <fa-icon [icon]="faEmail" class="me-2"></fa-icon>
    <span>Cargar Email</span>
  </button>
}

<ng-template #content let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Cargar correo electrónico</h4>
    <button type="button" class="btn-close" (click)="onCancel()" aria-label="Cerrar"></button>
  </div>

  <div class="modal-body">
    <!-- Sección de carga de archivo -->
    <div class="text-center mb-4 p-4 border rounded" style="background-color: #f8f9fa">
      <input
        #fileInput
        type="file"
        class="d-none"
        (change)="onFileSelected($event)"
        [accept]="allowedFileTypes.join(',')"
        [disabled]="isUploading"
      />

      <div *ngIf="!isUploading">
        <button
          type="button"
          class="btn btn-primary btn-lg"
          (click)="triggerFileInput()"
          [disabled]="isUploading"
          aria-label="Subir archivo de correo electrónico"
        >
          <fa-icon [icon]="faUpload" class="me-2"></fa-icon>
          Subir correo electrónico
        </button>
        <p class="text-muted mt-2 mb-0">Formatos aceptados: .eml, .mbox, .txt, archivos MBOX sin extensión</p>
        <p class="text-muted small">Tamaño máximo: 10MB</p>
      </div>

      <!-- Barra de progreso -->
      <div *ngIf="isUploading" class="w-100">
        <div class="progress mb-2">
          <div
            class="progress-bar progress-bar-striped progress-bar-animated"
            role="progressbar"
            [style.width.%]="uploadProgress"
            [attr.aria-valuenow]="uploadProgress"
            aria-valuemin="0"
            aria-valuemax="100"
          >
            {{ uploadProgress }}%
          </div>
        </div>
        <p class="text-muted mb-0">Procesando archivo, por favor espera...</p>
      </div>
    </div>

    <!-- Mensaje de éxito -->
    <div
      *ngIf="showSuccess"
      class="alert alert-success alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3"
      role="alert"
    >
      {{ successMessage }}
      <button type="button" class="btn-close" (click)="showSuccess = false" aria-label="Cerrar"></button>
    </div>

    <!-- Toast de error -->
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="onCancel()" [disabled]="isUploading">
      <fa-icon [icon]="faTimes" class="me-1"></fa-icon>
      <span>Cancelar</span>
    </button>
  </div>
</ng-template>
