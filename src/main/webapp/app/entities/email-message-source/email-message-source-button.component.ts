import { CommonModule } from '@angular/common';
import { HttpErrorResponse, HttpEventType, HttpResponse } from '@angular/common/http';
import { Component, ElementRef, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { faEnvelope, faTimes, faUpload } from '@fortawesome/free-solid-svg-icons';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Account } from 'app/core/auth/account.model';
import { AccountService } from 'app/core/auth/account.service';
import { EmailMessageSourceService } from './service/email-message-source.service';

@Component({
  selector: 'jhi-email-message-source-button',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FaIconComponent],
  templateUrl: './email-message-source-button.component.html',
})
export class EmailMessageSourceButtonComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('content') modalContent: any;
  @Output() fileSelected = new EventEmitter<File>();

  account: Account | null = null;
  faEmail = faEnvelope;
  faUpload = faUpload;
  faTimes = faTimes;

  uploadForm: FormGroup;
  selectedFile: File | null = null;
  uploadProgress = 0;
  isUploading = false;
  allowedFileTypes = ['.eml', '.msg', '.txt', '.mbox', ''];

  // UI State
  showSuccess = false;
  successMessage = '';
  showToast = false;
  toastMsg = '';
  maxFileSize = 10 * 1024 * 1024; // 10MB

  private modalRef: NgbModalRef | null = null;

  constructor(
    private modalService: NgbModal,
    private formBuilder: FormBuilder,
    private emailMessageSourceService: EmailMessageSourceService,
    private accountService: AccountService,
  ) {
    this.uploadForm = this.formBuilder.group({
      aliases: this.formBuilder.group({}),
    });
  }

  ngOnInit(): void {
    this.accountService.identity().subscribe(account => {
      this.account = account;
    });
  }

  openModal(): void {
    this.modalRef = this.modalService.open(this.modalContent, { size: 'lg' });
  }

  onCancel(): void {
    if (this.modalRef) {
      this.modalRef.dismiss('cancel');
    }
  }

  getObjectKeys(obj: any): string[] {
    return Object.keys(obj ?? {});
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  onToastClosed(): void {
    this.showToast = false;
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Validar tipo de archivo
    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.includes('.') ? `.${fileName.split('.').pop()}` : '';

    // Permitir archivos MBOX sin extensión o con extensiones válidas
    const isValidFile = this.allowedFileTypes.includes(fileExtension) || fileName.includes('mbox') || !fileName.includes('.'); // Archivos sin extensión (común en MBOX)

    if (!isValidFile) {
      this.showErrorToast('Tipo de archivo no permitido. Por favor, sube un archivo .eml, .mbox, .txt o archivo MBOX sin extensión');
      return;
    }

    // Validar tamaño del archivo
    if (file.size > this.maxFileSize) {
      this.showErrorToast('El archivo es demasiado grande. El tamaño máximo permitido es 10MB');
      return;
    }

    this.selectedFile = file;
    this.uploadFile();
  }

  uploadFile(): void {
    if (!this.selectedFile) return;

    this.isUploading = true;
    this.uploadProgress = 0;

    const formData = new FormData();
    formData.append('file', this.selectedFile);

    this.emailMessageSourceService.uploadFile(formData).subscribe({
      next: (event: any) => {
        if (event.type === HttpEventType.UploadProgress) {
          this.uploadProgress = Math.round((100 * event.loaded) / (event.total ?? 1));
        } else if (event instanceof HttpResponse) {
          this.showSuccess = true;
          this.successMessage = 'Archivo de correo cargado exitosamente';
          this.isUploading = false;
          this.fileSelected.emit(this.selectedFile as File);

          // Cerrar el modal después de 2 segundos
          setTimeout(() => {
            if (this.modalRef) {
              this.modalRef.close('success');
            }
          }, 2000);
        }
      },
      error: (error: HttpErrorResponse) => {
        console.error('Error al cargar el archivo:', error);
        this.isUploading = false;
        this.showErrorToast('Error al cargar el archivo. Por favor, inténtalo de nuevo.');
      },
    });
  }

  private showErrorToast(message: string): void {
    this.toastMsg = message;
    this.showToast = true;
    setTimeout(() => {
      this.showToast = false;
    }, 5000);
  }
}
