import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IAudioMessageSource } from '../model/audio-message-source.model';
import { AudioMessageSourceService } from '../service/audio-message-source.service';

const audioMessageSourceResolve = (route: ActivatedRouteSnapshot): Observable<null | IAudioMessageSource> => {
  const id = route.params.id;
  if (id) {
    return inject(AudioMessageSourceService)
      .find(id)
      .pipe(
        mergeMap((audioMessageSource: HttpResponse<IAudioMessageSource>) => {
          if (audioMessageSource.body) {
            return of(audioMessageSource.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default audioMessageSourceResolve;
