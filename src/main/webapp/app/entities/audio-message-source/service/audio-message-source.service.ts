import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, asapScheduler, map, scheduled } from 'rxjs';
import { catchError } from 'rxjs/operators';

import dayjs from 'dayjs/esm';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { Search } from 'app/core/request/request.model';
import { IAudioMessageSource, NewAudioMessageSource } from '../model/audio-message-source.model';
import { AudioMessageSourceResponseDto } from '../model/audio-message-source-response-dto.model';

export type PartialUpdateAudioMessageSource = Partial<IAudioMessageSource> & Pick<IAudioMessageSource, 'id'>;

type RestOf<T extends IAudioMessageSource | NewAudioMessageSource> = Omit<T, 'time'> & {
  time?: string | null;
};

export type RestAudioMessageSource = RestOf<IAudioMessageSource>;
export type NewRestAudioMessageSource = RestOf<NewAudioMessageSource>;
export type PartialUpdateRestAudioMessageSource = RestOf<PartialUpdateAudioMessageSource>;

export type EntityResponseType = HttpResponse<IAudioMessageSource>;
export type EntityArrayResponseType = HttpResponse<IAudioMessageSource[]>;

@Injectable({ providedIn: 'root' })
export class AudioMessageSourceService {
  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/audio-message-sources');
  protected resourceSearchUrl = this.applicationConfigService.getEndpointFor('api/audio-message-sources/_search');

  constructor(
    protected http: HttpClient,
    protected applicationConfigService: ApplicationConfigService,
  ) {}

  create(audioMessageSource: NewAudioMessageSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(audioMessageSource);
    return this.http
      .post<RestAudioMessageSource>(this.resourceUrl, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  update(audioMessageSource: IAudioMessageSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(audioMessageSource);
    return this.http
      .put<RestAudioMessageSource>(`${this.resourceUrl}/${this.getAudioMessageSourceIdentifier(audioMessageSource)}`, copy, {
        observe: 'response',
      })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  partialUpdate(audioMessageSource: PartialUpdateAudioMessageSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(audioMessageSource);
    return this.http
      .patch<RestAudioMessageSource>(`${this.resourceUrl}/${this.getAudioMessageSourceIdentifier(audioMessageSource)}`, copy, {
        observe: 'response',
      })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  find(id: string): Observable<EntityResponseType> {
    return this.http
      .get<RestAudioMessageSource>(`${this.resourceUrl}/${id}`, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestAudioMessageSource[]>(this.resourceUrl, { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }

  delete(id: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  // Audio-specific Operations
  uploadAudio(formData: FormData): Observable<AudioMessageSourceResponseDto> {
    return this.http.post<AudioMessageSourceResponseDto>(`${this.resourceUrl}/load-data`, formData);
  }

  createAudioMessageSource(audioUploadResponse: AudioMessageSourceResponseDto): Observable<EntityResponseType> {
    return this.http
      .post<RestAudioMessageSource>(this.resourceUrl, audioUploadResponse, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  search(req: Search): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestAudioMessageSource[]>(this.resourceSearchUrl, {
        params: options,
        observe: 'response',
      })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }

  // Helper Methods
  getAudioMessageSourceIdentifier(audioMessageSource: Pick<IAudioMessageSource, 'id'>): string {
    return audioMessageSource.id;
  }

  compareAudioMessageSource(o1: Pick<IAudioMessageSource, 'id'> | null, o2: Pick<IAudioMessageSource, 'id'> | null): boolean {
    return o1 && o2 ? this.getAudioMessageSourceIdentifier(o1) === this.getAudioMessageSourceIdentifier(o2) : o1 === o2;
  }

  addAudioMessageSourceToCollectionIfMissing<Type extends Pick<IAudioMessageSource, 'id'>>(
    audioMessageSourceCollection: Type[],
    ...audioMessageSourcesToCheck: (Type | null | undefined)[]
  ): Type[] {
    const audioMessageSources = audioMessageSourcesToCheck.filter(isPresent);
    if (audioMessageSources.length > 0) {
      const audioMessageSourceCollectionIdentifiers = audioMessageSourceCollection.map(audioMessageSourceItem =>
        this.getAudioMessageSourceIdentifier(audioMessageSourceItem),
      );
      const audioMessageSourcesToAdd = audioMessageSources.filter(audioMessageSourceItem => {
        const audioMessageSourceIdentifier = this.getAudioMessageSourceIdentifier(audioMessageSourceItem);
        if (audioMessageSourceCollectionIdentifiers.includes(audioMessageSourceIdentifier)) {
          return false;
        }
        audioMessageSourceCollectionIdentifiers.push(audioMessageSourceIdentifier);
        return true;
      });
      return [...audioMessageSourcesToAdd, ...audioMessageSourceCollection];
    }
    return audioMessageSourceCollection;
  }

  // Conversion Methods
  protected convertDateFromClient<T extends IAudioMessageSource | NewAudioMessageSource | PartialUpdateAudioMessageSource>(
    audioMessageSource: T,
  ): RestOf<T> {
    return {
      ...audioMessageSource,
      time: audioMessageSource.time?.toJSON() ?? null,
    };
  }

  protected convertDateFromServer(restAudioMessageSource: RestAudioMessageSource): IAudioMessageSource {
    return {
      ...restAudioMessageSource,
      time: restAudioMessageSource.time ? dayjs(restAudioMessageSource.time) : undefined,
    };
  }

  protected convertResponseFromServer(res: HttpResponse<RestAudioMessageSource>): HttpResponse<IAudioMessageSource> {
    return res.clone({
      body: res.body ? this.convertDateFromServer(res.body) : null,
    });
  }

  protected convertResponseArrayFromServer(res: HttpResponse<RestAudioMessageSource[]>): HttpResponse<IAudioMessageSource[]> {
    return res.clone({
      body: res.body ? res.body.map(item => this.convertDateFromServer(item)) : null,
    });
  }
}
