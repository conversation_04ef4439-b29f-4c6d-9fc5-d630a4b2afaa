import { AliasType } from 'app/entities/enumerations/alias-type.model';

/**
 * Interface representing the response from audio upload and transcription
 */
export interface AudioMessageSourceResponseDto {
  // Audio file data
  file?: any;
  fileContentType?: string;

  // Transcription text
  text?: string;

  // Alias objects (simplified to avoid circular dependencies)
  aliasSender?: {
    id?: string;
    value?: string;
    type?: AliasType;
  } | null;

  aliasReceiver?: {
    id?: string;
    value?: string;
    type?: AliasType;
  } | null;
  // Timestamp
  time?: string;

  // Additional metadata
  duration?: number; // in seconds
  sampleRateHertz?: number;
  languageCode?: string;

  // Status
  status?: 'success' | 'error' | 'processing';
  errorMessage?: string;
}
