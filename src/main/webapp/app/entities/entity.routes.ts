import { Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'authority',
    data: { pageTitle: 'forconversationsApp.adminAuthority.home.title' },
    loadChildren: () => import('./admin/authority/authority.routes'),
  },

  {
    path: 'source',
    data: { pageTitle: 'forconversationsApp.source.home.title' },
    loadChildren: () => import('./source/source.routes'),
  },
  {
    path: 'whatsapp-message-source',
    data: { pageTitle: 'forconversationsApp.whatsappMessageSource.home.title' },
    loadChildren: () => import('./whatsapp-message-source/whatsapp-message-source.routes'),
  },
  {
    path: 'audio-message-source',
    data: { pageTitle: 'forconversationsApp.audioMessageSource.home.title' },
    loadChildren: () => import('./audio-message-source/audio-message-source.routes'),
  },
  {
    path: 'email-message-source',
    data: { pageTitle: 'forconversationsApp.emailMessageSource.home.title' },
    loadChildren: () => import('./email-message-source/email-message-source.routes'),
  },
  {
    path: 'alias',
    data: { pageTitle: 'forconversationsApp.alias.home.title' },
    loadChildren: () => import('./alias/alias.routes'),
  },
  {
    path: 'participant',
    data: { pageTitle: 'forconversationsApp.participant.home.title' },
    loadChildren: () => import('./participant/participant.routes'),
  },
  /* jhipster-needle-add-entity-route - JHipster will add entity modules routes here */
];

export default routes;
