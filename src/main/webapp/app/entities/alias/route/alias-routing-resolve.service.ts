import { inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { EMPTY, Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';

import { IAlias } from '../alias.model';
import { AliasService } from '../service/alias.service';

const aliasResolve = (route: ActivatedRouteSnapshot): Observable<null | IAlias> => {
  const id = route.params.id;
  if (id) {
    return inject(AliasService)
      .find(id)
      .pipe(
        mergeMap((alias: HttpResponse<IAlias>) => {
          if (alias.body) {
            return of(alias.body);
          }
          inject(Router).navigate(['404']);
          return EMPTY;
        }),
      );
  }
  return of(null);
};

export default aliasResolve;
