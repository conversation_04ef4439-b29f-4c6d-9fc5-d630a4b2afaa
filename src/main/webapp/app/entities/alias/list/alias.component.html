<div>
  <h2 id="page-heading" data-cy="AliasHeading">
    <span jhiTranslate="forconversationsApp.alias.home.title">Aliases</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="forconversationsApp.alias.home.refreshListLabel">Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-alias"
        [routerLink]="['/alias/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span class="hidden-sm-down" jhiTranslate="forconversationsApp.alias.home.createLabel">Create a new Alias</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error />

  <jhi-alert />

  <form name="searchForm" class="row row-cols-sm-auto align-items-center">
    <div class="col-sm-12">
      <div class="input-group w-100 mt-3">
        <label class="visually-hidden" for="currentSearch" jhiTranslate="forconversationsApp.alias.home.search">Search for Alias</label>
        <input
          type="text"
          class="form-control"
          [(ngModel)]="currentSearch"
          id="currentSearch"
          name="currentSearch"
          placeholder="{{ 'forconversationsApp.alias.home.search' | translate }}"
        />

        <button class="btn btn-info" (click)="search(currentSearch)">
          <fa-icon icon="search"></fa-icon>
        </button>

        @if (currentSearch) {
          <button class="btn btn-danger" (click)="search('')">
            <fa-icon icon="trash-alt"></fa-icon>
          </button>
        }
      </div>
    </div>
  </form>

  @if (aliases().length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="forconversationsApp.alias.home.notFound">No Aliases found</span>
    </div>
  } @else {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [(sortState)]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="id">
              <div class="d-flex">
                <span jhiTranslate="global.field.id">ID</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="value">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.alias.value">Value</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="type">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.alias.type">Type</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (alias of aliases(); track trackId(alias)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/alias', alias.id, 'view']">{{ alias.id }}</a>
              </td>
              <td>{{ alias.value }}</td>
              <td [jhiTranslate]="'forconversationsApp.AliasType.' + (alias.type ?? 'null')">
                {{ { null: '', WHATSAPP: 'WHATSAPP', EMAIL: 'EMAIL' }[alias.type ?? 'null'] }}
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <a [routerLink]="['/alias', alias.id, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">View</span>
                  </a>

                  <a [routerLink]="['/alias', alias.id, 'edit']" class="btn btn-primary btn-sm" data-cy="entityEditButton">
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Edit</span>
                  </a>

                  <button type="submit" (click)="delete(alias)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Delete</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
</div>
