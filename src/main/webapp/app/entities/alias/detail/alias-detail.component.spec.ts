import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { of } from 'rxjs';

import { AliasDetailComponent } from './alias-detail.component';

describe('Alias Management Detail Component', () => {
  let comp: AliasDetailComponent;
  let fixture: ComponentFixture<AliasDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AliasDetailComponent],
      providers: [
        provideRouter(
          [
            {
              path: '**',
              loadComponent: () => import('./alias-detail.component').then(m => m.AliasDetailComponent),
              resolve: { alias: () => of({ id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' }) },
            },
          ],
          withComponentInputBinding(),
        ),
      ],
    })
      .overrideTemplate(AliasDetailComponent, '')
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AliasDetailComponent);
    comp = fixture.componentInstance;
  });

  describe('OnInit', () => {
    it('should load alias on init', async () => {
      const harness = await RouterTestingHarness.create();
      const instance = await harness.navigateByUrl('/', AliasDetailComponent);

      // THEN
      expect(instance.alias()).toEqual(expect.objectContaining({ id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' }));
    });
  });

  describe('PreviousState', () => {
    it('should navigate to previous state', () => {
      jest.spyOn(window.history, 'back');
      comp.previousState();
      expect(window.history.back).toHaveBeenCalled();
    });
  });
});
