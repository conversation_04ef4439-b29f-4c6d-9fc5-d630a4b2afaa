import { Component, input } from '@angular/core';
import { RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { IAlias } from '../alias.model';

@Component({
  selector: 'jhi-alias-detail',
  templateUrl: './alias-detail.component.html',
  imports: [SharedModule, RouterModule],
})
export class AliasDetailComponent {
  alias = input<IAlias | null>(null);

  previousState(): void {
    window.history.back();
  }
}
