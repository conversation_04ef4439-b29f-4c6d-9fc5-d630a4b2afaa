<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (alias(); as aliasRef) {
      <div>
        <h2 data-cy="aliasDetailsHeading"><span jhiTranslate="forconversationsApp.alias.detail.title">Alias</span></h2>

        <hr />

        <jhi-alert-error />

        <jhi-alert />

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.id">ID</span></dt>
          <dd>
            <span>{{ aliasRef.id }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.alias.value">Value</span></dt>
          <dd>
            <span>{{ aliasRef.value }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.alias.type">Type</span></dt>
          <dd>
            <span [jhiTranslate]="'forconversationsApp.AliasType.' + (aliasRef.type ?? 'null')">{{
              { null: '', WHATSAPP: 'WHATSAPP', EMAIL: 'EMAIL' }[aliasRef.type ?? 'null']
            }}</span>
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Back</span>
        </button>

        <button type="button" [routerLink]="['/alias', aliasRef.id, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Edit</span>
        </button>
      </div>
    }
  </div>
</div>
