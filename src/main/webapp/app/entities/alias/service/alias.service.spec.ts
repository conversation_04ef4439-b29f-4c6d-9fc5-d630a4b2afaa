import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';

import { IAlias } from '../alias.model';
import { sampleWithFullData, sampleWithNewData, sampleWithPartialData, sampleWithRequiredData } from '../alias.test-samples';

import { AliasService } from './alias.service';

const requireRestSample: IAlias = {
  ...sampleWithRequiredData,
};

describe('Alias Service', () => {
  let service: AliasService;
  let httpMock: HttpTestingController;
  let expectedResult: IAlias | IAlias[] | boolean | null;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    expectedResult = null;
    service = TestBed.inject(AliasService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  describe('Service methods', () => {
    it('should find an element', () => {
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.find('ABC').subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should create a Alias', () => {
      const alias = { ...sampleWithNewData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.create(alias).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'POST' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should update a Alias', () => {
      const alias = { ...sampleWithRequiredData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.update(alias).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PUT' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should partial update a Alias', () => {
      const patchObject = { ...sampleWithPartialData };
      const returnedFromService = { ...requireRestSample };
      const expected = { ...sampleWithRequiredData };

      service.partialUpdate(patchObject).subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'PATCH' });
      req.flush(returnedFromService);
      expect(expectedResult).toMatchObject(expected);
    });

    it('should return a list of Alias', () => {
      const returnedFromService = { ...requireRestSample };

      const expected = { ...sampleWithRequiredData };

      service.query().subscribe(resp => (expectedResult = resp.body));

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush([returnedFromService]);
      httpMock.verify();
      expect(expectedResult).toMatchObject([expected]);
    });

    it('should delete a Alias', () => {
      const expected = true;

      service.delete('ABC').subscribe(resp => (expectedResult = resp.ok));

      const req = httpMock.expectOne({ method: 'DELETE' });
      req.flush({ status: 200 });
      expect(expectedResult).toBe(expected);
    });

    it('should handle exceptions for searching a Alias', () => {
      const queryObject: any = {
        page: 0,
        size: 20,
        query: '',
        sort: [],
      };
      service.search(queryObject).subscribe(() => expectedResult);

      const req = httpMock.expectOne({ method: 'GET' });
      req.flush(null, { status: 500, statusText: 'Internal Server Error' });
      expect(expectedResult).toBe(null);
    });

    describe('addAliasToCollectionIfMissing', () => {
      it('should add a Alias to an empty array', () => {
        const alias: IAlias = sampleWithRequiredData;
        expectedResult = service.addAliasToCollectionIfMissing([], alias);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(alias);
      });

      it('should not add a Alias to an array that contains it', () => {
        const alias: IAlias = sampleWithRequiredData;
        const aliasCollection: IAlias[] = [
          {
            ...alias,
          },
          sampleWithPartialData,
        ];
        expectedResult = service.addAliasToCollectionIfMissing(aliasCollection, alias);
        expect(expectedResult).toHaveLength(2);
      });

      it("should add a Alias to an array that doesn't contain it", () => {
        const alias: IAlias = sampleWithRequiredData;
        const aliasCollection: IAlias[] = [sampleWithPartialData];
        expectedResult = service.addAliasToCollectionIfMissing(aliasCollection, alias);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(alias);
      });

      it('should add only unique Alias to an array', () => {
        const aliasArray: IAlias[] = [sampleWithRequiredData, sampleWithPartialData, sampleWithFullData];
        const aliasCollection: IAlias[] = [sampleWithRequiredData];
        expectedResult = service.addAliasToCollectionIfMissing(aliasCollection, ...aliasArray);
        expect(expectedResult).toHaveLength(3);
      });

      it('should accept varargs', () => {
        const alias: IAlias = sampleWithRequiredData;
        const alias2: IAlias = sampleWithPartialData;
        expectedResult = service.addAliasToCollectionIfMissing([], alias, alias2);
        expect(expectedResult).toHaveLength(2);
        expect(expectedResult).toContain(alias);
        expect(expectedResult).toContain(alias2);
      });

      it('should accept null and undefined values', () => {
        const alias: IAlias = sampleWithRequiredData;
        expectedResult = service.addAliasToCollectionIfMissing([], null, alias, undefined);
        expect(expectedResult).toHaveLength(1);
        expect(expectedResult).toContain(alias);
      });

      it('should return initial array if no Alias is added', () => {
        const aliasCollection: IAlias[] = [sampleWithRequiredData];
        expectedResult = service.addAliasToCollectionIfMissing(aliasCollection, undefined, null);
        expect(expectedResult).toEqual(aliasCollection);
      });
    });

    describe('compareAlias', () => {
      it('should return true if both entities are null', () => {
        const entity1 = null;
        const entity2 = null;

        const compareResult = service.compareAlias(entity1, entity2);

        expect(compareResult).toEqual(true);
      });

      it('should return false if one entity is null', () => {
        const entity1 = { id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' };
        const entity2 = null;

        const compareResult1 = service.compareAlias(entity1, entity2);
        const compareResult2 = service.compareAlias(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('should return false if primaryKey differs', () => {
        const entity1 = { id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' };
        const entity2 = { id: '0ce539f8-16c5-460a-a1c1-f8e9002e31e8' };

        const compareResult1 = service.compareAlias(entity1, entity2);
        const compareResult2 = service.compareAlias(entity2, entity1);

        expect(compareResult1).toEqual(false);
        expect(compareResult2).toEqual(false);
      });

      it('should return false if primaryKey matches', () => {
        const entity1 = { id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' };
        const entity2 = { id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' };

        const compareResult1 = service.compareAlias(entity1, entity2);
        const compareResult2 = service.compareAlias(entity2, entity1);

        expect(compareResult1).toEqual(true);
        expect(compareResult2).toEqual(true);
      });
    });
  });

  afterEach(() => {
    httpMock.verify();
  });
});
