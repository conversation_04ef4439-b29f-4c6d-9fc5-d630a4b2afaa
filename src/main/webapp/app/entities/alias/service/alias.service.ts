import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, asapScheduler, scheduled } from 'rxjs';

import { catchError } from 'rxjs/operators';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { Search } from 'app/core/request/request.model';
import { IAlias, NewAlias } from '../alias.model';

export type PartialUpdateAlias = Partial<IAlias> & Pick<IAlias, 'id'>;

export type EntityResponseType = HttpResponse<IAlias>;
export type EntityArrayResponseType = HttpResponse<IAlias[]>;

@Injectable({ providedIn: 'root' })
export class AliasService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/aliases');
  protected resourceSearchUrl = this.applicationConfigService.getEndpointFor('api/aliases/_search');

  create(alias: NewAlias): Observable<EntityResponseType> {
    return this.http.post<IAlias>(this.resourceUrl, alias, { observe: 'response' });
  }

  update(alias: IAlias): Observable<EntityResponseType> {
    return this.http.put<IAlias>(`${this.resourceUrl}/${this.getAliasIdentifier(alias)}`, alias, { observe: 'response' });
  }

  partialUpdate(alias: PartialUpdateAlias): Observable<EntityResponseType> {
    return this.http.patch<IAlias>(`${this.resourceUrl}/${this.getAliasIdentifier(alias)}`, alias, { observe: 'response' });
  }

  find(id: string): Observable<EntityResponseType> {
    return this.http.get<IAlias>(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<IAlias[]>(this.resourceUrl, { params: options, observe: 'response' });
  }

  delete(id: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  search(req: Search): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<IAlias[]>(this.resourceSearchUrl, { params: options, observe: 'response' })
      .pipe(catchError(() => scheduled([new HttpResponse<IAlias[]>()], asapScheduler)));
  }

  getAliasIdentifier(alias: Pick<IAlias, 'id'>): string {
    return alias.id;
  }

  compareAlias(o1: Pick<IAlias, 'id'> | null, o2: Pick<IAlias, 'id'> | null): boolean {
    return o1 && o2 ? this.getAliasIdentifier(o1) === this.getAliasIdentifier(o2) : o1 === o2;
  }

  addAliasToCollectionIfMissing<Type extends Pick<IAlias, 'id'>>(
    aliasCollection: Type[],
    ...aliasesToCheck: (Type | null | undefined)[]
  ): Type[] {
    const aliases: Type[] = aliasesToCheck.filter(isPresent);
    if (aliases.length > 0) {
      const aliasCollectionIdentifiers = aliasCollection.map(aliasItem => this.getAliasIdentifier(aliasItem));
      const aliasesToAdd = aliases.filter(aliasItem => {
        const aliasIdentifier = this.getAliasIdentifier(aliasItem);
        if (aliasCollectionIdentifiers.includes(aliasIdentifier)) {
          return false;
        }
        aliasCollectionIdentifiers.push(aliasIdentifier);
        return true;
      });
      return [...aliasesToAdd, ...aliasCollection];
    }
    return aliasCollection;
  }
}
