import { TestBed } from '@angular/core/testing';

import { sampleWithNewData, sampleWithRequiredData } from '../alias.test-samples';

import { AliasFormService } from './alias-form.service';

describe('Alias Form Service', () => {
  let service: AliasFormService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(AliasFormService);
  });

  describe('Service methods', () => {
    describe('createAliasFormGroup', () => {
      it('should create a new form with FormControl', () => {
        const formGroup = service.createAliasFormGroup();

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            id: expect.any(Object),
            value: expect.any(Object),
            type: expect.any(Object),
          }),
        );
      });

      it('passing IAlias should create a new form with FormGroup', () => {
        const formGroup = service.createAliasFormGroup(sampleWithRequiredData);

        expect(formGroup.controls).toEqual(
          expect.objectContaining({
            id: expect.any(Object),
            value: expect.any(Object),
            type: expect.any(Object),
          }),
        );
      });
    });

    describe('getAlias', () => {
      it('should return NewAlias for default Alias initial value', () => {
        const formGroup = service.createAliasFormGroup(sampleWithNewData);

        const alias = service.getAlias(formGroup) as any;

        expect(alias).toMatchObject(sampleWithNewData);
      });

      it('should return NewAlias for empty Alias initial value', () => {
        const formGroup = service.createAliasFormGroup();

        const alias = service.getAlias(formGroup) as any;

        expect(alias).toMatchObject({});
      });

      it('should return IAlias', () => {
        const formGroup = service.createAliasFormGroup(sampleWithRequiredData);

        const alias = service.getAlias(formGroup) as any;

        expect(alias).toMatchObject(sampleWithRequiredData);
      });
    });

    describe('resetForm', () => {
      it('passing IAlias should not enable id FormControl', () => {
        const formGroup = service.createAliasFormGroup();
        expect(formGroup.controls.id.disabled).toBe(true);

        service.resetForm(formGroup, sampleWithRequiredData);

        expect(formGroup.controls.id.disabled).toBe(true);
      });

      it('passing NewAlias should disable id FormControl', () => {
        const formGroup = service.createAliasFormGroup(sampleWithRequiredData);
        expect(formGroup.controls.id.disabled).toBe(true);

        service.resetForm(formGroup, { id: null });

        expect(formGroup.controls.id.disabled).toBe(true);
      });
    });
  });
});
