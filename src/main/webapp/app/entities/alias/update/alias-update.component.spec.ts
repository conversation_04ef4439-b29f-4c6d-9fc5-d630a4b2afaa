import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse, provideHttpClient } from '@angular/common/http';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Subject, from, of } from 'rxjs';

import { AliasService } from '../service/alias.service';
import { IAlias } from '../alias.model';
import { AliasFormService } from './alias-form.service';

import { AliasUpdateComponent } from './alias-update.component';

describe('Alias Management Update Component', () => {
  let comp: AliasUpdateComponent;
  let fixture: ComponentFixture<AliasUpdateComponent>;
  let activatedRoute: ActivatedRoute;
  let aliasFormService: AliasFormService;
  let aliasService: AliasService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AliasUpdateComponent],
      providers: [
        provideHttpClient(),
        FormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            params: from([{}]),
          },
        },
      ],
    })
      .overrideTemplate(AliasUpdateComponent, '')
      .compileComponents();

    fixture = TestBed.createComponent(AliasUpdateComponent);
    activatedRoute = TestBed.inject(ActivatedRoute);
    aliasFormService = TestBed.inject(AliasFormService);
    aliasService = TestBed.inject(AliasService);

    comp = fixture.componentInstance;
  });

  describe('ngOnInit', () => {
    it('should update editForm', () => {
      const alias: IAlias = { id: '0ce539f8-16c5-460a-a1c1-f8e9002e31e8' };

      activatedRoute.data = of({ alias });
      comp.ngOnInit();

      expect(comp.alias).toEqual(alias);
    });
  });

  describe('save', () => {
    it('should call update service on save for existing entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAlias>>();
      const alias = { id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' };
      jest.spyOn(aliasFormService, 'getAlias').mockReturnValue(alias);
      jest.spyOn(aliasService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ alias });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: alias }));
      saveSubject.complete();

      // THEN
      expect(aliasFormService.getAlias).toHaveBeenCalled();
      expect(comp.previousState).toHaveBeenCalled();
      expect(aliasService.update).toHaveBeenCalledWith(expect.objectContaining(alias));
      expect(comp.isSaving).toEqual(false);
    });

    it('should call create service on save for new entity', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAlias>>();
      const alias = { id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' };
      jest.spyOn(aliasFormService, 'getAlias').mockReturnValue({ id: null });
      jest.spyOn(aliasService, 'create').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ alias: null });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.next(new HttpResponse({ body: alias }));
      saveSubject.complete();

      // THEN
      expect(aliasFormService.getAlias).toHaveBeenCalled();
      expect(aliasService.create).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).toHaveBeenCalled();
    });

    it('should set isSaving to false on error', () => {
      // GIVEN
      const saveSubject = new Subject<HttpResponse<IAlias>>();
      const alias = { id: 'af1b3858-7a74-497a-9d44-fcc8195bd5db' };
      jest.spyOn(aliasService, 'update').mockReturnValue(saveSubject);
      jest.spyOn(comp, 'previousState');
      activatedRoute.data = of({ alias });
      comp.ngOnInit();

      // WHEN
      comp.save();
      expect(comp.isSaving).toEqual(true);
      saveSubject.error('This is an error!');

      // THEN
      expect(aliasService.update).toHaveBeenCalled();
      expect(comp.isSaving).toEqual(false);
      expect(comp.previousState).not.toHaveBeenCalled();
    });
  });
});
