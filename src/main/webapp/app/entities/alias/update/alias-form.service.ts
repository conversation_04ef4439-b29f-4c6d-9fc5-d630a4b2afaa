import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { IAlias, NewAlias } from '../alias.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { id: unknown }> = Partial<Omit<T, 'id'>> & { id: T['id'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts IAlias for edit and NewAliasFormGroupInput for create.
 */
type AliasFormGroupInput = IAlias | PartialWithRequiredKeyOf<NewAlias>;

type AliasFormDefaults = Pick<NewAlias, 'id'>;

type AliasFormGroupContent = {
  id: FormControl<IAlias['id'] | NewAlias['id']>;
  value: FormControl<IAlias['value']>;
  type: FormControl<IAlias['type']>;
};

export type AliasFormGroup = FormGroup<AliasFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class AliasFormService {
  createAliasFormGroup(alias: AliasFormGroupInput = { id: null }): AliasFormGroup {
    const aliasRawValue = {
      ...this.getFormDefaults(),
      ...alias,
    };
    return new FormGroup<AliasFormGroupContent>({
      id: new FormControl(
        { value: aliasRawValue.id, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      value: new FormControl(aliasRawValue.value),
      type: new FormControl(aliasRawValue.type),
    });
  }

  getAlias(form: AliasFormGroup): IAlias | NewAlias {
    return form.getRawValue() as IAlias | NewAlias;
  }

  resetForm(form: AliasFormGroup, alias: AliasFormGroupInput): void {
    const aliasRawValue = { ...this.getFormDefaults(), ...alias };
    form.reset(
      {
        ...aliasRawValue,
        id: { value: aliasRawValue.id, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): AliasFormDefaults {
    return {
      id: null,
    };
  }
}
