import { Component, OnInit, inject } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';

import SharedModule from 'app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { AliasType } from 'app/entities/enumerations/alias-type.model';
import { IAlias } from '../alias.model';
import { AliasService } from '../service/alias.service';
import { AliasFormGroup, AliasFormService } from './alias-form.service';

@Component({
  selector: 'jhi-alias-update',
  templateUrl: './alias-update.component.html',
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class AliasUpdateComponent implements OnInit {
  isSaving = false;
  alias: <PERSON><PERSON><PERSON>s | null = null;
  aliasTypeValues = Object.keys(AliasType);

  protected aliasService = inject(AliasService);
  protected aliasFormService = inject(AliasFormService);
  protected activatedRoute = inject(ActivatedRoute);

  // eslint-disable-next-line @typescript-eslint/member-ordering
  editForm: AliasFormGroup = this.aliasFormService.createAliasFormGroup();

  ngOnInit(): void {
    this.activatedRoute.data.subscribe(({ alias }) => {
      this.alias = alias;
      if (alias) {
        this.updateForm(alias);
      }
    });
  }

  previousState(): void {
    window.history.back();
  }

  save(): void {
    this.isSaving = true;
    const alias = this.aliasFormService.getAlias(this.editForm);
    if (alias.id !== null) {
      this.subscribeToSaveResponse(this.aliasService.update(alias));
    } else {
      this.subscribeToSaveResponse(this.aliasService.create(alias));
    }
  }

  protected subscribeToSaveResponse(result: Observable<HttpResponse<IAlias>>): void {
    result.pipe(finalize(() => this.onSaveFinalize())).subscribe({
      next: () => this.onSaveSuccess(),
      error: () => this.onSaveError(),
    });
  }

  protected onSaveSuccess(): void {
    this.previousState();
  }

  protected onSaveError(): void {
    // Api for inheritance.
  }

  protected onSaveFinalize(): void {
    this.isSaving = false;
  }

  protected updateForm(alias: IAlias): void {
    this.alias = alias;
    this.aliasFormService.resetForm(this.editForm, alias);
  }
}
