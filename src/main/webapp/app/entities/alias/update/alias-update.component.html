<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2 id="jhi-alias-heading" data-cy="AliasCreateUpdateHeading" jhiTranslate="forconversationsApp.alias.home.createOrEditLabel">
        Create or edit a Alias
      </h2>

      <div>
        <jhi-alert-error />

        @if (editForm.controls.id.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_id" jhiTranslate="forconversationsApp.alias.id">ID</label>
            <input type="text" class="form-control" name="id" id="field_id" data-cy="id" formControlName="id" [readonly]="true" />
          </div>
        }

        <div class="mb-3">
          <label class="form-label" for="field_value" jhiTranslate="forconversationsApp.alias.value">Value</label>
          <input type="text" class="form-control" name="value" id="field_value" data-cy="value" formControlName="value" />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_type" jhiTranslate="forconversationsApp.alias.type">Type</label>
          <select class="form-control" name="type" formControlName="type" id="field_type" data-cy="type">
            <option [ngValue]="null">{{ 'forconversationsApp.AliasType.null' | translate }}</option>
            @for (aliasType of aliasTypeValues; track $index) {
              <option [value]="aliasType">{{ 'forconversationsApp.AliasType.' + aliasType | translate }}</option>
            }
          </select>
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Save</span>
        </button>
      </div>
    </form>
  </div>
</div>
