import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import SharedModule from 'app/shared/shared.module';
import { ITEM_DELETED_EVENT } from 'app/config/navigation.constants';
import { IAlias } from '../alias.model';
import { AliasService } from '../service/alias.service';

@Component({
  templateUrl: './alias-delete-dialog.component.html',
  imports: [SharedModule, FormsModule],
})
export class AliasDeleteDialogComponent {
  alias?: IAlias;

  protected aliasService = inject(AliasService);
  protected activeModal = inject(NgbActiveModal);

  cancel(): void {
    this.activeModal.dismiss();
  }

  confirmDelete(id: string): void {
    this.aliasService.delete(id).subscribe(() => {
      this.activeModal.close(ITEM_DELETED_EVENT);
    });
  }
}
