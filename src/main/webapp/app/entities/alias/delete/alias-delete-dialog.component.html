@if (alias) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(alias.id!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="aliasDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirm delete operation</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error />
      <p id="jhi-delete-alias-heading" jhiTranslate="forconversationsApp.alias.delete.question" [translateValues]="{ id: alias.id }">
        Are you sure you want to delete Alias {{ alias.id }}?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
      </button>

      <button id="jhi-confirm-delete-alias" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Delete</span>
      </button>
    </div>
  </form>
}
