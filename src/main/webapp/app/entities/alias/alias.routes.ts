import { Routes } from '@angular/router';

import { UserRouteAccessService } from 'app/core/auth/user-route-access.service';
import AliasResolve from './route/alias-routing-resolve.service';

const aliasRoute: Routes = [
  {
    path: '',
    loadComponent: () => import('./list/alias.component').then(m => m.AliasComponent),
    data: {},
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/view',
    loadComponent: () => import('./detail/alias-detail.component').then(m => m.AliasDetailComponent),
    resolve: {
      alias: AliasResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: 'new',
    loadComponent: () => import('./update/alias-update.component').then(m => m.AliasUpdateComponent),
    resolve: {
      alias: AliasResolve,
    },
    canActivate: [UserRouteAccessService],
  },
  {
    path: ':id/edit',
    loadComponent: () => import('./update/alias-update.component').then(m => m.AliasUpdateComponent),
    resolve: {
      alias: AliasResolve,
    },
    canActivate: [UserRouteAccessService],
  },
];

export default aliasRoute;
