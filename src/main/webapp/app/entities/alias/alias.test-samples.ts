import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Alia<PERSON> } from './alias.model';

export const sampleWithRequiredData: IAlias = {
  id: '7ab073c3-488f-4af6-96cc-ee21d55b8e39',
};

export const sampleWithPartialData: IAlias = {
  id: '156fbd50-ec2e-4507-a304-6181e7b9535e',
  value: 'even boohoo willfully',
  type: 'WHATSAPP',
};

export const sampleWithFullData: IAlias = {
  id: '76e7f7f8-463a-4fd9-b3de-4945098275f7',
  value: 'unless warlike makeover',
  type: 'WHATSAPP',
};

export const sampleWithNewData: NewAlias = {
  id: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
