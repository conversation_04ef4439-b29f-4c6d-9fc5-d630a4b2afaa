import dayjs from 'dayjs/esm';

import { ISource, NewSource } from './source.model';

export const sampleWithRequiredData: ISource = {
  id: '3dc9eb47-7844-4cc3-adb4-160d25b0ef9a',
  time: dayjs('2025-06-07T22:01'),
  messages: '../fake-data/blob/hipster.txt',
  type: 'WHATSAPP',
  sourceId: 'uh-huh',
};

export const sampleWithPartialData: ISource = {
  id: 'd23c2590-202c-45f0-ac25-20e636c1cdea',
  time: dayjs('2025-06-08T05:04'),
  messages: '../fake-data/blob/hipster.txt',
  type: 'EMAIL',
  sourceId: 'galvanize broadside',
};

export const sampleWithFullData: ISource = {
  id: '545cc025-a79e-4f3c-ae72-cd4368403654',
  time: dayjs('2025-06-08T10:47'),
  messages: '../fake-data/blob/hipster.txt',
  type: 'EMAIL',
  sourceId: 'supplier',
};

export const sampleWithNewData: NewSource = {
  time: dayjs('2025-06-08T11:09'),
  messages: '../fake-data/blob/hipster.txt',
  type: 'AUDIO',
  sourceId: 'pish following',
  id: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
