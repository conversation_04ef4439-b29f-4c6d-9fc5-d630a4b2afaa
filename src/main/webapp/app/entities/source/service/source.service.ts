import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable, asapScheduler, map, scheduled } from 'rxjs';

import { catchError } from 'rxjs/operators';

import dayjs from 'dayjs/esm';

import { isPresent } from 'app/core/util/operators';
import { ApplicationConfigService } from 'app/core/config/application-config.service';
import { createRequestOption } from 'app/core/request/request-util';
import { Search } from 'app/core/request/request.model';
import { ISource, NewSource } from '../source.model';

export type PartialUpdateSource = Partial<ISource> & Pick<ISource, 'id'>;

type RestOf<T extends ISource | NewSource> = Omit<T, 'time'> & {
  time?: string | null;
};

export type RestSource = RestOf<ISource>;

export type NewRestSource = RestOf<NewSource>;

export type PartialUpdateRestSource = RestOf<PartialUpdateSource>;

export type EntityResponseType = HttpResponse<ISource>;
export type EntityArrayResponseType = HttpResponse<ISource[]>;

@Injectable({ providedIn: 'root' })
export class SourceService {
  protected readonly http = inject(HttpClient);
  protected readonly applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('api/sources');
  protected resourceSearchUrl = this.applicationConfigService.getEndpointFor('api/sources/_search');

  create(source: NewSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(source);
    return this.http
      .post<RestSource>(this.resourceUrl, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  update(source: ISource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(source);
    return this.http
      .put<RestSource>(`${this.resourceUrl}/${this.getSourceIdentifier(source)}`, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  partialUpdate(source: PartialUpdateSource): Observable<EntityResponseType> {
    const copy = this.convertDateFromClient(source);
    return this.http
      .patch<RestSource>(`${this.resourceUrl}/${this.getSourceIdentifier(source)}`, copy, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  find(id: string): Observable<EntityResponseType> {
    return this.http
      .get<RestSource>(`${this.resourceUrl}/${id}`, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }

  query(req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http
      .get<RestSource[]>(this.resourceUrl, { params: options, observe: 'response' })
      .pipe(map(res => this.convertResponseArrayFromServer(res)));
  }

  delete(id: string): Observable<HttpResponse<{}>> {
    return this.http.delete(`${this.resourceUrl}/${id}`, { observe: 'response' });
  }

  search(req: Search): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.get<RestSource[]>(this.resourceSearchUrl, { params: options, observe: 'response' }).pipe(
      map(res => this.convertResponseArrayFromServer(res)),

      catchError(() => scheduled([new HttpResponse<ISource[]>()], asapScheduler)),
    );
  }

  getSourceIdentifier(source: Pick<ISource, 'id'>): string {
    return source.id;
  }

  compareSource(o1: Pick<ISource, 'id'> | null, o2: Pick<ISource, 'id'> | null): boolean {
    return o1 && o2 ? this.getSourceIdentifier(o1) === this.getSourceIdentifier(o2) : o1 === o2;
  }

  addSourceToCollectionIfMissing<Type extends Pick<ISource, 'id'>>(
    sourceCollection: Type[],
    ...sourcesToCheck: (Type | null | undefined)[]
  ): Type[] {
    const sources: Type[] = sourcesToCheck.filter(isPresent);
    if (sources.length > 0) {
      const sourceCollectionIdentifiers = sourceCollection.map(sourceItem => this.getSourceIdentifier(sourceItem));
      const sourcesToAdd = sources.filter(sourceItem => {
        const sourceIdentifier = this.getSourceIdentifier(sourceItem);
        if (sourceCollectionIdentifiers.includes(sourceIdentifier)) {
          return false;
        }
        sourceCollectionIdentifiers.push(sourceIdentifier);
        return true;
      });
      return [...sourcesToAdd, ...sourceCollection];
    }
    return sourceCollection;
  }

  protected convertDateFromClient<T extends ISource | NewSource | PartialUpdateSource>(source: T): RestOf<T> {
    return {
      ...source,
      time: source.time?.toJSON() ?? null,
    };
  }

  protected convertDateFromServer(restSource: RestSource): ISource {
    return {
      ...restSource,
      time: restSource.time ? dayjs(restSource.time) : undefined,
    };
  }

  protected convertResponseFromServer(res: HttpResponse<RestSource>): HttpResponse<ISource> {
    return res.clone({
      body: res.body ? this.convertDateFromServer(res.body) : null,
    });
  }

  protected convertResponseArrayFromServer(res: HttpResponse<RestSource[]>): HttpResponse<ISource[]> {
    return res.clone({
      body: res.body ? res.body.map(item => this.convertDateFromServer(item)) : null,
    });
  }
}
