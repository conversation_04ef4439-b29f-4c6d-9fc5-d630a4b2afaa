<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2 id="jhi-source-heading" data-cy="SourceCreateUpdateHeading" jhiTranslate="forconversationsApp.source.home.createOrEditLabel">
        Create or edit a Source
      </h2>

      <div>
        <jhi-alert-error />

        @if (editForm.controls.id.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_id" jhiTranslate="forconversationsApp.source.id">ID</label>
            <input type="text" class="form-control" name="id" id="field_id" data-cy="id" formControlName="id" [readonly]="true" />
          </div>
        }

        @let timeRef = editForm.get('time')!;
        <div class="mb-3">
          <label class="form-label" for="field_time" jhiTranslate="forconversationsApp.source.time">Time</label>
          <div class="d-flex">
            <input
              id="field_time"
              data-cy="time"
              type="datetime-local"
              class="form-control"
              name="time"
              formControlName="time"
              placeholder="YYYY-MM-DD HH:mm"
            />
          </div>
          @if (timeRef.invalid && (timeRef.dirty || timeRef.touched)) {
            <div>
              @if (editForm.get('time')?.errors?.required) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
              }
              <small
                class="form-text text-danger"
                [hidden]="!editForm.get('time')?.errors?.datetimelocal"
                jhiTranslate="entity.validation.datetimelocal"
                >This field should be a date and time.</small
              >
            </div>
          }
        </div>

        @let messagesRef = editForm.get('messages')!;
        <div class="mb-3">
          <label class="form-label" for="field_messages" jhiTranslate="forconversationsApp.source.messages">Messages</label>
          <textarea class="form-control" name="messages" id="field_messages" data-cy="messages" formControlName="messages"></textarea>
          @if (messagesRef.invalid && (messagesRef.dirty || messagesRef.touched)) {
            <div>
              @if (editForm.get('messages')?.errors?.required) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
              }
            </div>
          }
        </div>

        @let typeRef = editForm.get('type')!;
        <div class="mb-3">
          <label class="form-label" for="field_type" jhiTranslate="forconversationsApp.source.type">Type</label>
          <select class="form-control" name="type" formControlName="type" id="field_type" data-cy="type">
            <option [ngValue]="null">{{ 'forconversationsApp.SourceType.null' | translate }}</option>
            @for (sourceType of sourceTypeValues; track $index) {
              <option [value]="sourceType">{{ 'forconversationsApp.SourceType.' + sourceType | translate }}</option>
            }
          </select>
          @if (typeRef.invalid && (typeRef.dirty || typeRef.touched)) {
            <div>
              @if (editForm.get('type')?.errors?.required) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
              }
            </div>
          }
        </div>

        @let sourceIdRef = editForm.get('sourceId')!;
        <div class="mb-3">
          <label class="form-label" for="field_sourceId" jhiTranslate="forconversationsApp.source.sourceId">Source Id</label>
          <input type="text" class="form-control" name="sourceId" id="field_sourceId" data-cy="sourceId" formControlName="sourceId" />
          @if (sourceIdRef.invalid && (sourceIdRef.dirty || sourceIdRef.touched)) {
            <div>
              @if (editForm.get('sourceId')?.errors?.required) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
              }
            </div>
          }
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Save</span>
        </button>
      </div>
    </form>
  </div>
</div>
