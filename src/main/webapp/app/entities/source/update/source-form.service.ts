import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import dayjs from 'dayjs/esm';
import { DATE_TIME_FORMAT } from 'app/config/input.constants';
import { ISource, NewSource } from '../source.model';

/**
 * A partial Type with required key is used as form input.
 */
type PartialWithRequiredKeyOf<T extends { id: unknown }> = Partial<Omit<T, 'id'>> & { id: T['id'] };

/**
 * Type for createFormGroup and resetForm argument.
 * It accepts ISource for edit and NewSourceFormGroupInput for create.
 */
type SourceFormGroupInput = ISource | PartialWithRequiredKeyOf<NewSource>;

/**
 * Type that converts some properties for forms.
 */
type FormValueOf<T extends ISource | NewSource> = Omit<T, 'time'> & {
  time?: string | null;
};

type SourceFormRawValue = FormValueOf<ISource>;

type NewSourceFormRawValue = FormValueOf<NewSource>;

type SourceFormDefaults = Pick<NewSource, 'id' | 'time'>;

type SourceFormGroupContent = {
  id: FormControl<SourceFormRawValue['id'] | NewSource['id']>;
  time: FormControl<SourceFormRawValue['time']>;
  messages: FormControl<SourceFormRawValue['messages']>;
  type: FormControl<SourceFormRawValue['type']>;
  sourceId: FormControl<SourceFormRawValue['sourceId']>;
};

export type SourceFormGroup = FormGroup<SourceFormGroupContent>;

@Injectable({ providedIn: 'root' })
export class SourceFormService {
  createSourceFormGroup(source: SourceFormGroupInput = { id: null }): SourceFormGroup {
    const sourceRawValue = this.convertSourceToSourceRawValue({
      ...this.getFormDefaults(),
      ...source,
    });
    return new FormGroup<SourceFormGroupContent>({
      id: new FormControl(
        { value: sourceRawValue.id, disabled: true },
        {
          nonNullable: true,
          validators: [Validators.required],
        },
      ),
      time: new FormControl(sourceRawValue.time, {
        validators: [Validators.required],
      }),
      messages: new FormControl(sourceRawValue.messages, {
        validators: [Validators.required],
      }),
      type: new FormControl(sourceRawValue.type, {
        validators: [Validators.required],
      }),
      sourceId: new FormControl(sourceRawValue.sourceId, {
        validators: [Validators.required],
      }),
    });
  }

  getSource(form: SourceFormGroup): ISource | NewSource {
    return this.convertSourceRawValueToSource(form.getRawValue() as SourceFormRawValue | NewSourceFormRawValue);
  }

  resetForm(form: SourceFormGroup, source: SourceFormGroupInput): void {
    const sourceRawValue = this.convertSourceToSourceRawValue({ ...this.getFormDefaults(), ...source });
    form.reset(
      {
        ...sourceRawValue,
        id: { value: sourceRawValue.id, disabled: true },
      } as any /* cast to workaround https://github.com/angular/angular/issues/46458 */,
    );
  }

  private getFormDefaults(): SourceFormDefaults {
    const currentTime = dayjs();

    return {
      id: null,
      time: currentTime,
    };
  }

  private convertSourceRawValueToSource(rawSource: SourceFormRawValue | NewSourceFormRawValue): ISource | NewSource {
    return {
      ...rawSource,
      time: dayjs(rawSource.time, DATE_TIME_FORMAT),
    };
  }

  private convertSourceToSourceRawValue(
    source: ISource | (Partial<NewSource> & SourceFormDefaults),
  ): SourceFormRawValue | PartialWithRequiredKeyOf<NewSourceFormRawValue> {
    return {
      ...source,
      time: source.time ? source.time.format(DATE_TIME_FORMAT) : undefined,
    };
  }
}
