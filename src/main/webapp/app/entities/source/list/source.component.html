<div>
  <h2 id="page-heading" data-cy="SourceHeading">
    <span jhiTranslate="forconversationsApp.source.home.title">Sources</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="forconversationsApp.source.home.refreshListLabel">Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-source"
        [routerLink]="['/source/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span class="hidden-sm-down" jhiTranslate="forconversationsApp.source.home.createLabel">Create a new Source</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error />

  <jhi-alert />

  <form name="searchForm" class="row row-cols-sm-auto align-items-center">
    <div class="col-sm-12">
      <div class="input-group w-100 mt-3">
        <label class="visually-hidden" for="currentSearch" jhiTranslate="forconversationsApp.source.home.search">Search for Source</label>
        <input
          type="text"
          class="form-control"
          [(ngModel)]="currentSearch"
          id="currentSearch"
          name="currentSearch"
          placeholder="{{ 'forconversationsApp.source.home.search' | translate }}"
        />

        <button class="btn btn-info" (click)="search(currentSearch)">
          <fa-icon icon="search"></fa-icon>
        </button>

        @if (currentSearch) {
          <button class="btn btn-danger" (click)="search('')">
            <fa-icon icon="trash-alt"></fa-icon>
          </button>
        }
      </div>
    </div>
  </form>

  @if (sources().length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="forconversationsApp.source.home.notFound">No Sources found</span>
    </div>
  } @else {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [(sortState)]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="id">
              <div class="d-flex">
                <span jhiTranslate="global.field.id">ID</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="time">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.source.time">Time</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="messages">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.source.messages">Messages</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="type">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.source.type">Type</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="sourceId">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.source.sourceId">Source Id</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (source of sources(); track trackId(source)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/source', source.id, 'view']">{{ source.id }}</a>
              </td>
              <td>{{ source.time | formatMediumDatetime }}</td>
              <td>{{ source.messages }}</td>
              <td [jhiTranslate]="'forconversationsApp.SourceType.' + (source.type ?? 'null')">
                {{ { null: '', WHATSAPP: 'WHATSAPP', AUDIO: 'AUDIO', EMAIL: 'EMAIL' }[source.type ?? 'null'] }}
              </td>
              <td>{{ source.sourceId }}</td>
              <td class="text-end">
                <div class="btn-group">
                  <a [routerLink]="['/source', source.id, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">View</span>
                  </a>

                  <a [routerLink]="['/source', source.id, 'edit']" class="btn btn-primary btn-sm" data-cy="entityEditButton">
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Edit</span>
                  </a>

                  <button type="submit" (click)="delete(source)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Delete</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
</div>
