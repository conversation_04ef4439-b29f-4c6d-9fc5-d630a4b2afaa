@if (source) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(source.id!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="sourceDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirm delete operation</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error />
      <p id="jhi-delete-source-heading" jhiTranslate="forconversationsApp.source.delete.question" [translateValues]="{ id: source.id }">
        Are you sure you want to delete Source {{ source.id }}?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
      </button>

      <button id="jhi-confirm-delete-source" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Delete</span>
      </button>
    </div>
  </form>
}
