<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (source(); as sourceRef) {
      <div>
        <h2 data-cy="sourceDetailsHeading"><span jhiTranslate="forconversationsApp.source.detail.title">Source</span></h2>

        <hr />

        <jhi-alert-error />

        <jhi-alert />

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.id">ID</span></dt>
          <dd>
            <span>{{ sourceRef.id }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.source.time">Time</span></dt>
          <dd>
            <span>{{ sourceRef.time | formatMediumDatetime }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.source.messages">Messages</span></dt>
          <dd>
            <span>{{ sourceRef.messages }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.source.type">Type</span></dt>
          <dd>
            <span [jhiTranslate]="'forconversationsApp.SourceType.' + (sourceRef.type ?? 'null')">{{
              { null: '', WHATSAPP: 'WHATSAPP', AUDIO: 'AUDIO', EMAIL: 'EMAIL' }[sourceRef.type ?? 'null']
            }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.source.sourceId">Source Id</span></dt>
          <dd>
            <span>{{ sourceRef.sourceId }}</span>
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Back</span>
        </button>

        <button type="button" [routerLink]="['/source', sourceRef.id, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Edit</span>
        </button>
      </div>
    }
  </div>
</div>
