import dayjs from 'dayjs/esm';

import { IMessage, NewMessage } from './message.model';

export const sampleWithRequiredData: IMessage = {
  id: '42bfda03-0f8b-4ea3-a0e7-520813ec664a',
  time: dayjs('2025-06-08T10:54'),
  sender: 'uncork',
  recipient: 'until',
  content: 'verbally animated motor',
  type: 'AUDIO',
};

export const sampleWithPartialData: IMessage = {
  id: '0e3af454-266b-4007-87fa-17e609fd9603',
  time: dayjs('2025-06-08T05:33'),
  sender: 'yum',
  recipient: 'um',
  content: 'creaking lighthearted unto',
  type: 'EMAIL',
};

export const sampleWithFullData: IMessage = {
  id: '1dbe3ed3-49c7-43ac-8029-cff4cc208105',
  time: dayjs('2025-06-08T13:06'),
  sender: 'little',
  recipient: 'purple',
  content: 'besides',
  type: 'WHATSAPP',
};

export const sampleWithNewData: NewMessage = {
  time: dayjs('2025-06-08T12:02'),
  sender: 'broadly gracious',
  recipient: 'hence',
  content: 'sunny absentmindedly reach',
  type: 'AUDIO',
  id: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
