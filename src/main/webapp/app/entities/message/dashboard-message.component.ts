import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faFaceSmile, faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
import { Feeling, IMessage } from './dashboard-message-model';
import { FeelingComponent } from '../feeling/feeling.component';

@Component({
  selector: 'jhi-dashboard-message',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule, FeelingComponent],
  templateUrl: './dashboard-message.component.html',
  styleUrls: ['./dashboard-message.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardMessageComponent {
  @Input({ required: true }) message!: IMessage;
  @Input() actionType: 'add' | 'remove' | null = null;
  @Output() actionClicked = new EventEmitter<IMessage>();

  faFaceSmile = faFaceSmile;
  faPlus = faPlus;
  faMinus = faMinus;

  showFeelingSelector = signal(false);
  isLoading = signal(false); // Keep isLoading for the button

  toggleFeelingSelector(): void {
    this.showFeelingSelector.set(!this.showFeelingSelector());
  }

  onFeelingsChanged(feelings: Feeling[]): void {
    if (this.message) {
      this.message.feelingList = feelings;
    }
  }

  hasAnyFeelings(): boolean {
    return !!(this.message.feelingList && this.message.feelingList.length > 0);
  }

  onActionClick(): void {
    this.actionClicked.emit(this.message);
  }
}
