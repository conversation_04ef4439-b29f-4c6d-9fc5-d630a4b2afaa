<div>
  <h2 id="page-heading" data-cy="MessageHeading">
    <span jhiTranslate="forconversationsApp.message.home.title">Messages</span>

    <div class="d-flex justify-content-between align-items-center mt-3">
      <!-- Upload Buttons Section -->
      <div class="upload-actions">
        <h6 class="text-muted mb-2"><PERSON>gar conversaciones:</h6>
        <fieldset class="btn-group gap-2">
          <jhi-whatsapp-message-source-button></jhi-whatsapp-message-source-button>
          <jhi-audio-message-source-button></jhi-audio-message-source-button>
          <jhi-email-message-source-button></jhi-email-message-source-button>
        </fieldset>
      </div>

      <!-- Action Buttons Section -->
      <div class="action-buttons d-flex gap-2">
        <button class="btn btn-outline-secondary" (click)="load()" [disabled]="isLoading">
          <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
          <span class="ms-2" jhiTranslate="forconversationsApp.message.home.refreshListLabel">Refresh list</span>
        </button>

        <button id="jh-create-entity" data-cy="entityCreateButton" class="btn btn-primary" [routerLink]="['/message/new']">
          <fa-icon icon="plus"></fa-icon>
          <span class="ms-2 hidden-sm-down" jhiTranslate="forconversationsApp.message.home.createLabel">Create a new Message</span>
        </button>
      </div>
    </div>
  </h2>

  <jhi-alert-error />

  <jhi-alert />

  <form name="searchForm" class="row row-cols-sm-auto align-items-center">
    <div class="col-sm-12">
      <div class="input-group w-100 mt-3">
        <label class="visually-hidden" for="currentSearch" jhiTranslate="forconversationsApp.message.home.search">Search for Message</label>
        <input
          type="text"
          class="form-control"
          [(ngModel)]="currentSearch"
          id="currentSearch"
          name="currentSearch"
          placeholder="{{ 'forconversationsApp.message.home.search' | translate }}"
        />

        <button class="btn btn-info" (click)="search(currentSearch)">
          <fa-icon icon="search"></fa-icon>
        </button>

        @if (currentSearch) {
          <button class="btn btn-danger" (click)="search('')">
            <fa-icon icon="trash-alt"></fa-icon>
          </button>
        }
      </div>
    </div>
  </form>

  @if (messages().length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="forconversationsApp.message.home.notFound">No Messages found</span>
    </div>
  } @else {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [(sortState)]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="id">
              <div class="d-flex">
                <span jhiTranslate="global.field.id">ID</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="time">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.message.time">Time</span>

                <fa-icon class="p-1" icon="sort"></fa-icon>
              </div>
            </th>
            <th scope="col" jhiSortBy="sender">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.message.sender">Sender</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="recipient">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.message.recipient">recipient</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="content">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.message.content">Content</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="type">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.message.type">Type</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (message of messages(); track trackId(message)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/message', message.id, 'view']">{{ message.id }}</a>
              </td>
              <td>{{ message.time | formatMediumDatetime }}</td>
              <td>{{ message.sender }}</td>
              <td>{{ message.recipient }}</td>
              <td>{{ message.content }}</td>
              <td [jhiTranslate]="'forconversationsApp.SourceType.' + (message.type ?? 'null')">
                {{ { null: '', WHATSAPP: 'WHATSAPP', AUDIO: 'AUDIO', EMAIL: 'EMAIL' }[message.type ?? 'null'] }}
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <a [routerLink]="['/message', message.id, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">View</span>
                  </a>

                  <a [routerLink]="['/message', message.id, 'edit']" class="btn btn-primary btn-sm" data-cy="entityEditButton">
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Edit</span>
                  </a>

                  <button type="submit" (click)="delete(message)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Delete</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
</div>
