<div class="compact-msg" *ngIf="message">
  <div class="msg-header">
    <span class="msg-time">{{ message.time?.toDate() | date: 'HH:mm' }}</span>
    <span class="msg-sender">{{ message.sender }}</span>
    <span class="msg-arrow">→</span>
    <span class="msg-recipient">{{ message.recipient }}</span>
    <div class="msg-actions">
      <button class="btn btn-sm btn-outline-secondary" (click)="toggleFeelingSelector()">
        <fa-icon [icon]="faFaceSmile"></fa-icon>
      </button>
      @if (hasAnyFeelings()) {
        <div class="feelings-display">
          <span *ngFor="let feeling of message.feelingList" class="badge bg-info me-1">{{ feeling }}</span>
        </div>
      }
    </div>
  </div>
  

  <div class="msg-content">{{ message.content }}</div>

  <!-- Feeling Selector Modal -->
  @if (showFeelingSelector()) {
    <jhi-feeling-selector
      [messageId]="message.id!"
      [initialFeelings]="message.feelingList ?? []"
      (feelingsChange)="onFeelingsChanged($event)"
      (closeSelector)="toggleFeelingSelector()"
    ></jhi-feeling-selector>
  }
</div>