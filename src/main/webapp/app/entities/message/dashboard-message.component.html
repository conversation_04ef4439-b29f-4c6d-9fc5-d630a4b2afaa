@if (message) {
  <div class="msg-header">
    <span class="msg-time">{{ message.time?.toDate() | date: 'yy-MM-dd HH:mm' }}</span>
    <span class="msg-sender">{{ message.sender }}</span>
    <span class="msg-arrow">→</span>
    <span class="msg-recipient">{{ message.recipient }}</span>

    <!-- Use the FeelingComponent directly -->
    <jhi-feeling-selector [message]="message"></jhi-feeling-selector>
  </div>

  <div class="msg-content">{{ message.content }}</div>
}
