:host {
  display: block;
  margin: 0;
}

.compact-msg {
  background: white;
  
  border-radius: 4px;
  padding: 8px 12px;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.3;
  position: relative;

  &:hover {
    background: #f8f9fa;
  }
}

.msg-header {
  display: flex;
  align-items: center;
  justify-content: space-between; /* Distribuye el espacio entre los elementos */
  margin-bottom: 4px;
  font-size: 0.8rem;
  color: #666;

  .msg-actions {
    display: flex;
    align-items: center;
    gap: 4px;

    .feelings-display {
      display: flex;
      gap: 2px;
    }
  }
}

.msg-time,
.msg-sender,
.msg-arrow,
.msg-recipient {
  /* Asegura que estos elementos no se estiren y permitan que msg-actions se mueva a la derecha */
  flex-shrink: 0;
}

.msg-time {
  font-weight: 500;
  color: #888;
}

.msg-sender {
  font-weight: 600;
  color: #1e3c72;
}

.msg-arrow {
  color: #999;
}

.msg-recipient {
  color: #666;
}

.msg-content {
  color: #333;
  margin: 0;
  padding: 0;
}

// Responsive design
@media (max-width: 768px) {
  .msg-header {
    flex-wrap: wrap;
    gap: 4px;

    .msg-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }
}