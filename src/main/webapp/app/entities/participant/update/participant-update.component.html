<div class="d-flex justify-content-center">
  <div class="col-8">
    <form name="editForm" novalidate (ngSubmit)="save()" [formGroup]="editForm">
      <h2
        id="jhi-participant-heading"
        data-cy="ParticipantCreateUpdateHeading"
        jhiTranslate="forconversationsApp.participant.home.createOrEditLabel"
      >
        Create or edit a Participant
      </h2>

      <div>
        <jhi-alert-error />

        @if (editForm.controls.id.value !== null) {
          <div class="mb-3">
            <label class="form-label" for="field_id" jhiTranslate="forconversationsApp.participant.id">ID</label>
            <input type="text" class="form-control" name="id" id="field_id" data-cy="id" formControlName="id" [readonly]="true" />
          </div>
        }

        @let nameRef = editForm.get('name')!;
        <div class="mb-3">
          <label class="form-label" for="field_name" jhiTranslate="forconversationsApp.participant.name">Name</label>
          <input type="text" class="form-control" name="name" id="field_name" data-cy="name" formControlName="name" />
          @if (nameRef.invalid && (nameRef.dirty || nameRef.touched)) {
            <div>
              @if (editForm.get('name')?.errors?.required) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
              }
            </div>
          }
        </div>

        @let surnameRef = editForm.get('surname')!;
        <div class="mb-3">
          <label class="form-label" for="field_surname" jhiTranslate="forconversationsApp.participant.surname">Surname</label>
          <input type="text" class="form-control" name="surname" id="field_surname" data-cy="surname" formControlName="surname" />
          @if (surnameRef.invalid && (surnameRef.dirty || surnameRef.touched)) {
            <div>
              @if (editForm.get('surname')?.errors?.required) {
                <small class="form-text text-danger" jhiTranslate="entity.validation.required">This field is required.</small>
              }
            </div>
          }
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_secondSurname" jhiTranslate="forconversationsApp.participant.secondSurname"
            >Second Surname</label
          >
          <input
            type="text"
            class="form-control"
            name="secondSurname"
            id="field_secondSurname"
            data-cy="secondSurname"
            formControlName="secondSurname"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_emailContact" jhiTranslate="forconversationsApp.participant.emailContact"
            >Email Contact</label
          >
          <input
            type="text"
            class="form-control"
            name="emailContact"
            id="field_emailContact"
            data-cy="emailContact"
            formControlName="emailContact"
          />
        </div>

        <div class="mb-3">
          <label class="form-label" for="field_mobileContact" jhiTranslate="forconversationsApp.participant.mobileContact"
            >Mobile Contact</label
          >
          <input
            type="text"
            class="form-control"
            name="mobileContact"
            id="field_mobileContact"
            data-cy="mobileContact"
            formControlName="mobileContact"
          />
        </div>
      </div>

      <div>
        <button type="button" id="cancel-save" data-cy="entityCreateCancelButton" class="btn btn-secondary" (click)="previousState()">
          <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
        </button>

        <button
          type="submit"
          id="save-entity"
          data-cy="entityCreateSaveButton"
          [disabled]="editForm.invalid || isSaving"
          class="btn btn-primary"
        >
          <fa-icon icon="save"></fa-icon>&nbsp;<span jhiTranslate="entity.action.save">Save</span>
        </button>
      </div>
    </form>
  </div>
</div>
