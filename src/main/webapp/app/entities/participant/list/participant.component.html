<div>
  <h2 id="page-heading" data-cy="ParticipantHeading">
    <span jhiTranslate="forconversationsApp.participant.home.title">Participants</span>

    <div class="d-flex justify-content-end">
      <button class="btn btn-info me-2" (click)="load()" [disabled]="isLoading">
        <fa-icon icon="sync" [animation]="isLoading ? 'spin' : undefined"></fa-icon>
        <span jhiTranslate="forconversationsApp.participant.home.refreshListLabel">Refresh list</span>
      </button>

      <button
        id="jh-create-entity"
        data-cy="entityCreateButton"
        class="btn btn-primary jh-create-entity create-participant"
        [routerLink]="['/participant/new']"
      >
        <fa-icon icon="plus"></fa-icon>
        <span class="hidden-sm-down" jhiTranslate="forconversationsApp.participant.home.createLabel">Create a new Participant</span>
      </button>
    </div>
  </h2>

  <jhi-alert-error />

  <jhi-alert />

  <form name="searchForm" class="row row-cols-sm-auto align-items-center">
    <div class="col-sm-12">
      <div class="input-group w-100 mt-3">
        <label class="visually-hidden" for="currentSearch" jhiTranslate="forconversationsApp.participant.home.search"
          >Search for Participant</label
        >
        <input
          type="text"
          class="form-control"
          [(ngModel)]="currentSearch"
          id="currentSearch"
          name="currentSearch"
          placeholder="{{ 'forconversationsApp.participant.home.search' | translate }}"
        />

        <button class="btn btn-info" (click)="search(currentSearch)">
          <fa-icon icon="search"></fa-icon>
        </button>

        @if (currentSearch) {
          <button class="btn btn-danger" (click)="search('')">
            <fa-icon icon="trash-alt"></fa-icon>
          </button>
        }
      </div>
    </div>
  </form>

  @if (participants().length === 0) {
    <div class="alert alert-warning" id="no-result">
      <span jhiTranslate="forconversationsApp.participant.home.notFound">No Participants found</span>
    </div>
  } @else {
    <div class="table-responsive table-entities" id="entities">
      <table class="table table-striped" aria-describedby="page-heading">
        <thead>
          <tr jhiSort [(sortState)]="sortState" (sortChange)="navigateToWithComponentValues($event)">
            <th scope="col" jhiSortBy="id">
              <div class="d-flex">
                <span jhiTranslate="global.field.id">ID</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="name">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.participant.name">Name</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="surname">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.participant.surname">Surname</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="secondSurname">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.participant.secondSurname">Second Surname</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="emailContact">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.participant.emailContact">Email Contact</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col" jhiSortBy="mobileContact">
              <div class="d-flex">
                <span jhiTranslate="forconversationsApp.participant.mobileContact">Mobile Contact</span>
                @if (!currentSearch) {
                  <fa-icon class="p-1" icon="sort"></fa-icon>
                }
              </div>
            </th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          @for (participant of participants(); track trackId(participant)) {
            <tr data-cy="entityTable">
              <td>
                <a [routerLink]="['/participant', participant.id, 'view']">{{ participant.id }}</a>
              </td>
              <td>{{ participant.name }}</td>
              <td>{{ participant.surname }}</td>
              <td>{{ participant.secondSurname }}</td>
              <td>{{ participant.emailContact }}</td>
              <td>{{ participant.mobileContact }}</td>
              <td class="text-end">
                <div class="btn-group">
                  <a [routerLink]="['/participant', participant.id, 'view']" class="btn btn-info btn-sm" data-cy="entityDetailsButton">
                    <fa-icon icon="eye"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.view">View</span>
                  </a>

                  <a [routerLink]="['/participant', participant.id, 'edit']" class="btn btn-primary btn-sm" data-cy="entityEditButton">
                    <fa-icon icon="pencil-alt"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.edit">Edit</span>
                  </a>

                  <button type="submit" (click)="delete(participant)" class="btn btn-danger btn-sm" data-cy="entityDeleteButton">
                    <fa-icon icon="times"></fa-icon>
                    <span class="d-none d-md-inline" jhiTranslate="entity.action.delete">Delete</span>
                  </button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
</div>
