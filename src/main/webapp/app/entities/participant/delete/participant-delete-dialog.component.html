@if (participant) {
  <form name="deleteForm" (ngSubmit)="confirmDelete(participant.id!)">
    <div class="modal-header">
      <h4 class="modal-title" data-cy="participantDeleteDialogHeading" jhiTranslate="entity.delete.title">Confirm delete operation</h4>
    </div>

    <div class="modal-body">
      <jhi-alert-error />
      <p
        id="jhi-delete-participant-heading"
        jhiTranslate="forconversationsApp.participant.delete.question"
        [translateValues]="{ id: participant.id }"
      >
        Are you sure you want to delete Participant {{ participant.id }}?
      </p>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="cancel()">
        <fa-icon icon="ban"></fa-icon>&nbsp;<span jhiTranslate="entity.action.cancel">Cancel</span>
      </button>

      <button id="jhi-confirm-delete-participant" data-cy="entityConfirmDeleteButton" type="submit" class="btn btn-danger">
        <fa-icon icon="times"></fa-icon>&nbsp;<span jhiTranslate="entity.action.delete">Delete</span>
      </button>
    </div>
  </form>
}
