import { IParticipant, NewParticipant } from './participant.model';

export const sampleWithRequiredData: IParticipant = {
  id: '316ee8e8-f6fb-4225-8ba7-70c03fa269da',
  name: 'swim soon',
  surname: 'wallop',
};

export const sampleWithPartialData: IParticipant = {
  id: '2bce2515-0ef5-4692-8574-520b0ff6047e',
  name: 'meaningfully',
  surname: 'lest',
  secondSurname: 'ouch',
  emailContact: 'above nervous aw',
  mobileContact: 'appliance',
};

export const sampleWithFullData: IParticipant = {
  id: '39c36e07-1cf7-45f2-bd6e-359e4ce00942',
  name: 'er innovate till',
  surname: 'if solidly ignorant',
  secondSurname: 'meh ignite',
  emailContact: 'zowie experienced cafe',
  mobileContact: 'frozen tedious superb',
};

export const sampleWithNewData: NewParticipant = {
  name: 'intermarry oof',
  surname: 'curly woot',
  id: null,
};

Object.freeze(sampleWithNewData);
Object.freeze(sampleWithRequiredData);
Object.freeze(sampleWithPartialData);
Object.freeze(sampleWithFullData);
