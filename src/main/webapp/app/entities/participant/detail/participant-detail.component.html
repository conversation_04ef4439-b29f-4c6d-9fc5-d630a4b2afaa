<div class="d-flex justify-content-center">
  <div class="col-8">
    @if (participant(); as participantRef) {
      <div>
        <h2 data-cy="participantDetailsHeading"><span jhiTranslate="forconversationsApp.participant.detail.title">Participant</span></h2>

        <hr />

        <jhi-alert-error />

        <jhi-alert />

        <dl class="row-md jh-entity-details">
          <dt><span jhiTranslate="global.field.id">ID</span></dt>
          <dd>
            <span>{{ participantRef.id }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.participant.name">Name</span></dt>
          <dd>
            <span>{{ participantRef.name }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.participant.surname">Surname</span></dt>
          <dd>
            <span>{{ participantRef.surname }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.participant.secondSurname">Second Surname</span></dt>
          <dd>
            <span>{{ participantRef.secondSurname }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.participant.emailContact">Email Contact</span></dt>
          <dd>
            <span>{{ participantRef.emailContact }}</span>
          </dd>
          <dt><span jhiTranslate="forconversationsApp.participant.mobileContact">Mobile Contact</span></dt>
          <dd>
            <span>{{ participantRef.mobileContact }}</span>
          </dd>
        </dl>

        <button type="submit" (click)="previousState()" class="btn btn-info" data-cy="entityDetailsBackButton">
          <fa-icon icon="arrow-left"></fa-icon>&nbsp;<span jhiTranslate="entity.action.back">Back</span>
        </button>

        <button type="button" [routerLink]="['/participant', participantRef.id, 'edit']" class="btn btn-primary">
          <fa-icon icon="pencil-alt"></fa-icon>&nbsp;<span jhiTranslate="entity.action.edit">Edit</span>
        </button>
      </div>
    }
  </div>
</div>
