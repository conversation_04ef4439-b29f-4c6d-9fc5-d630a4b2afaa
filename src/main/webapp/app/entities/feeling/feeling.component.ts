import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, OnInit, Output, signal } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faSave, faTimes } from '@fortawesome/free-solid-svg-icons';
import { Feeling, FEELING_INFO } from 'app/entities/feeling/feeling.model';
import { HttpClient } from '@angular/common/http';
import { IMessage } from 'app/entities/message/dashboard-message-model';

@Component({
  selector: 'jhi-feeling-selector',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule, TranslateModule],
  templateUrl: './feeling.component.html',
  styleUrls: ['./feeling.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeelingComponent implements OnInit {
  private readonly http = inject(HttpClient);

  faFaceSmile = faFaceSmile;
  faTimes = faTimes;
  faSave = faSave;
  faPlus = faPlus;
  faMinus = faMinus;

  showFeelingSelector = signal(false);
  selectedFeelings = signal<Feeling[]>([]);
  isLoading = signal(false);

  public readonly FEELING_INFO = FEELING_INFO;
  public readonly Feeling = Feeling;
  public readonly feelingOptions = Object.values(Feeling);

  ngOnInit(): void {
    // Initialize selected feelings from message
    if (this.message.feelingList) {
      this.selectedFeelings.set([...this.message.feelingList]);
    }
  }

  toggleFeelingSelector(): void {
    if (!this.showFeelingSelector()) {
      // Reset to current message feelings when opening
      this.selectedFeelings.set(this.message.feelingList ? [...this.message.feelingList] : []);
    }
    this.showFeelingSelector.set(!this.showFeelingSelector());
  }

  toggleFeeling(feeling: Feeling): void {
    const current = this.selectedFeelings();
    const index = current.indexOf(feeling);

    if (index > -1) {
      // Remove feeling
      this.selectedFeelings.set(current.filter(f => f !== feeling));
    } else {
      // Add feeling
      this.selectedFeelings.set([...current, feeling]);
    }
  }

  isFeelingSelected(feeling: Feeling): boolean {
    return this.selectedFeelings().includes(feeling);
  }

  saveFeelings(): void {
    if (!this.message.id) {
      return;
    }

    this.isLoading.set(true);
    const feelings = this.selectedFeelings();

    this.http.put<IMessage>(`/api/messages/${this.message.id}/feelings`, feelings).subscribe({
      next: updatedMessage => {
        // Update the message with new feelings
        this.message.feelingList = updatedMessage.feelingList;
        this.showFeelingSelector.set(false);
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error updating feelings:', error);
        this.isLoading.set(false);
      },
    });
  }

  cancelFeelingSelection(): void {
    // Reset to original feelings
    this.selectedFeelings.set(this.message.feelingList ? [...this.message.feelingList] : []);
    this.showFeelingSelector.set(false);
  }

  getFeelingEmoji(feeling: Feeling): string {
    return FEELING_INFO[feeling]?.emoji || '😐';
  }

  getFeelingColor(feeling: Feeling): string {
    return FEELING_INFO[feeling]?.color || '#808080';
  }

  getFeelingLabel(feeling: Feeling): string {
    return FEELING_INFO[feeling]?.label || feeling;
  }

  hasAnyFeelings(): boolean {
    return !!(this.message.feelingList && this.message.feelingList.length > 0);
  }

  onActionClick(): void {
    this.actionClicked.emit(this.message);
  }
}
