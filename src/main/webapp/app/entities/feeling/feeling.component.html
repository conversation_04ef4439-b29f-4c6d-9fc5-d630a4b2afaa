<div class="compact-msg" *ngIf="message">

    <!-- Feelings Display/Button -->
    <div class="msg-feelings">
      <!-- Show selected feelings if any -->
      @if (hasAnyFeelings()) {
        <div class="feelings-display">
          @for (feeling of message.feelingList; track feeling) {
            <span class="feeling-emoji selected" [style.color]="getFeelingColor(feeling)" [title]="getFeelingLabel(feeling)">
              {{ getFeelingEmoji(feeling) }}
            </span>
          }
        </div>
      }

      <!-- Feeling selector button -->
      <button
        type="button"
        class="feeling-button"
        [class.has-feelings]="hasAnyFeelings()"
        (click)="toggleFeelingSelector()"
        [disabled]="isLoading()"
        title="Agregar/editar sentimientos"
      >
        <fa-icon [icon]="faFaceSmile" [style.color]="hasAnyFeelings() ? '#FFD700' : '#ccc'"></fa-icon>
      </button>
    </div>

    @if (actionType) {
      <button
        type="button"
        class="btn btn-sm message-action-btn"
        [ngClass]="{ 'btn-success': actionType === 'add', 'btn-danger': actionType === 'remove' }"
        (click)="onActionClick()"
        [title]="actionType === 'add' ? 'Agregar a la conversación' : 'Quitar de la conversación'"
      >
        <fa-icon [icon]="actionType === 'add' ? faPlus : faMinus"></fa-icon>
      </button>
    }

  <div class="msg-content">{{ message.content }}</div>

  <!-- Feeling Selector Modal -->
  @if (showFeelingSelector()) {
    <div class="feeling-selector-overlay" (click)="cancelFeelingSelection()">
      <div class="feeling-selector" (click)="$event.stopPropagation()">
        <div class="feeling-selector-header">
          <h4>Selecciona los sentimientos</h4>
          <button type="button" class="close-btn" (click)="cancelFeelingSelection()">
            <fa-icon [icon]="faTimes"></fa-icon>
          </button>
        </div>

        <div class="feeling-grid">
          @for (feeling of feelingOptions; track feeling) {
            <button
              type="button"
              class="feeling-option"
              [class.selected]="isFeelingSelected(feeling)"
              (click)="toggleFeeling(feeling)"
              [style.border-color]="isFeelingSelected(feeling) ? getFeelingColor(feeling) : '#ddd'"
              [style.background-color]="isFeelingSelected(feeling) ? getFeelingColor(feeling) + '20' : 'transparent'"
            >
              <span class="feeling-emoji">{{ getFeelingEmoji(feeling) }}</span>
              <span class="feeling-label">{{ getFeelingLabel(feeling) }}</span>
            </button>
          }
        </div>

        <div class="feeling-selector-actions">
          <button type="button" class="btn btn-success btn-sm" (click)="saveFeelings()" [disabled]="isLoading()">
            <fa-icon [icon]="faSave"></fa-icon>
            Guardar
          </button>
          <button type="button" class="btn btn-secondary btn-sm" (click)="cancelFeelingSelection()">Cancelar</button>
        </div>
      </div>
    </div>
  }
</div>
