<div class="dashboard-container">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="header-title">
        <fa-icon [icon]="faComments" class="title-icon"></fa-icon>
        <h1>Dashboard de Conversaciones</h1>
      </div>
      <fieldset class="btn-group gap-2">
        <jhi-whatsapp-message-source-button (uploadSuccess)="loadMessages()"></jhi-whatsapp-message-source-button>
        <jhi-audio-message-source-button (uploadSuccess)="loadMessages()"></jhi-audio-message-source-button>
        <jhi-email-message-source-button (uploadSuccess)="loadMessages()"></jhi-email-message-source-button>
      </fieldset>
    </div>
  </div>
  <!-- Dashboard Content -->
  <div class="dashboard-content">
    <!-- Left Section - 25% -->
    <div class="left-section">
      <!-- Participants Section -->
      <jhi-dashboard-participant></jhi-dashboard-participant>

      <!-- Aliases Section -->
      <jhi-dashboard-alias></jhi-dashboard-alias>
    </div>

    <!-- Center Section - 50% -->
    <div class="center-section">
      <!-- Filter Section -->
      <jhi-dashboard-filter
        [tags]="tags()"
        (filterChange)="onFilterChange($event)"
        (clearFilters)="onClearFilters()"
        (searchFilter)="onSearch($event)"
      >
      </jhi-dashboard-filter>

      <!-- Messages Section -->
      @if (filteredMessages().length === 0 && messages().length === 0) {
        <div class="alert alert-warning" id="no-result">
          <span jhiTranslate="forconversationsApp.message.home.notFound">No Messages found</span>
        </div>
      } @else if (filteredMessages().length === 0 && messages().length > 0) {
        <div class="alert alert-info">No se encontraron mensajes que coincidan con los filtros aplicados.</div>
      } @else {
        <div class="messages-container-compact">
          @for (message of filteredMessages(); track message.id; let i = $index) {
            <jhi-dashboard-message [message]="message"></jhi-dashboard-message>
          } @empty {
            <div class="alert alert-warning">No se encontraron mensajes.</div>
          }
        </div>
      }
    </div>

    <!-- Right Section - 25% -->
    <div class="right-section">
      <!-- Conversations Section -->
      <jhi-dashboard-conversation [allMessages]="messages()"></jhi-dashboard-conversation>

      <!-- Tags Section -->
      <jhi-dashboard-tag></jhi-dashboard-tag>
    </div>
  </div>
</div>
