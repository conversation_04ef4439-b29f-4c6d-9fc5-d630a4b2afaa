.alias-panel {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;

  .alias-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;

    h3 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #495057;
    }

    .btn {
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 2rem;

      fa-icon {
        font-size: 0.875rem;
      }
    }
  }

  .alias-search {
    margin-bottom: 1rem;

    .search-input-group {
      position: relative;
      display: flex;
      align-items: center;

      .search-icon {
        position: absolute;
        left: 0.75rem;
        color: #6c757d;
        font-size: 0.875rem;
        z-index: 2;
      }

      .form-control {
        padding-left: 2.25rem;
        border-radius: 6px;
        border: 1px solid #ced4da;
        font-size: 0.875rem;

        &:focus {
          border-color: #86b7fe;
          box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
          outline: 0;
        }
      }
    }
  }

  .alias-list {
    flex: 1;
    overflow-y: auto;
    max-height: 400px;

    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      color: #6c757d;
      font-size: 0.875rem;
    }

    .empty-state {
      text-align: center;
      padding: 2rem 1rem;
      color: #6c757d;

      p {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }

      small {
        font-size: 0.8125rem;
        color: #868e96;
      }
    }

    .alias-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      margin-bottom: 0.5rem;
      background: white;
      transition: all 0.2s ease;

      &:hover {
        border-color: #86b7fe;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .alias-info {
        flex: 1;
        min-width: 0;

        .alias-type {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 0.25rem;

          .type-icon {
            font-size: 1rem;
          }

          .type-label {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }

        .alias-value {
          font-size: 0.875rem;
          font-weight: 500;
          color: #495057;
          word-break: break-word;
        }
      }

      .alias-actions {
        display: flex;
        gap: 0.25rem;
        margin-left: 0.5rem;

        .btn {
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;

          fa-icon {
            font-size: 0.75rem;
          }

          &.btn-outline-primary {
            color: #0d6efd;
            border-color: #0d6efd;

            &:hover {
              background-color: #0d6efd;
              color: white;
            }
          }
        }
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;

  .modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      border-bottom: 1px solid #e9ecef;

      h4 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
      }

      .btn-close {
        background: none;
        border: none;
        color: #6c757d;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0.25rem;

        &:hover {
          color: #495057;
        }
      }
    }

    .modal-body {
      padding: 1rem;

      .form-section {
        .form-group {
          margin-bottom: 1rem;

          &:last-child {
            margin-bottom: 0;
          }

          .form-label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #495057;
            display: block;
          }

          .form-control,
          .form-select {
            width: 100%;
            font-size: 0.875rem;
            border-radius: 6px;
            border: 1px solid #ced4da;
            padding: 0.5rem 0.75rem;

            &:focus {
              border-color: #86b7fe;
              box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
              outline: 0;
            }
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 0.5rem;
      padding: 1rem;
      border-top: 1px solid #e9ecef;
      justify-content: flex-end;

      .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        fa-icon {
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .alias-panel {
    .alias-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;

      .alias-actions {
        margin-left: 0;
        align-self: flex-end;
      }
    }
  }

  .modal-overlay {
    .modal-content {
      width: 95%;
      margin: 1rem;

      .modal-footer {
        flex-direction: column;

        .btn {
          width: 100%;
        }
      }
    }
  }
}
