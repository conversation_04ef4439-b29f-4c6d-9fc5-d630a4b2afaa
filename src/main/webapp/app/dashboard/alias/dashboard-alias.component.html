<div class="alias-panel">
  <!-- Header -->
  <div class="alias-header">
    <h3><PERSON><PERSON></h3>
    <button type="button" class="btn btn-primary btn-sm" (click)="openCreateModal()" [disabled]="isLoading()">
      <fa-icon [icon]="faPlus"></fa-icon>
    </button>
  </div>

  <!-- Search Box -->
  <div class="alias-search" *ngIf="aliases().length > 0">
    <div class="search-input-group">
      <fa-icon [icon]="faSearch" class="search-icon"></fa-icon>
      <input
        type="text"
        class="form-control form-control-sm"
        placeholder="Buscar alias..."
        [(ngModel)]="searchText"
        (ngModelChange)="onSearchChange()"
      />
    </div>
  </div>

  <!-- Alias List -->
  <div class="alias-list">
    @if (isLoading()) {
      <div class="loading-state">
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <span class="ms-2">Cargando alias...</span>
      </div>
    } @else if (filteredAliases().length === 0 && aliases().length === 0) {
      <div class="empty-state">
        <p>No tienes alias creados.</p>
        <small>Haz clic en el botón + para crear tu primer alias.</small>
      </div>
    } @else if (filteredAliases().length === 0 && searchText.trim()) {
      <div class="empty-state">
        <p>No se encontraron alias que coincidan con "{{ searchText }}".</p>
      </div>
    } @else {
      @for (alias of filteredAliases(); track alias.id) {
        <div class="alias-item">
          <div class="alias-info">
            <div class="alias-type">
              <span class="type-icon">{{ getTypeIcon(alias.type) }}</span>
              <span class="type-label">{{ getTypeLabel(alias.type) }}</span>
            </div>
            <div class="alias-value">{{ alias.value }}</div>
          </div>
          <div class="alias-actions">
            <button
              type="button"
              class="btn btn-outline-primary btn-sm"
              (click)="openEditModal(alias)"
              [disabled]="isLoading()"
              title="Editar alias"
            >
              <fa-icon [icon]="faEdit"></fa-icon>
            </button>
          </div>
        </div>
      }
    }
  </div>
</div>

<!-- Create/Edit Modal -->
<div class="modal-overlay" *ngIf="showModal()" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h4>{{ isEditMode() ? 'Editar Alias' : 'Crear Alias' }}</h4>
      <button type="button" class="btn-close" (click)="closeModal()">
        <fa-icon [icon]="faTimes"></fa-icon>
      </button>
    </div>

    <div class="modal-body">
      <div class="form-section">
        <div class="form-group">
          <label for="aliasType" class="form-label">Tipo:</label>
          <select id="aliasType" class="form-select form-select-sm" [(ngModel)]="currentAlias.type">
            @for (type of aliasTypes; track type.value) {
              <option [value]="type.value">{{ type.label }}</option>
            }
          </select>
        </div>

        <div class="form-group">
          <label for="aliasValue" class="form-label">Valor:</label>
          <input
            type="text"
            id="aliasValue"
            class="form-control form-control-sm"
            placeholder="Introduce el valor del alias..."
            [(ngModel)]="currentAlias.value"
          />
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-success" (click)="saveAlias()" [disabled]="!currentAlias.value.trim() || isLoading()">
        <fa-icon [icon]="faSave"></fa-icon>
        {{ isEditMode() ? 'Guardar' : 'Crear' }}
      </button>
      @if (isEditMode()) {
        <button type="button" class="btn btn-danger" (click)="deleteAlias()" [disabled]="isLoading()">
          <fa-icon [icon]="faTrash"></fa-icon>
          Eliminar
        </button>
      }
      <button type="button" class="btn btn-secondary" (click)="closeModal()">Cancelar</button>
    </div>
  </div>
</div>
