import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPlus, faTrash, faEdit, faSearch, faTimes, faSave } from '@fortawesome/free-solid-svg-icons';

export interface AliasDTO {
  id?: string;
  value: string;
  type: AliasType;
  userId?: string;
}

export enum AliasType {
  WHATSAPP = 'WHATSAPP',
  AUDIO = 'AUDIO',
  EMAIL = 'EMAIL',
}

@Component({
  selector: 'jhi-dashboard-alias',
  templateUrl: './dashboard-alias.component.html',
  styleUrls: ['./dashboard-alias.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
})
export class DashboardAliasComponent implements OnInit {
  faPlus = faPlus;
  faTrash = faTrash;
  faEdit = faEdit;
  faSearch = faSearch;
  faTimes = faTimes;
  faSave = faSave;

  private readonly http = inject(HttpClient);

  aliases = signal<AliasDTO[]>([]);
  filteredAliases = signal<AliasDTO[]>([]);
  isLoading = signal(false);
  showModal = signal(false);
  isEditMode = signal(false);
  searchText = '';

  // Form data
  currentAlias: AliasDTO = {
    value: '',
    type: AliasType.WHATSAPP,
  };

  // Available alias types
  aliasTypes = [
    { value: AliasType.WHATSAPP, label: 'WhatsApp' },
    { value: AliasType.EMAIL, label: 'Email' },
    { value: AliasType.AUDIO, label: 'Audio' },
  ];

  ngOnInit(): void {
    this.loadAliases();
  }

  loadAliases(): void {
    this.isLoading.set(true);
    this.http.get<AliasDTO[]>('/api/aliases/my').subscribe({
      next: aliases => {
        this.aliases.set(aliases);
        this.filterAliases();
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error loading aliases:', error);
        this.isLoading.set(false);
      },
    });
  }

  filterAliases(): void {
    const allAliases = this.aliases();
    if (!this.searchText.trim()) {
      this.filteredAliases.set(allAliases);
      return;
    }

    const filtered = allAliases.filter(alias => alias.value.toLowerCase().includes(this.searchText.toLowerCase()));
    this.filteredAliases.set(filtered);
  }

  onSearchChange(): void {
    this.filterAliases();
  }

  openCreateModal(): void {
    this.isEditMode.set(false);
    this.resetForm();
    this.showModal.set(true);
  }

  openEditModal(alias: AliasDTO): void {
    this.isEditMode.set(true);
    this.currentAlias = { ...alias };
    this.showModal.set(true);
  }

  saveAlias(): void {
    if (!this.currentAlias.value.trim()) {
      return;
    }

    this.isLoading.set(true);

    if (this.isEditMode()) {
      // Update existing alias
      this.http.put<AliasDTO>(`/api/aliases/${this.currentAlias.id}`, this.currentAlias).subscribe({
        next: updatedAlias => {
          const currentAliases = this.aliases();
          const updatedAliases = currentAliases.map(a => (a.id === updatedAlias.id ? updatedAlias : a));
          this.aliases.set(updatedAliases);
          this.filterAliases();
          this.closeModal();
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error updating alias:', error);
          this.isLoading.set(false);
        },
      });
    } else {
      // Create new alias
      this.http.post<AliasDTO>('/api/aliases/my', this.currentAlias).subscribe({
        next: createdAlias => {
          const currentAliases = this.aliases();
          this.aliases.set([...currentAliases, createdAlias]);
          this.filterAliases();
          this.closeModal();
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error creating alias:', error);
          this.isLoading.set(false);
        },
      });
    }
  }

  deleteAlias(): void {
    if (!this.currentAlias.id || !confirm(`¿Estás seguro de que quieres eliminar el alias "${this.currentAlias.value}"?`)) {
      return;
    }

    this.isLoading.set(true);
    this.http.delete(`/api/aliases/${this.currentAlias.id}`).subscribe({
      next: () => {
        const currentAliases = this.aliases();
        const updatedAliases = currentAliases.filter(a => a.id !== this.currentAlias.id);
        this.aliases.set(updatedAliases);
        this.filterAliases();
        this.closeModal();
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error deleting alias:', error);
        this.isLoading.set(false);
      },
    });
  }

  closeModal(): void {
    this.showModal.set(false);
    this.isEditMode.set(false);
    this.resetForm();
  }

  resetForm(): void {
    this.currentAlias = {
      value: '',
      type: AliasType.WHATSAPP,
    };
  }

  getTypeLabel(type: AliasType): string {
    const typeObj = this.aliasTypes.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  }

  getTypeIcon(type: AliasType): string {
    switch (type) {
      case AliasType.WHATSAPP:
        return '💬';
      case AliasType.EMAIL:
        return '📧';
      case AliasType.AUDIO:
        return '🎵';
      default:
        return '📝';
    }
  }
}
