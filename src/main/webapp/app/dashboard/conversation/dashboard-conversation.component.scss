.conversation-panel {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;

  .conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;

    .header-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .header-icon {
        color: #6c757d;
        font-size: 1.2rem;
      }

      h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
      }
    }

    .btn {
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 2rem;

      fa-icon {
        font-size: 0.875rem;
      }
    }
  }

  .conversation-list {
    flex: 1;
    overflow-y: auto;
    max-height: 600px;

    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      color: #6c757d;
      font-size: 0.875rem;
    }

    .empty-state {
      text-align: center;
      padding: 2rem 1rem;
      color: #6c757d;

      p {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }

      small {
        font-size: 0.8125rem;
        color: #868e96;
      }
    }

    .conversation-item {
      border: 1px solid #e9ecef;
      border-radius: 6px;
      margin-bottom: 0.5rem;
      background: white;
      transition: all 0.2s ease;

      &:hover {
        border-color: #86b7fe;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &.expanded {
        border-color: #0d6efd;
      }

      .conversation-header-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        cursor: pointer;

        .conversation-info {
          flex: 1;

          .conversation-date {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.25rem;

            .date-icon {
              color: #6c757d;
              font-size: 0.875rem;
            }
          }

          .conversation-summary {
            font-size: 0.8125rem;
            color: #6c757d;
          }
        }

        .conversation-meta {
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .feelings-preview {
            display: flex;
            align-items: center;
            gap: 0.25rem;

            .feeling-emoji {
              font-size: 1rem;
            }

            .more-indicator {
              font-size: 0.75rem;
              color: #6c757d;
              font-weight: 500;
            }
          }

          .btn {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;

            fa-icon {
              font-size: 0.75rem;
            }
          }
        }
      }

      .conversation-details {
        padding: 0.75rem;
        border-top: 1px solid #e9ecef;
        background: #f8f9fa;

        .context-section,
        .tags-section,
        .feelings-section {
          margin-bottom: 0.75rem;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            display: block;
            font-size: 0.8125rem;
            color: #495057;
            margin-bottom: 0.5rem;
          }

          p {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0;
          }
        }

        .tag-list,
        .feeling-list {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }

        .tag-badge {
          display: inline-flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.25rem 0.5rem;
          background: #e7f3ff;
          color: #0066cc;
          border-radius: 4px;
          font-size: 0.75rem;

          fa-icon {
            font-size: 0.625rem;
          }
        }

        .feeling-badge {
          display: inline-flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.25rem 0.5rem;
          background: #fff3cd;
          color: #856404;
          border-radius: 4px;
          font-size: 0.75rem;
        }
      }
    }
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;

  .modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      border-bottom: 1px solid #e9ecef;

      h4 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
      }

      .btn-close {
        background: none;
        border: none;
        color: #6c757d;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0.25rem;

        &:hover {
          color: #495057;
        }
      }
    }

    .modal-body {
      padding: 1rem;

      .form-section {
        .form-group {
          margin-bottom: 1rem;

          &:last-child {
            margin-bottom: 0;
          }

          .form-label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #495057;
            display: block;
          }

          .form-control {
            width: 100%;
            font-size: 0.875rem;
            border-radius: 6px;
            border: 1px solid #ced4da;
            padding: 0.5rem 0.75rem;

            &:focus {
              border-color: #86b7fe;
              box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
              outline: 0;
            }
          }

          .feelings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 0.5rem;

            .feeling-button {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 0.25rem;
              padding: 0.5rem;
              border: 1px solid #dee2e6;
              border-radius: 6px;
              background: white;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                border-color: #86b7fe;
                background: #f8f9fa;
              }

              &.selected {
                border-color: #0d6efd;
                background: #e7f3ff;
              }

              .feeling-emoji {
                font-size: 1.5rem;
              }

              .feeling-label {
                font-size: 0.75rem;
                color: #495057;
              }
            }
          }

          .available-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.5rem;

            .tag-select-button {
              display: inline-flex;
              align-items: center;
              gap: 0.25rem;
              padding: 0.25rem 0.75rem;
              border: 1px solid #dee2e6;
              border-radius: 20px;
              background: white;
              color: #495057;
              font-size: 0.875rem;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                border-color: #86b7fe;
                background: #f8f9fa;
              }

              &.selected {
                border-color: #0d6efd;
                background: #e7f3ff;
                color: #0066cc;
              }

              fa-icon {
                font-size: 0.75rem;
              }
            }
          }

          .selected-tags {
            margin-top: 0.5rem;

            small {
              display: block;
              color: #6c757d;
              font-size: 0.75rem;
              margin-bottom: 0.25rem;
            }

            .tag-badge {
              &.removable {
                cursor: pointer;
                padding-right: 0.75rem;
                position: relative;

                &:hover {
                  background: #cce5ff;
                }

                .remove-icon {
                  margin-left: 0.25rem;
                  font-size: 0.625rem;
                }
              }
            }
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      gap: 0.5rem;
      padding: 1rem;
      border-top: 1px solid #e9ecef;
      justify-content: flex-end;

      .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 6px;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        fa-icon {
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .conversation-panel {
    .conversation-item {
      .conversation-header-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;

        .conversation-meta {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }

  .modal-overlay {
    .modal-content {
      width: 95%;
      margin: 1rem;

      .modal-body {
        .form-section {
          .feelings-grid {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
          }
        }
      }

      .modal-footer {
        flex-direction: column;

        .btn {
          width: 100%;
        }
      }
    }
  }
}
