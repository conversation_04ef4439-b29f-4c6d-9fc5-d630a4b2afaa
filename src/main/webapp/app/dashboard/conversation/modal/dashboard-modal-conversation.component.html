<div class="modal-overlay" (click)="closeModal()" (keydown.escape)="closeModal()">
  <div class="modal-content modal-lg" (click)="$event.stopPropagation()" (keydown)="$event.stopPropagation()">
    <div class="modal-header bg-danger">
      <h4 class="modal-title" data-cy="conversationCreateUpdateHeading">
        <span *ngIf="conversation?.id">{{ 'forconversationsApp.conversation.home.createOrEditLabel' | translate }}</span>
        <span *ngIf="!conversation?.id">{{ 'forconversationsApp.conversation.home.createLabel' | translate }}</span>
      </h4>
      <div class="d-flex align-items-center">
        <p-button
          type="submit"
          icon="pi pi-save"
          [label]="(conversation?.id ? 'entity.action.update' : 'entity.action.create') | translate"
          [disabled]="isLoading()"
          class="p-button-sm p-button-success me-2"
        ></p-button>
        <p-button
          type="button"
          icon="pi pi-times"
          [label]="'entity.action.cancel' | translate"
          (click)="closeModal()"
          class="p-button-sm p-button-secondary"
        ></p-button>
      </div>
    </div>

    <div class="modal-body">
      <!-- Error Message Display -->
      @if (errorMessage()) {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
          <fa-icon [icon]="faExclamationTriangle" class="me-2"></fa-icon>
          {{ errorMessage() }}
          <button type="button" class="btn-close" (click)="errorMessage.set(null)" aria-label="Close"></button>
        </div>
      }
      <div class="row mb-3">
        <div class="col-md-4">
          <label class="form-label" for="conversationName" jhiTranslate="forconversationsApp.conversation.name">Conversation Name</label>
          <input id="conversationName" type="text" class="form-control" [(ngModel)]="currentConversation.name" required />
        </div>
        <div class="col-md-4">
          <label class="form-label" for="startDate" jhiTranslate="forconversationsApp.conversation.startDate">Start Date</label>
          <p-datepicker
            id="startDate"
            name="startDate"
            [(ngModel)]="currentConversation.start"
            dateFormat="yy-mm-dd"
            [showTime]="true"
            [showSeconds]="true"
            [hourFormat]="'24'"
            [style]="{ width: '100%' }"
            (onSelect)="updateDateTime()"
            (onBlur)="updateDateTime()"
            [input]="true"
          ></p-datepicker>
        </div>
        <div class="col-md-4">
          <label class="form-label" for="endDate" jhiTranslate="forconversationsApp.conversation.endDate">End Date</label>
          <p-datepicker
            id="endDate"
            name="endDate"
            [(ngModel)]="currentConversation.end"
            dateFormat="yy-mm-dd"
            [showTime]="true"
            [showSeconds]="true"
            [hourFormat]="'24'"
            [style]="{ width: '100%' }"
            (onSelect)="updateDateTime()"
            (onBlur)="updateDateTime()"
            [input]="true"
          ></p-datepicker>
        </div>

        <p-accordion [activeIndex]="[0]">
          <p-accordionTab header="{{ 'forconversationsApp.conversation.filterAndMessages' | translate }}">
            <!-- Contenido del filtro y mensajes para añadir -->
            <div class="filter-wrapper mb-3">
              <jhi-dashboard-filter
                [tags]="[]"
                (filterChange)="onFilterChange($event)"
                (clearFilters)="onClearFilters()"
              ></jhi-dashboard-filter>
            </div>

            <!-- Messages List -->
            <div class="messages-container">
              <h5 class="section-title" jhiTranslate="forconversationsApp.conversation.messages">Messages</h5>

              @if (isLoading()) {
                <div class="loading-state">
                  <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">Cargando...</span>
                  </div>
                  <span class="ms-2">Cargando mensajes...</span>
                </div>
              } @else if (allMessagesSignal().length === 0) {
                <div class="empty-state">
                  <p class="text-muted" jhiTranslate="forconversationsApp.conversation.notFound">No Messages found</p>
                </div>
              } @else {
                <div class="messages-list">
                  @for (message of filteredMessages(); track trackMessageById($index, message)) {
                    @if (!isMessageInConversation(message)) {
                      <div class="message-item-wrapper">
                        <jhi-dashboard-message [message]="message"></jhi-dashboard-message>
                        <button
                          type="button"
                          class="btn btn-sm btn-success message-action-btn"
                          (click)="toggleMessageInConversation(message)"
                          title="{{ 'forconversationsApp.conversation.addMessage' | translate }}"
                        >
                          <fa-icon [icon]="faPlus"></fa-icon>
                        </button>
                      </div>
                    }
                  }
                </div>
              }
            </div>
          </p-accordionTab>
        </p-accordion>

        <p-accordion [activeIndex]="[0]">
          <p-accordionTab header="{{ 'forconversationsApp.conversation.conversationMessages' | translate }}">
            <!-- Contenido de los mensajes de la conversación -->
            <div class="messages-container">
              <h5 class="section-title" jhiTranslate="forconversationsApp.conversation.messages">Messages</h5>
              @if (conversationMessages().length > 0) {
                <div class="selected-messages mt-4">
                  <div class="messages-list">
                    @for (message of conversationMessages(); track trackMessageById($index, message)) {
                      <div class="message-item-wrapper">
                        <jhi-dashboard-message
                          [message]="message"
                          [actionType]="'remove'"
                          (actionClicked)="toggleMessageInConversation($event)"
                        ></jhi-dashboard-message>
                      </div>
                    }
                  </div>
                </div>
              } @else {
                <div class="empty-state">
                  <p class="text-muted" jhiTranslate="forconversationsApp.conversation.noConversationMessages">
                    No conversation messages found
                  </p>
                </div>
              }
            </div>
          </p-accordionTab>
        </p-accordion>
      </div>

      <div class="modal-footer">
        <p-button
          type="button"
          [label]="'entity.action.save' | translate"
          icon="pi pi-save"
          (click)="saveConversation()"
          [disabled]="isLoading()"
          class="p-button-success"
        ></p-button>
        @if (isEditMode) {
          <p-button
            type="button"
            [label]="'entity.action.delete' | translate"
            icon="pi pi-trash"
            (click)="deleteConversation()"
            [disabled]="isLoading()"
            class="p-button-danger"
          ></p-button>
        }
        <p-button
          type="button"
          [label]="'entity.action.cancel' | translate"
          icon="pi pi-times"
          (click)="closeModal()"
          class="p-button-secondary"
        ></p-button>
      </div>
    </div>
  </div>
</div>
