<div class="messages-section">
  <h5 class="section-title">{{ title }}</h5>

  @if (isLoading) {
    <div class="loading-state">
      <div class="spinner-border spinner-border-sm" role="status">
        <span class="visually-hidden">Cargando...</span>
      </div>
      <span class="ms-2">Cargando mensajes...</span>
    </div>
  } @else if (messages.length === 0) {
    <div class="empty-state">
      <p class="text-muted">No hay mensajes disponibles</p>
    </div>
  } @else {
    <div class="messages-list">
      @for (message of messages; track trackById($index, message)) {
        <div class="message-item-wrapper">
          <jhi-dashboard-message [message]="message"></jhi-dashboard-message>
          @if (showAddButton || showRemoveButton) {
            <button
              type="button"
              class="btn btn-sm message-action-btn"
              [class.btn-success]="showAddButton && !isMessageInConversation(message)"
              [class.btn-danger]="(showAddButton && isMessageInConversation(message)) || showRemoveButton"
              (click)="onToggleMessage(message)"
              [title]="
                showRemoveButton
                  ? 'Quitar de la conversación'
                  : isMessageInConversation(message)
                    ? 'Quitar de la conversación'
                    : 'Agregar a la conversación'
              "
            >
              <fa-icon [icon]="showRemoveButton ? faMinus : isMessageInConversation(message) ? faMinus : faPlus"></fa-icon>
            </button>
          }
        </div>
      }
    </div>
  }
</div>
