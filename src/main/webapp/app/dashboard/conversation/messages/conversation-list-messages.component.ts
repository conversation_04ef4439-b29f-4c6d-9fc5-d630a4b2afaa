import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
import { DashboardMessageComponent } from '../../messsage/dashboard-message.component';
import { IMessage } from '../../messsage/dashboard-message-model';
import { MessageDTO } from '../dashboard-conversation-model';

@Component({
  selector: 'jhi-conversation-list-messages',
  templateUrl: './conversation-list-messages.component.html',
  styleUrls: ['./conversation-list-messages.component.scss'],
  standalone: true,
  imports: [CommonModule, FontAwesomeModule, DashboardMessageComponent],
})
export class ConversationListMessagesComponent {
  @Input() messages: (IMessage | MessageDTO)[] = [];
  @Input() conversationMessages: MessageDTO[] = [];
  @Input() title = 'Mensajes';
  @Input() showAddButton = true;
  @Input() showRemoveButton = false;
  @Input() isLoading = false;
  @Output() toggleMessage = new EventEmitter<IMessage | MessageDTO>();

  faPlus = faPlus;
  faMinus = faMinus;

  isMessageInConversation(message: IMessage | MessageDTO): boolean {
    return this.conversationMessages.some(m => m.id === message.id);
  }

  onToggleMessage(message: IMessage | MessageDTO): void {
    this.toggleMessage.emit(message);
  }

  trackById(index: number, item: IMessage | MessageDTO): string | number {
    return item.id ?? index;
  }
}
