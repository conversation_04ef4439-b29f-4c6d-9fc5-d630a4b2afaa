<div class="conversation-panel">
  <!-- Header -->
  <div class="conversation-header">
    <div class="header-title">
      <fa-icon [icon]="faComments" class="header-icon"></fa-icon>
      <h3>Conversaciones</h3>
    </div>
    <button type="button" class="btn btn-primary btn-sm" (click)="openCreateModal()" [disabled]="isLoading()">
      <fa-icon [icon]="faPlus"></fa-icon>
    </button>
  </div>

  <!-- Conversation List -->
  <div class="conversation-list">
    @if (isLoading()) {
      <div class="loading-state">
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <span class="ms-2">Cargando conversaciones...</span>
      </div>
    } @else if (conversations().length === 0) {
      <div class="empty-state">
        <p>No tienes conversaciones registradas.</p>
        <small>Haz clic en el botón + para crear tu primera conversación.</small>
      </div>
    } @else {
      @for (conversation of conversations(); track conversation.id) {
        <div class="conversation-item" [class.expanded]="isConversationExpanded(conversation.id!)">
          <div class="conversation-header-item" (click)="toggleConversation(conversation.id!)">
            <div class="conversation-info">
              <div class="conversation-name">{{ conversation.name }}</div>
              <div class="conversation-date">
                <fa-icon [icon]="faCalendar" class="date-icon"></fa-icon>
                {{ formatDate(conversation.start) }}
              </div>
              <div class="conversation-summary">{{ getConversationSummary(conversation) }}</div>
            </div>
            <div class="conversation-meta">
              <button
                type="button"
                class="btn btn-outline-primary btn-sm"
                (click)="openEditModal(conversation); $event.stopPropagation()"
                [disabled]="isLoading()"
                title="Editar conversación"
              >
                <fa-icon [icon]="faEdit"></fa-icon>
              </button>
            </div>
          </div>

          @if (isConversationExpanded(conversation.id!)) {
            <div class="conversation-details">
              @if (conversation.context) {
                <div class="context-section">
                  <strong>Contexto:</strong>
                  <p>{{ conversation.context }}</p>
                </div>
              }
            </div>
          }
        </div>
      }
    }
  </div>
</div>

<!-- Create/Edit Modal -->
@if (showModal() && currentConversation) {
  <jhi-dashboard-modal-conversation
    [conversation]="currentConversation"
    [isEditMode]="isEditMode()"
    [allMessages]="allMessages"
    (conversationSave)="onSaveConversation($event)"
    (conversationDelete)="onDeleteConversation()"
    (conversationClose)="closeModal()"
  ></jhi-dashboard-modal-conversation>
}
