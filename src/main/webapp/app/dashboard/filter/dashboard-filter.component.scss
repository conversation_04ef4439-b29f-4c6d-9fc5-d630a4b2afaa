.dashboard-filter {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .filter-collapsed {
    .filter-input-row {
      display: flex;
      gap: 0.5rem;
      align-items: center;

      .filter-text-input {
        flex: 1;
        border-radius: 6px;
        border: 1px solid #ced4da;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;

        &:focus {
          border-color: #86b7fe;
          box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
          outline: 0;
        }
      }

      .filter-actions {
        display: flex;
        gap: 0.25rem;

        .btn {
          padding: 0.375rem 0.75rem;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.25rem;
          font-size: 0.875rem;
          font-weight: 500;

          fa-icon {
            font-size: 0.875rem;
          }

          &.btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;

            &:hover {
              background-color: #0b5ed7;
              border-color: #0a58ca;
            }
          }

          &.btn-outline-primary {
            color: #0d6efd;
            border-color: #0d6efd;

            &:hover {
              background-color: #0d6efd;
              color: white;
            }
          }

          &.btn-outline-secondary {
            color: #6c757d;
            border-color: #6c757d;

            &:hover {
              background-color: #6c757d;
              color: white;
            }
          }
        }
      }
    }
  }

  .filter-expanded {
    max-height: 0;
    overflow: hidden;
    transition:
      max-height 0.3s ease-out,
      padding-top 0.3s ease-out;
    padding-top: 0;

    &.show {
      max-height: 200px;
      padding-top: 1rem;
    }

    .filter-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }

      .filter-item {
        flex: 1;

        .form-label {
          font-size: 0.875rem;
          font-weight: 500;
          margin-bottom: 0.25rem;
          color: #495057;
        }

        .form-control,
        .form-select {
          font-size: 0.875rem;
          border-radius: 6px;
          border: 1px solid #ced4da;

          &:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            outline: 0;
          }
        }

        .form-control-sm,
        .form-select-sm {
          padding: 0.25rem 0.5rem;
          font-size: 0.8125rem;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-filter {
    .filter-expanded {
      .filter-row {
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  }
}
