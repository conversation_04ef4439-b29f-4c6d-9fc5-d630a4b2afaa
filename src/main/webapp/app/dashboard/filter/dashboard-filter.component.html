<div class="dashboard-filter">
  <!-- Collapsed state - always visible -->
  <div class="filter-collapsed">
    <div class="filter-input-row">
      <input
        type="text"
        class="form-control filter-text-input"
        [(ngModel)]="filterText"
        (ngModelChange)="onFilterChange()"
        placeholder="Buscar en mensajes..."
      />
      <div class="filter-actions">
        <button type="button" class="btn btn-primary btn-sm" (click)="onSearch()" title="Buscar">
          <fa-icon [icon]="faSearch"></fa-icon>
          Buscar
        </button>
        <button
          type="button"
          class="btn btn-outline-primary btn-sm"
          (click)="toggleExpanded()"
          [attr.aria-expanded]="isExpanded()"
          aria-controls="expandedFilters"
        >
          <fa-icon [icon]="isExpanded() ? faMinus : faPlus"></fa-icon>
        </button>
        <button type="button" class="btn btn-outline-secondary btn-sm" (click)="onClearFilters()" title="Limpiar filtros">
          <fa-icon [icon]="faBroom"></fa-icon>
          Limpiar
        </button>
      </div>
    </div>
  </div>

  <!-- Expanded state - collapsible -->
  <div id="expandedFilters" class="filter-expanded" [class.show]="isExpanded()">
    <!-- First row: Date range -->
    <div class="filter-row">
      <div class="filter-item">
        <label for="filterStartDate" class="form-label">Inicio:</label>
        <input
          type="datetime-local"
          id="filterStartDate"
          class="form-control form-control-sm"
          [(ngModel)]="filterStartDate"
          (ngModelChange)="onFilterChange()"
        />
      </div>
      <div class="filter-item">
        <label for="filterEndDate" class="form-label">Fin:</label>
        <input
          type="datetime-local"
          id="filterEndDate"
          class="form-control form-control-sm"
          [(ngModel)]="filterEndDate"
          (ngModelChange)="onFilterChange()"
        />
      </div>
    </div>

    <!-- Second row: Tag and Feeling -->
    <div class="filter-row">
      <div class="filter-item">
        <label for="filterTag" class="form-label">Tag:</label>
        <select id="filterTag" class="form-select form-select-sm" [(ngModel)]="filterTag" (ngModelChange)="onFilterChange()">
          <option value="">Todos los tags</option>
          @for (tag of tags; track tag) {
            <option [value]="tag">{{ tag }}</option>
          }
        </select>
      </div>
      <div class="filter-item">
        <label for="filterFeeling" class="form-label">Sentimiento:</label>
        <select id="filterFeeling" class="form-select form-select-sm" [(ngModel)]="filterFeeling" (ngModelChange)="onFilterChange()">
          <option value="">Todos</option>
          @for (feeling of feelings; track feeling.value) {
            <option [value]="feeling.value">{{ feeling.label }}</option>
          }
        </select>
      </div>
    </div>
  </div>
</div>
