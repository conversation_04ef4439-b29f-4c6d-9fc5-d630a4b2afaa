import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, inject, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPlus, faTrash, faEdit, faSearch, faTimes, faSave } from '@fortawesome/free-solid-svg-icons';
import { TagDTO } from './dashboard-tag-model';

@Component({
  selector: 'jhi-dashboard-tag',
  templateUrl: './dashboard-tag.component.html',
  styleUrls: ['./dashboard-tag.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, FontAwesomeModule],
})
export class DashboardTagComponent implements OnInit {
  faPlus = faPlus;
  faTrash = faTrash;
  faEdit = faEdit;
  faSearch = faSearch;
  faTimes = faTimes;
  faSave = faSave;

  private readonly http = inject(HttpClient);

  tags = signal<TagDTO[]>([]);
  filteredTags = signal<TagDTO[]>([]);
  isLoading = signal(false);
  showModal = signal(false);
  isEditMode = signal(false);
  searchText = '';

  currentTag: TagDTO = {
    value: '',
  };

  ngOnInit(): void {
    this.loadTags();
  }

  loadTags(): void {
    this.isLoading.set(true);
    this.http.get<TagDTO[]>('/api/tags/my').subscribe({
      next: tags => {
        this.tags.set(tags);
        this.filterTags();
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error loading tags:', error);
        this.isLoading.set(false);
      },
    });
  }

  filterTags(): void {
    const allTags = this.tags();
    if (!this.searchText.trim()) {
      this.filteredTags.set(allTags);
      return;
    }

    const filtered = allTags.filter(tag => tag.value.toLowerCase().includes(this.searchText.toLowerCase()));
    this.filteredTags.set(filtered);
  }

  onSearchChange(): void {
    this.filterTags();
  }

  openCreateModal(): void {
    this.isEditMode.set(false);
    this.resetForm();
    this.showModal.set(true);
  }

  openEditModal(tag: TagDTO): void {
    this.isEditMode.set(true);
    this.currentTag = { ...tag };
    this.showModal.set(true);
  }

  saveTag(): void {
    if (!this.currentTag.value.trim()) {
      return;
    }

    this.isLoading.set(true);

    if (this.isEditMode()) {
      this.http.put<TagDTO>(`/api/tags/${this.currentTag.id}`, this.currentTag).subscribe({
        next: updatedTag => {
          const currentTags = this.tags();
          const updatedTags = currentTags.map(t => (t.id === updatedTag.id ? updatedTag : t));
          this.tags.set(updatedTags);
          this.filterTags();
          this.closeModal();
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error updating tag:', error);
          this.isLoading.set(false);
        },
      });
    } else {
      this.http.post<TagDTO>('/api/tags/my', this.currentTag).subscribe({
        next: createdTag => {
          const currentTags = this.tags();
          this.tags.set([...currentTags, createdTag]);
          this.filterTags();
          this.closeModal();
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error creating tag:', error);
          this.isLoading.set(false);
        },
      });
    }
  }

  deleteTag(): void {
    if (!this.currentTag.id || !confirm(`¿Estás seguro de que quieres eliminar el tag "${this.currentTag.value}"?`)) {
      return;
    }

    this.isLoading.set(true);
    this.http.delete(`/api/tags/${this.currentTag.id}`).subscribe({
      next: () => {
        const currentTags = this.tags();
        const updatedTags = currentTags.filter(t => t.id !== this.currentTag.id);
        this.tags.set(updatedTags);
        this.filterTags();
        this.closeModal();
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error deleting tag:', error);
        this.isLoading.set(false);
      },
    });
  }

  closeModal(): void {
    this.showModal.set(false);
    this.isEditMode.set(false);
    this.resetForm();
  }

  resetForm(): void {
    this.currentTag = {
      value: '',
    };
  }
}
