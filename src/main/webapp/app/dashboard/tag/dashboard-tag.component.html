<div class="tag-panel">
  <!-- Header -->
  <div class="tag-header">
    <h3>Tag</h3>
    <button type="button" class="btn btn-primary btn-sm" (click)="openCreateModal()" [disabled]="isLoading()">
      <fa-icon [icon]="faPlus"></fa-icon>
    </button>
  </div>

  <!-- Search Box -->
  <div class="tag-search" *ngIf="tags().length > 0">
    <div class="search-input-group">
      <fa-icon [icon]="faSearch" class="search-icon"></fa-icon>
      <input
        type="text"
        class="form-control form-control-sm"
        placeholder="Buscar tag..."
        [(ngModel)]="searchText"
        (ngModelChange)="onSearchChange()"
      />
    </div>
  </div>

  <!-- Tag List -->
  <div class="tag-list">
    @if (isLoading()) {
      <div class="loading-state">
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <span class="ms-2">Cargando tags...</span>
      </div>
    } @else if (filteredTags().length === 0 && tags().length === 0) {
      <div class="empty-state">
        <p>No tienes tags creados.</p>
        <small>Haz clic en el botón + para crear tu primer tag.</small>
      </div>
    } @else if (filteredTags().length === 0 && searchText.trim()) {
      <div class="empty-state">
        <p>No se encontraron tags que coincidan con "{{ searchText }}".</p>
      </div>
    } @else {
      @for (tag of filteredTags(); track tag.id) {
        <div class="tag-item">
          <div class="tag-info">
            <div class="tag-value">{{ tag.value }}</div>
          </div>
          <div class="tag-actions">
            <button
              type="button"
              class="btn btn-outline-primary btn-sm"
              (click)="openEditModal(tag)"
              [disabled]="isLoading()"
              title="Editar tag"
            >
              <fa-icon [icon]="faEdit"></fa-icon>
            </button>
          </div>
        </div>
      }
    }
  </div>
</div>

<!-- Create/Edit Modal -->
<div class="modal-overlay" *ngIf="showModal()" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h4>{{ isEditMode() ? 'Editar Tag' : 'Crear Tag' }}</h4>
      <button type="button" class="btn-close" (click)="closeModal()">
        <fa-icon [icon]="faTimes"></fa-icon>
      </button>
    </div>

    <div class="modal-body">
      <div class="form-section">
        <div class="form-group">
          <label for="tagValue" class="form-label">Valor:</label>
          <input
            type="text"
            id="tagValue"
            class="form-control form-control-sm"
            placeholder="Introduce el valor del tag..."
            [(ngModel)]="currentTag.value"
          />
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="button" class="btn btn-success" (click)="saveTag()" [disabled]="!currentTag.value.trim() || isLoading()">
        <fa-icon [icon]="faSave"></fa-icon>
        {{ isEditMode() ? 'Guardar' : 'Crear' }}
      </button>
      @if (isEditMode()) {
        <button type="button" class="btn btn-danger" (click)="deleteTag()" [disabled]="isLoading()">
          <fa-icon [icon]="faTrash"></fa-icon>
          Eliminar
        </button>
      }
      <button type="button" class="btn btn-secondary" (click)="closeModal()">Cancelar</button>
    </div>
  </div>
</div>
