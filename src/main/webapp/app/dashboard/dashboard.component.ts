import { CommonModule } from '@angular/common';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faComments, faEnvelope, faFilter, faPhone, faSync, faTags, faUsers } from '@fortawesome/free-solid-svg-icons';
import { MessageService } from 'app/entities/message/service/message.service';
import SharedModule from 'app/shared/shared.module';
import { map, switchMap, tap } from 'rxjs/operators';
import dayjs from 'dayjs/esm';
import { AudioMessageSourceButtonComponent } from '../entities/audio-message-source/audio-message-source-button';
import { EmailMessageSourceButtonComponent } from '../entities/email-message-source/email-message-source-button.component';
import { WhatsappMessageSourceButtonComponent } from '../entities/whatsapp-message-source/whatsapp-message-source-button.component';
import { IMessage } from './messsage/dashboard-message-model';
import { DashboardMessageComponent } from './messsage/dashboard-message.component';
import { DashboardFilterComponent } from './filter/dashboard-filter.component';
import { DashboardAliasComponent } from './alias/dashboard-alias.component';
import { DashboardTagComponent } from './tag/dashboard-tag.component';
import { DashboardParticipantComponent } from './participant/dashboard-participant.component';
import { DashboardConversationComponent } from './conversation/dashboard-conversation.component';
import { DashboardFilterModel } from './filter/dashboard-filter-model';

@Component({
  selector: 'jhi-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    FontAwesomeModule,
    SharedModule,
    DashboardMessageComponent,
    DashboardFilterComponent,
    DashboardAliasComponent,
    DashboardTagComponent,
    DashboardParticipantComponent,
    DashboardConversationComponent,
    WhatsappMessageSourceButtonComponent,
    AudioMessageSourceButtonComponent,
    EmailMessageSourceButtonComponent,
  ],
})
export class DashboardComponent implements OnInit {
  faComments = faComments;
  faUsers = faUsers;
  faEnvelope = faEnvelope;
  faPhone = faPhone;
  faFilter = faFilter;
  faTags = faTags;
  faSync = faSync;
  isLoading = signal(false);

  private readonly messageService = inject(MessageService);
  private readonly http = inject(HttpClient);
  private readonly refreshTrigger = signal(0);

  messages = toSignal(
    toObservable(this.refreshTrigger).pipe(
      tap(() => this.isLoading.set(true)),
      switchMap(() => this.messageService.query()),
      map((res: HttpResponse<IMessage[]>) => res.body ?? []),
      tap(() => this.isLoading.set(false)),
    ),
    { initialValue: [] },
  );

  filteredMessages = signal<IMessage[]>([]);
  conversations = signal<string[]>([]);
  tags = signal<string[]>([]);
  currentFilter: DashboardFilterModel | null = null;

  trackId = (index: number, item: IMessage): string | number => item.id ?? index;

  constructor() {
    effect(() => {
      const currentMessages = this.messages();
      this.updateTags(currentMessages);
      this.applyFilters();
    });
  }

  ngOnInit(): void {
    this.loadMessages();
  }

  loadMessages(): void {
    this.refreshTrigger.set(Date.now());
  }

  private updateTags(messages: IMessage[]): void {
    const tags = [...new Set(messages.map(m => m.tag).filter((t): t is string => !!t))];
    this.tags.set(tags);
  }

  onFilterChange(filter: any): void {
    this.currentFilter = filter;
    this.applyFilters();
  }

  onClearFilters(): void {
    this.currentFilter = null;
    this.applyFilters();
  }

  onTagClick(tag: string): void {
    const filter: any = {
      tag: [{ id: '', value: tag } as any],
    };
    this.onFilterChange(filter);
  }

  onSearch(filter: DashboardFilterModel): void {
    this.isLoading.set(true);

    // Convert filter to backend DTO format
    const dashboardFilter = {
      content: filter.content || null,
      startTime: filter.init ? filter.init.toISOString() : null,
      endTime: filter.end ? filter.end.toISOString() : null,
      tags: filter.tag ? filter.tag.map(t => t.value) : null,
      feelings: filter.feelings || null,
      sender: null,
      recipient: null,
      sourceType: null,
    };

    this.http.post<IMessage[]>('/api/messages/dashboard-filter', dashboardFilter).subscribe({
      next: messages => {
        this.filteredMessages.set(messages);
        this.isLoading.set(false);
      },
      error: error => {
        console.error('Error searching messages:', error);
        this.isLoading.set(false);
      },
    });
  }

  private applyFilters(): void {
    const allMessages = this.messages();
    if (!this.currentFilter) {
      this.filteredMessages.set(allMessages);
      return;
    }

    const filtered = allMessages.filter(message => {
      // Text filter
      if (this.currentFilter?.content && !message.content?.toLowerCase().includes(this.currentFilter.content.toLowerCase())) {
        return false;
      }

      // Date range filter
      if (this.currentFilter?.init && message.time) {
        const messageDate = message.time.toDate();
        const startDate = this.currentFilter.init.toDate();
        if (messageDate < startDate) {
          return false;
        }
      }

      if (this.currentFilter?.end && message.time) {
        const messageDate = message.time.toDate();
        const endDate = this.currentFilter.end.toDate();
        if (messageDate > endDate) {
          return false;
        }
      }

      // Tag filter
      if (this.currentFilter?.tag && this.currentFilter.tag.length > 0) {
        const filterTagValue = this.currentFilter.tag[0].value;
        if (message.tag !== filterTagValue) {
          return false;
        }
      }

      // Feeling filter
      if (this.currentFilter?.feelings && this.currentFilter.feelings.length > 0) {
        const filterFeeling = this.currentFilter.feelings[0];
        if (message.feeling !== filterFeeling) {
          return false;
        }
      }

      return true;
    });

    this.filteredMessages.set(filtered);
  }
}
