import { SourceType } from 'app/entities/enumerations/source-type.model';
import { Feeling, FEELING_INFO } from 'app/shared/enum/feeling';
import dayjs from 'dayjs/esm';

export interface IMessage {
  id?: string;
  time?: dayjs.Dayjs | null;
  sender?: string | null;
  recipient?: string | null;
  content?: string | null;
  type?: keyof typeof SourceType | null;
  feeling?: Feeling;
  feelingList?: Feeling[];
  tag?: string;
}

export { Feeling, FEELING_INFO };
export type NewMessage = Omit<IMessage, 'id'> & { id: null };
