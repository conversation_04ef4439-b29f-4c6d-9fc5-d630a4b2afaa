:host {
  display: block;
  margin: 0;
}

.compact-msg {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.3;
  position: relative;

  &:hover {
    background: #f8f9fa;
  }
}

.msg-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 0.8rem;
  color: #666;
}

.msg-time {
  font-weight: 500;
  color: #888;
}

.msg-sender {
  font-weight: 600;
  color: #1e3c72;
}

.msg-arrow {
  color: #999;
}

.msg-recipient {
  color: #666;
}

.msg-feelings {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 4px;

  .feelings-display {
    display: flex;
    gap: 2px;

    .feeling-emoji {
      font-size: 1rem;
      cursor: default;

      &.selected {
        filter: brightness(1.2);
      }
    }
  }

  .feeling-button {
    background: none;
    border: none;
    padding: 2px 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }

    &.has-feelings {
      background: rgba(255, 215, 0, 0.2);
    }

    fa-icon {
      font-size: 1rem;
    }
  }
}

.msg-content {
  color: #333;
  margin: 0;
  padding: 0;
}

// Feeling Selector Modal
.feeling-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.feeling-selector {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;

  .feeling-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;

    h4 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #495057;
    }

    .close-btn {
      background: none;
      border: none;
      color: #6c757d;
      font-size: 1.25rem;
      cursor: pointer;
      padding: 0.25rem;

      &:hover {
        color: #495057;
      }
    }
  }

  .feeling-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.375rem;
    padding: 1rem;

    .feeling-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.125rem;
      padding: 0.5rem 0.25rem;
      border: 2px solid #ddd;
      border-radius: 6px;
      background: transparent;
      cursor: pointer;
      transition: all 0.2s ease;
      min-height: 65px;
      aspect-ratio: 1;

      &:hover {
        border-color: #86b7fe;
        background: rgba(134, 183, 254, 0.1);
      }

      &.selected {
        border-width: 2px;
        transform: scale(1.05);
      }

      .feeling-emoji {
        font-size: 1.1rem;
      }

      .feeling-label {
        font-size: 0.6rem;
        font-weight: 500;
        text-align: center;
        color: #495057;
        line-height: 1.1;
        word-break: break-word;
      }
    }
  }

  .feeling-selector-actions {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    justify-content: flex-end;

    .btn {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      fa-icon {
        font-size: 0.875rem;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .feeling-selector {
    width: 95%;
    margin: 1rem;

    .feeling-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 0.25rem;

      .feeling-option {
        min-height: 55px;
        padding: 0.375rem 0.125rem;

        .feeling-emoji {
          font-size: 1rem;
        }

        .feeling-label {
          font-size: 0.55rem;
        }
      }
    }

    .feeling-selector-actions {
      flex-direction: column;

      .btn {
        width: 100%;
      }
    }
  }

  .msg-header {
    flex-wrap: wrap;
    gap: 4px;

    .msg-feelings {
      margin-left: 0;
      order: -1;
      width: 100%;
      justify-content: flex-end;
    }
  }
}
