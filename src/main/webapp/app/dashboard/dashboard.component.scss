.dashboard-container {
  height: 100vh;
  background: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 1.5rem 0;
  margin-bottom: 2rem;

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 1rem;

    .title-icon {
      font-size: 2rem;
      color: #1e3c72;
    }

    h1 {
      margin: 0;
      color: #1e3c72;
      font-size: 1.8rem;
      font-weight: 600;
    }
  }

  .header-actions {
    .btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 2rem;
  height: calc(100vh - 140px);
  overflow: hidden;
}

.left-section,
.center-section,
.right-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .section-header {
    padding: 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .section-icon {
      color: #1e3c72;
      font-size: 1.2rem;
    }

    h3 {
      margin: 0;
      color: #333;
      font-size: 1.1rem;
      font-weight: 600;
    }
  }

  .section-content {
    padding: 1.5rem;
  }
}

.filter-card {
  .filter-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    .filter-item {
      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #333;
        font-size: 0.9rem;
      }

      .form-control,
      .form-select {
        width: 100%;
      }
    }
  }
}

// Estilos para mensajes compactos con scroll
.messages-container-compact {
  max-height: 65vh;
  min-height: 300px;
  overflow-y: auto;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  gap: 0.4rem;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.compact-message {
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  padding: 0.6rem 0.8rem;
  margin: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  font-size: 0.9rem;
  line-height: 1.4;

  &:hover {
    background: #f8f9fa;
    border-color: #d0d0d0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  ::ng-deep {
    .message-header {
      margin-bottom: 0.3rem;
      font-size: 0.85rem;
    }

    .message-content {
      margin-bottom: 0.3rem;
      line-height: 1.3;
    }

    .message-participants {
      margin-bottom: 0.3rem;
      font-size: 0.85rem;
    }

    .message-actions {
      margin-top: 0.3rem;
    }

    .btn {
      padding: 0.2rem 0.5rem;
      font-size: 0.8rem;
    }
  }
}

.participant-group {
  margin-bottom: 1.5rem;

  h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e3c72;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.participant-list {
  .participant-item {
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    transition: all 0.2s ease;

    &:hover {
      background: #e9ecef;
    }

    .participant-name {
      font-weight: 500;
      color: #333;
    }

    &.alias-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .alias-icon {
        color: #1e3c72;
      }

      .alias-info {
        display: flex;
        flex-direction: column;

        .participant-alias {
          font-size: 0.8rem;
          color: #666;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 1rem;
    color: #999;
    font-style: italic;
  }
}

.conversation-list {
  .conversation-item {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #e9ecef;
    }

    .conversation-participants {
      font-weight: 500;
      color: #333;
    }
  }
}

.tags-list {
  .tag-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #e3f2fd;
      color: #1976d2;
    }

    .tag-icon {
      font-size: 0.8rem;
    }

    .tag-name {
      font-weight: 500;
    }
  }
}

// Responsive
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filter-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    .header-content {
      padding: 0 1rem;
      flex-direction: column;
      gap: 1rem;
    }
  }

  .dashboard-content {
    padding: 0 1rem;
  }
}
