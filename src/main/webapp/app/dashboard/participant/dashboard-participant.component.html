<div class="participant-panel">
  <!-- Header -->
  <div class="participant-header">
    <h3>Participantes</h3>
    <button type="button" class="btn btn-primary btn-sm" (click)="openCreateModal()" [disabled]="isLoading()">
      <fa-icon [icon]="faPlus"></fa-icon>
    </button>
  </div>

  <!-- Search Box -->
  <div class="participant-search" *ngIf="participants().length > 0">
    <div class="search-input-group">
      <fa-icon [icon]="faSearch" class="search-icon"></fa-icon>
      <input
        type="text"
        class="form-control form-control-sm"
        placeholder="Buscar participantes..."
        [(ngModel)]="searchText"
        (ngModelChange)="onSearchChange()"
      />
    </div>
  </div>

  <!-- Participants List -->
  <div class="participant-list">
    @if (isLoading()) {
      <div class="loading-state">
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <span class="ms-2">Cargando participantes...</span>
      </div>
    } @else if (filteredParticipants().length === 0 && participants().length === 0) {
      <div class="empty-state">
        <p>No tienes participantes creados.</p>
        <small>Haz clic en el botón + para crear tu primer participante.</small>
      </div>
    } @else if (filteredParticipants().length === 0 && searchText.trim()) {
      <div class="empty-state">
        <p>No se encontraron participantes que coincidan con "{{ searchText }}".</p>
      </div>
    } @else {
      @for (participant of filteredParticipants(); track participant.id) {
        <div class="participant-item">
          <div class="participant-info">
            <div class="participant-name">
              <fa-icon [icon]="faUser" class="user-icon"></fa-icon>
              <span class="name">{{ getFullName(participant) }}</span>
            </div>
            @if (participant.aliasList && participant.aliasList.length > 0) {
              <div class="participant-aliases">
                @for (alias of participant.aliasList.slice(0, 3); track alias.id) {
                  <span class="alias-badge">
                    <span class="alias-icon">{{ getTypeIcon(alias.type) }}</span>
                    {{ alias.value }}
                  </span>
                }
                @if (participant.aliasList.length > 3) {
                  <span class="alias-more">+{{ participant.aliasList.length - 3 }} más</span>
                }
              </div>
            }
          </div>
          <div class="participant-actions">
            <button
              type="button"
              class="btn btn-outline-primary btn-sm"
              (click)="openEditModal(participant)"
              [disabled]="isLoading()"
              title="Editar participante"
            >
              <fa-icon [icon]="faEdit"></fa-icon>
            </button>
          </div>
        </div>
      }
    }
  </div>
</div>

<!-- Create/Edit Modal -->
<div class="modal-overlay" *ngIf="showModal()" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h4>{{ isEditMode() ? 'Editar Participante' : 'Crear Participante' }}</h4>
      <button type="button" class="btn-close" (click)="closeModal()">
        <fa-icon [icon]="faTimes"></fa-icon>
      </button>
    </div>

    <div class="modal-body">
      <!-- Basic Info -->
      <div class="form-section">
        <h5>Información Personal</h5>
        <div class="form-row">
          <div class="form-group">
            <label for="participantName" class="form-label">Nombre:</label>
            <input
              type="text"
              id="participantName"
              class="form-control form-control-sm"
              [(ngModel)]="currentParticipant.name"
              placeholder="Nombre"
            />
          </div>

          <div class="form-group">
            <label for="participantSurname" class="form-label">Apellido:</label>
            <input
              type="text"
              id="participantSurname"
              class="form-control form-control-sm"
              [(ngModel)]="currentParticipant.surname"
              placeholder="Apellido"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="participantSecondSurname" class="form-label">Segundo Apellido:</label>
            <input
              type="text"
              id="participantSecondSurname"
              class="form-control form-control-sm"
              [(ngModel)]="currentParticipant.secondSurname"
              placeholder="Segundo apellido (opcional)"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="participantEmail" class="form-label">Email:</label>
            <input
              type="email"
              id="participantEmail"
              class="form-control form-control-sm"
              [(ngModel)]="currentParticipant.emailContact"
              placeholder="<EMAIL>"
            />
          </div>

          <div class="form-group">
            <label for="participantMobile" class="form-label">Móvil:</label>
            <input
              type="tel"
              id="participantMobile"
              class="form-control form-control-sm"
              [(ngModel)]="currentParticipant.mobileContact"
              placeholder="+34 600 000 000"
            />
          </div>
        </div>
      </div>

      <!-- Aliases Section -->
      <div class="form-section">
        <h5>Alias Asociados</h5>

        <!-- Selected Aliases -->
        @if (currentParticipant.aliasList && currentParticipant.aliasList.length > 0) {
          <div class="selected-aliases">
            <h6>Alias Seleccionados:</h6>
            @for (alias of currentParticipant.aliasList; track alias.id) {
              <div class="alias-item selected">
                <span class="alias-info">
                  <span class="alias-icon">{{ getTypeIcon(alias.type) }}</span>
                  <span class="alias-value">{{ alias.value }}</span>
                  <span class="alias-type">{{ getTypeLabel(alias.type) }}</span>
                </span>
                <button
                  type="button"
                  class="btn btn-outline-danger btn-sm"
                  (click)="removeAliasFromParticipant(alias)"
                  title="Quitar alias"
                >
                  <fa-icon [icon]="faTimes"></fa-icon>
                </button>
              </div>
            }
          </div>
        }

        <!-- Available Aliases -->
        @if (availableAliases().length > 0) {
          <div class="available-aliases">
            <h6>Alias Disponibles:</h6>
            @for (alias of availableAliases(); track alias.id) {
              @if (!isAliasSelected(alias)) {
                <div class="alias-item available">
                  <span class="alias-info">
                    <span class="alias-icon">{{ getTypeIcon(alias.type) }}</span>
                    <span class="alias-value">{{ alias.value }}</span>
                    <span class="alias-type">{{ getTypeLabel(alias.type) }}</span>
                  </span>
                  <button type="button" class="btn btn-outline-success btn-sm" (click)="addAliasToParticipant(alias)" title="Agregar alias">
                    <fa-icon [icon]="faPlus"></fa-icon>
                  </button>
                </div>
              }
            }
          </div>
        }
      </div>
    </div>

    <div class="modal-footer">
      <button
        type="button"
        class="btn btn-success"
        (click)="saveParticipant()"
        [disabled]="!currentParticipant.name.trim() || !currentParticipant.surname.trim() || isLoading()"
      >
        <fa-icon [icon]="faSave"></fa-icon>
        {{ isEditMode() ? 'Guardar' : 'Crear' }}
      </button>
      @if (isEditMode()) {
        <button type="button" class="btn btn-danger" (click)="deleteParticipant()" [disabled]="isLoading()">
          <fa-icon [icon]="faTrash"></fa-icon>
          Eliminar
        </button>
      }
      <button type="button" class="btn btn-secondary" (click)="closeModal()">Cancelar</button>
    </div>
  </div>
</div>
