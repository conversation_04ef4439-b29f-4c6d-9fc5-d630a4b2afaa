<div>
  <div class="d-flex justify-content-center">
    <div class="col-md-8">
      @if (settingsForm.value.login) {
        <h2 jhiTranslate="settings.title" [translateValues]="{ username: settingsForm.value.login }">
          User settings for [<strong>{{ settingsForm.value.login }}</strong
          >]
        </h2>
      }

      @if (success()) {
        <div class="alert alert-success" jhiTranslate="settings.messages.success"><strong>Settings saved!</strong></div>
      }

      <jhi-alert-error />

      @if (settingsForm.value.login) {
        <form name="form" (ngSubmit)="save()" [formGroup]="settingsForm" novalidate>
          <div class="mb-3">
            <label class="form-label" for="firstName" jhiTranslate="settings.form.firstname">First Name</label>
            <input
              type="text"
              class="form-control"
              id="firstName"
              name="firstName"
              placeholder="{{ 'settings.form.firstname.placeholder' | translate }}"
              formControlName="firstName"
              data-cy="firstname"
            />

            @let firstNameRef = settingsForm.get('firstName')!;
            @if (firstNameRef.invalid && (firstNameRef.dirty || firstNameRef.touched)) {
              <div>
                @if (firstNameRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="settings.messages.validate.firstname.required"
                    >Your first name is required.</small
                  >
                }

                @if (firstNameRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="settings.messages.validate.firstname.minlength"
                    >Your first name is required to be at least 1 character</small
                  >
                }

                @if (firstNameRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="settings.messages.validate.firstname.maxlength"
                    >Your first name cannot be longer than 50 characters</small
                  >
                }
              </div>
            }
          </div>

          <div class="mb-3">
            <label class="form-label" for="lastName" jhiTranslate="settings.form.lastname">Last Name</label>
            <input
              type="text"
              class="form-control"
              id="lastName"
              name="lastName"
              placeholder="{{ 'settings.form.lastname.placeholder' | translate }}"
              formControlName="lastName"
              data-cy="lastname"
            />

            @let lastNameRef = settingsForm.get('lastName')!;
            @if (lastNameRef.invalid && (lastNameRef.dirty || lastNameRef.touched)) {
              <div>
                @if (lastNameRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="settings.messages.validate.lastname.required"
                    >Your last name is required.</small
                  >
                }

                @if (lastNameRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="settings.messages.validate.lastname.minlength"
                    >Your last name is required to be at least 1 character</small
                  >
                }

                @if (lastNameRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="settings.messages.validate.lastname.maxlength"
                    >Your last name cannot be longer than 50 characters</small
                  >
                }
              </div>
            }
          </div>

          <div class="mb-3">
            <label class="form-label" for="email" jhiTranslate="global.form.email.label">Email</label>
            <input
              type="email"
              class="form-control"
              id="email"
              name="email"
              placeholder="{{ 'global.form.email.placeholder' | translate }}"
              formControlName="email"
              data-cy="email"
            />

            @let emailRef = settingsForm.get('email')!;
            @if (emailRef.invalid && (emailRef.dirty || emailRef.touched)) {
              <div>
                @if (emailRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.required"
                    >Your email is required.</small
                  >
                }

                @if (emailRef?.errors?.email) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.invalid">Your email is invalid.</small>
                }

                @if (emailRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.minlength"
                    >Your email is required to be at least 5 characters.</small
                  >
                }

                @if (emailRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.maxlength"
                    >Your email cannot be longer than 50 characters.</small
                  >
                }
              </div>
            }
          </div>

          @if (languages && languages.length > 0) {
            <div class="mb-3">
              <label for="langKey" jhiTranslate="settings.form.language">Language</label>
              <select class="form-control" id="langKey" name="langKey" formControlName="langKey" data-cy="langKey">
                @for (language of languages; track $index) {
                  <option [value]="language">{{ language | findLanguageFromKey }}</option>
                }
              </select>
            </div>
          }

          <button
            type="submit"
            [disabled]="settingsForm.invalid"
            class="btn btn-primary"
            data-cy="submit"
            jhiTranslate="settings.form.button"
          >
            Save
          </button>
        </form>
      }
    </div>
  </div>
</div>
