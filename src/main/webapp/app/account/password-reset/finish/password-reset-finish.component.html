<div>
  <div class="d-flex justify-content-center">
    <div class="col-md-4">
      <h1 jhiTranslate="reset.finish.title">Reset password</h1>

      @if (initialized() && !key()) {
        <div class="alert alert-danger" jhiTranslate="reset.finish.messages.keymissing">The reset key is missing.</div>
      }

      @if (key() && !success()) {
        <div class="alert alert-warning">
          <span jhiTranslate="reset.finish.messages.info">Choose a new password</span>
        </div>
      }

      @if (error()) {
        <div class="alert alert-danger">
          <span jhiTranslate="reset.finish.messages.error"
            >Your password couldn&apos;t be reset. Remember a password request is only valid for 24 hours.</span
          >
        </div>
      }

      @if (success()) {
        <div class="alert alert-success">
          <span jhiTranslate="reset.finish.messages.success"><strong>Your password has been reset.</strong> Please </span>
          <a class="alert-link" routerLink="/login" jhiTranslate="global.messages.info.authenticated.link">sign in</a>.
        </div>
      }

      @if (doNotMatch()) {
        <div class="alert alert-danger" jhiTranslate="global.messages.error.dontmatch">The password and its confirmation do not match!</div>
      }

      @if (key() && !success()) {
        <div>
          <form name="form" (ngSubmit)="finishReset()" [formGroup]="passwordForm">
            <div class="mb-3">
              <label class="form-label" for="newPassword" jhiTranslate="global.form.newpassword.label">New password</label>
              <input
                type="password"
                class="form-control"
                id="newPassword"
                name="newPassword"
                placeholder="{{ 'global.form.newpassword.placeholder' | translate }}"
                formControlName="newPassword"
                data-cy="resetPassword"
              />

              @let newPasswordRef = passwordForm.get('newPassword')!;
              @if (newPasswordRef.invalid && (newPasswordRef.dirty || newPasswordRef.touched)) {
                <div>
                  @if (newPasswordRef?.errors?.required) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.required"
                      >Your password is required.</small
                    >
                  }

                  @if (newPasswordRef?.errors?.minlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.minlength"
                      >Your password is required to be at least 4 characters.</small
                    >
                  }

                  @if (newPasswordRef?.errors?.maxlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.maxlength"
                      >Your password cannot be longer than 50 characters.</small
                    >
                  }
                </div>
              }

              <jhi-password-strength-bar [passwordToCheck]="newPasswordRef.value" />
            </div>

            <div class="mb-3">
              <label class="form-label" for="confirmPassword" jhiTranslate="global.form.confirmpassword.label"
                >New password confirmation</label
              >
              <input
                type="password"
                class="form-control"
                id="confirmPassword"
                name="confirmPassword"
                placeholder="{{ 'global.form.confirmpassword.placeholder' | translate }}"
                formControlName="confirmPassword"
                data-cy="confirmResetPassword"
              />

              @let confirmPasswordRef = passwordForm.get('confirmPassword')!;
              @if (confirmPasswordRef.invalid && (confirmPasswordRef.dirty || confirmPasswordRef.touched)) {
                <div>
                  @if (confirmPasswordRef?.errors?.required) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.required"
                      >Your confirmation password is required.</small
                    >
                  }

                  @if (confirmPasswordRef?.errors?.minlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.minlength"
                      >Your confirmation password is required to be at least 4 characters.</small
                    >
                  }

                  @if (confirmPasswordRef?.errors?.maxlength) {
                    <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.maxlength"
                      >Your confirmation password cannot be longer than 50 characters.</small
                    >
                  }
                </div>
              }
            </div>

            <button
              type="submit"
              [disabled]="passwordForm.invalid"
              class="btn btn-primary"
              data-cy="submit"
              jhiTranslate="reset.finish.form.button"
            >
              Validate new password
            </button>
          </form>
        </div>
      }
    </div>
  </div>
</div>
