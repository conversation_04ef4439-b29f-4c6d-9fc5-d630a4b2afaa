<div>
  <div class="d-flex justify-content-center">
    <div class="col-md-8">
      <h1 jhiTranslate="reset.request.title">Reset your password</h1>

      <jhi-alert-error />

      @if (!success()) {
        <div class="alert alert-warning">
          <span jhiTranslate="reset.request.messages.info">Enter the email address you used to register</span>
        </div>
        <form name="form" (ngSubmit)="requestReset()" [formGroup]="resetRequestForm">
          <div class="mb-3">
            <label class="form-label" for="email" jhiTranslate="global.form.email.label">Email</label>
            <input
              type="email"
              class="form-control"
              id="email"
              name="email"
              placeholder="{{ 'global.form.email.placeholder' | translate }}"
              formControlName="email"
              data-cy="emailResetPassword"
            />

            @let emailRef = resetRequestForm.get('email')!;
            @if (emailRef.invalid && (emailRef.dirty || emailRef.touched)) {
              <div>
                @if (emailRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.required"
                    >Your email is required.</small
                  >
                }
                @if (emailRef?.errors?.email) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.invalid">Your email is invalid.</small>
                }

                @if (emailRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.minlength"
                    >Your email is required to be at least 5 characters.</small
                  >
                }

                @if (emailRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.email.maxlength"
                    >Your email cannot be longer than 50 characters.</small
                  >
                }
              </div>
            }
          </div>
          <button
            type="submit"
            [disabled]="resetRequestForm.invalid"
            class="btn btn-primary"
            data-cy="submit"
            jhiTranslate="reset.request.form.button"
          >
            Reset password
          </button>
        </form>
      } @else {
        <div class="alert alert-success">
          <span jhiTranslate="reset.request.messages.success">Check your email for details on how to reset your password.</span>
        </div>
      }
    </div>
  </div>
</div>
