import { Component } from '@angular/core';
import { FormGroup, FormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { RegisterService } from './register.service';
import SharedModule from 'app/shared/shared.module';
import { faUserPlus, faTimes } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'jhi-register-modal',
  templateUrl: './register-modal.component.html',
  standalone: true,
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class RegisterModalComponent {
  registerError = false;
  registerSuccess = false;

  registerForm = new FormGroup({
    login: new FormControl('', {
      nonNullable: true,
      validators: [Validators.required, Validators.minLength(5), Validators.maxLength(254), Validators.email],
    }),
    password: new FormControl('', {
      nonNullable: true,
      validators: [Validators.required, Validators.minLength(4), Validators.maxLength(50)],
    }),
    confirmPassword: new FormControl('', {
      nonNullable: true,
      validators: [Validators.required, Validators.minLength(4), Validators.maxLength(50)],
    }),
  });

  faUserPlus = faUserPlus;
  faTimes = faTimes;

  constructor(
    private registerService: RegisterService,
    private translateService: TranslateService,
    public activeModal: NgbActiveModal,
  ) {}

  register(): void {
    const { login, password, confirmPassword } = this.registerForm.getRawValue();

    if (password !== confirmPassword) {
      this.registerError = true;
      return;
    }

    this.registerService
      .save({
        login,
        email: login,
        password,
        langKey: this.translateService.currentLang,
      })
      .subscribe({
        next: () => {
          this.registerSuccess = true;
          this.registerError = false;
        },
        error: () => {
          this.registerSuccess = false;
          this.registerError = true;
        },
      });
  }

  dismiss(): void {
    this.activeModal.dismiss();
  }
}
