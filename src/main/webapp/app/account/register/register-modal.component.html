<div class="modal-header">
  <h2>Create Account</h2>
  <button class="modal-close" (click)="dismiss()">
    <fa-icon [icon]="faTimes"></fa-icon>
  </button>
</div>

<div class="modal-body">
  <div class="alert alert-success" *ngIf="registerSuccess">
    <strong>Registration saved!</strong> Please check your email for confirmation.
  </div>

  <div class="alert alert-danger" *ngIf="registerError"><strong>Registration failed!</strong> Please try again.</div>

  <form class="auth-form" (ngSubmit)="register()" [formGroup]="registerForm" *ngIf="!registerSuccess">
    <div class="form-group">
      <label for="email">Email</label>
      <input type="email" id="email" class="form-control" formControlName="login" placeholder="Your email" />
    </div>

    <div class="form-group">
      <label for="password">Password</label>
      <input type="password" id="password" class="form-control" formControlName="password" placeholder="Your password" />
    </div>

    <div class="form-group">
      <label for="confirmPassword">Confirm Password</label>
      <input
        type="password"
        id="confirmPassword"
        class="form-control"
        formControlName="confirmPassword"
        placeholder="Confirm your password"
      />
    </div>

    <button type="submit" class="btn-primary" [disabled]="registerForm.invalid">
      <fa-icon [icon]="faUserPlus"></fa-icon>
      <span>Create Account</span>
    </button>
  </form>

  <div class="success-message" *ngIf="registerSuccess">
    <p>Your account has been created successfully!</p>
    <p>Please check your email for the confirmation link.</p>
    <button class="btn-primary" (click)="dismiss()">Close</button>
  </div>
</div>

<div class="modal-footer" *ngIf="!registerSuccess">
  <p>Already have an account? <a href="#" (click)="dismiss()">Sign in here</a></p>
</div>
