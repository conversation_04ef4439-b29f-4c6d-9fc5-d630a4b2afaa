<div>
  <div class="d-flex justify-content-center">
    @if (account) {
      <div class="col-md-8">
        <h2 jhiTranslate="password.title" [translateValues]="{ username: account()?.login }">
          Password for [<strong>{{ account()?.login }}</strong
          >]
        </h2>

        @if (success()) {
          <div class="alert alert-success" jhiTranslate="password.messages.success"><strong>Password changed!</strong></div>
        }
        @if (error()) {
          <div class="alert alert-danger" jhiTranslate="password.messages.error">
            <strong>An error has occurred!</strong> The password could not be changed.
          </div>
        }
        @if (doNotMatch()) {
          <div class="alert alert-danger" jhiTranslate="global.messages.error.dontmatch">
            The password and its confirmation do not match!
          </div>
        }

        <form name="form" (ngSubmit)="changePassword()" [formGroup]="passwordForm">
          <div class="mb-3">
            <label class="form-label" for="currentPassword" jhiTranslate="global.form.currentpassword.label">Current password</label>
            <input
              type="password"
              class="form-control"
              id="currentPassword"
              name="currentPassword"
              placeholder="{{ 'global.form.currentpassword.placeholder' | translate }}"
              formControlName="currentPassword"
              data-cy="currentPassword"
            />

            @let currentPasswordRef = passwordForm.get('currentPassword')!;
            @if (currentPasswordRef.invalid && (currentPasswordRef.dirty || currentPasswordRef.touched)) {
              <div>
                @if (currentPasswordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.required"
                    >Your password is required.</small
                  >
                }
              </div>
            }
          </div>

          <div class="mb-3">
            <label class="form-label" for="newPassword" jhiTranslate="global.form.newpassword.label">New password</label>
            <input
              type="password"
              class="form-control"
              id="newPassword"
              name="newPassword"
              placeholder="{{ 'global.form.newpassword.placeholder' | translate }}"
              formControlName="newPassword"
              data-cy="newPassword"
            />

            @let newPasswordRef = passwordForm.get('newPassword')!;
            @if (newPasswordRef.invalid && (newPasswordRef.dirty || newPasswordRef.touched)) {
              <div>
                @if (newPasswordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.required"
                    >Your password is required.</small
                  >
                }

                @if (newPasswordRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.minlength"
                    >Your password is required to be at least 4 characters.</small
                  >
                }

                @if (newPasswordRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.newpassword.maxlength"
                    >Your password cannot be longer than 50 characters.</small
                  >
                }
              </div>
            }

            <jhi-password-strength-bar [passwordToCheck]="newPasswordRef.value" />
          </div>

          <div class="mb-3">
            <label class="form-label" for="confirmPassword" jhiTranslate="global.form.confirmpassword.label"
              >New password confirmation</label
            >
            <input
              type="password"
              class="form-control"
              id="confirmPassword"
              name="confirmPassword"
              placeholder="{{ 'global.form.confirmpassword.placeholder' | translate }}"
              formControlName="confirmPassword"
              data-cy="confirmPassword"
            />

            @let confirmPasswordRef = passwordForm.get('confirmPassword')!;
            @if (confirmPasswordRef.invalid && (confirmPasswordRef.dirty || confirmPasswordRef.touched)) {
              <div>
                @if (confirmPasswordRef?.errors?.required) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.required"
                    >Your confirmation password is required.</small
                  >
                }

                @if (confirmPasswordRef?.errors?.minlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.minlength"
                    >Your confirmation password is required to be at least 4 characters.</small
                  >
                }

                @if (confirmPasswordRef?.errors?.maxlength) {
                  <small class="form-text text-danger" jhiTranslate="global.messages.validate.confirmpassword.maxlength"
                    >Your confirmation password cannot be longer than 50 characters.</small
                  >
                }
              </div>
            }
          </div>

          <button
            type="submit"
            [disabled]="passwordForm.invalid"
            class="btn btn-primary"
            data-cy="submit"
            jhiTranslate="password.form.button"
          >
            Save
          </button>
        </form>
      </div>
    }
  </div>
</div>
