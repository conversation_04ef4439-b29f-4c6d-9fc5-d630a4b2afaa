export enum Feeling {
  // Positive Feelings
  HAPPY = 'HAPPY',
  EXCITED = 'EXCITED',
  GRATEFUL = 'GRATEFUL',
  LOVE = 'LOVE',
  PROUD = 'PROUD',
  HOPEFUL = 'HOPEFUL',

  // Negative Feelings
  SAD = 'SAD',
  ANGRY = 'ANGRY',
  FRUSTRATED = 'FRUSTRATED',
  DISAPPOINTED = 'DISAPPOINTED',
  FEARFUL = 'FEARFUL',
  ANXIOUS = 'ANXIOUS',
  BLOCKED = 'BLOCKED',

  // Neutral/Complex Feelings
  NEUTRAL = 'NEUTRAL',
  SURPRISED = 'SURPRISED',
  CONFUSED = 'CONFUSED',
  THOUGHTFUL = 'THOUGHTFUL',
  CALM = 'CALM',
  CURIOUS = 'CURIOUS',
}

export interface FeelingInfo {
  emoji: string;
  label: string;
  color: string;
  fontAwesome: string;
}

export const FEELING_INFO: Record<Feeling, FeelingInfo> = {
  // Positive Feelings
  [Feeling.HAPPY]: {
    emoji: '😊',
    label: 'Feliz',
    color: '#FFD700',
    fontAwesome: 'fa-regular fa-face-smile',
  },
  [Feeling.EXCITED]: {
    emoji: '🤩',
    label: 'Emocionado',
    color: '#FF6B35',
    fontAwesome: 'fa-regular fa-face-grin-stars',
  },
  [Feeling.GRATEFUL]: {
    emoji: '🙏',
    label: 'Agradecido',
    color: '#32CD32',
    fontAwesome: 'fa-solid fa-hand-holding-heart',
  },
  [Feeling.LOVE]: {
    emoji: '❤️',
    label: 'Amor',
    color: '#FF1493',
    fontAwesome: 'fa-regular fa-heart',
  },
  [Feeling.PROUD]: {
    emoji: '🏆',
    label: 'Orgulloso',
    color: '#FFD700',
    fontAwesome: 'fa-solid fa-award',
  },
  [Feeling.HOPEFUL]: {
    emoji: '🌟',
    label: 'Esperanzado',
    color: '#87CEEB',
    fontAwesome: 'fa-solid fa-star',
  },

  // Negative Feelings
  [Feeling.SAD]: {
    emoji: '😢',
    label: 'Triste',
    color: '#4682B4',
    fontAwesome: 'fa-regular fa-face-frown',
  },
  [Feeling.ANGRY]: {
    emoji: '😠',
    label: 'Enojado',
    color: '#DC143C',
    fontAwesome: 'fa-regular fa-face-angry',
  },
  [Feeling.FRUSTRATED]: {
    emoji: '😤',
    label: 'Frustrado',
    color: '#FF4500',
    fontAwesome: 'fa-regular fa-face-tired',
  },
  [Feeling.DISAPPOINTED]: {
    emoji: '😞',
    label: 'Decepcionado',
    color: '#708090',
    fontAwesome: 'fa-regular fa-face-sad-tear',
  },
  [Feeling.FEARFUL]: {
    emoji: '😨',
    label: 'Temeroso',
    color: '#9370DB',
    fontAwesome: 'fa-regular fa-face-flushed',
  },
  [Feeling.ANXIOUS]: {
    emoji: '😰',
    label: 'Ansioso',
    color: '#FF6347',
    fontAwesome: 'fa-solid fa-exclamation-triangle',
  },
  [Feeling.BLOCKED]: {
    emoji: '🚫',
    label: 'Bloqueado',
    color: '#8B0000',
    fontAwesome: 'fa-solid fa-ban',
  },

  // Neutral/Complex Feelings
  [Feeling.NEUTRAL]: {
    emoji: '😐',
    label: 'Neutral',
    color: '#808080',
    fontAwesome: 'fa-regular fa-face-meh',
  },
  [Feeling.SURPRISED]: {
    emoji: '😲',
    label: 'Sorprendido',
    color: '#FFA500',
    fontAwesome: 'fa-regular fa-face-surprise',
  },
  [Feeling.CONFUSED]: {
    emoji: '🤔',
    label: 'Confundido',
    color: '#DDA0DD',
    fontAwesome: 'fa-regular fa-circle-question',
  },
  [Feeling.THOUGHTFUL]: {
    emoji: '🧠',
    label: 'Pensativo',
    color: '#20B2AA',
    fontAwesome: 'fa-solid fa-brain',
  },
  [Feeling.CALM]: {
    emoji: '😌',
    label: 'Tranquilo',
    color: '#98FB98',
    fontAwesome: 'fa-solid fa-hand-peace',
  },
  [Feeling.CURIOUS]: {
    emoji: '🔍',
    label: 'Curioso',
    color: '#FFB6C1',
    fontAwesome: 'fa-solid fa-magnifying-glass',
  },
};
