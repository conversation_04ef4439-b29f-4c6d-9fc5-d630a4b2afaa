<div class="mb-3" [formGroup]="formGroup">
  <label [for]="'alias-' + controlName" class="form-label" jhiTranslate="">{{ labelI18nKey }}</label>
  <select
    [id]="'alias-' + controlName"
    class="form-select"
    [formControlName]="controlName"
    [class.is-invalid]="isInvalid"
    (change)="onAliasSelected($event)"
    [disabled]="isLoading"
  >
    <option [ngValue]="null" jhiTranslate="entity.action.select">Select</option>
    @if (isLoading) {
      <option [ngValue]="null" disabled>Cargando...</option>
    } @else {
      <option *ngFor="let alias of availableAliases" [ngValue]="alias.id">
        {{ getAliasValue(alias) }}
      </option>
    }
  </select>
  <div class="invalid-feedback" *ngIf="isInvalid">
    {{ errorMessage }}
  </div>
</div>
