import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

@Component({
  selector: 'jhi-simple-toast',
  templateUrl: './simple-toast.component.html',
  styleUrls: ['./simple-toast.component.scss'],
})
export class SimpleToastComponent implements OnInit {
  @Input() message = '';
  @Input() duration = 3000;
  @Output() closed = new EventEmitter<void>();

  ngOnInit(): void {
    setTimeout(() => this.closed.emit(), this.duration);
  }
}
