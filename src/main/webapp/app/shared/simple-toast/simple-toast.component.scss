.simple-toast {
  position: fixed;
  top: 1.5rem;
  right: 1.5rem;
  background: #323232;
  color: #fff;
  padding: 1rem 2.5rem 1rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1055;
  display: flex;
  align-items: center;
  min-width: 200px;
  max-width: 350px;
  font-size: 1rem;
  opacity: 0.97;
  animation: fadeIn 0.3s;
}
.close-btn {
  background: transparent;
  border: none;
  color: #fff;
  font-size: 1.3rem;
  margin-left: 1rem;
  cursor: pointer;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 0.97;
    transform: translateY(0);
  }
}
