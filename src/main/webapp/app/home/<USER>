<!-- Custom Header -->
<div class="home-header">
  <div class="header-content">
    <!-- Logo -->
    <div class="header-logo">
      <fa-icon [icon]="faComments" class="logo-icon"></fa-icon>
      <span class="logo-text">ForConversations</span>
    </div>

    <!-- Header Actions -->
    <div class="header-actions">
      <!-- Language Selector -->
      <div class="language-selector" [class.active]="showLanguageDropdown()">
        <button class="language-button" (click)="toggleLanguageDropdown()">
          <span class="flag">{{ getLanguageFlag(currentLanguage()) }}</span>
          <span class="language-name">{{ getLanguageName(currentLanguage()) }}</span>
          <fa-icon [icon]="faGlobe" class="globe-icon"></fa-icon>
        </button>

        @if (showLanguageDropdown()) {
          <div class="language-dropdown">
            @for (language of languages; track language) {
              <button class="language-option" [class.active]="language === currentLanguage()" (click)="changeLanguage(language)">
                <span class="flag">{{ getLanguageFlag(language) }}</span>
                <span class="name">{{ getLanguageName(language) }}</span>
              </button>
            }
          </div>
        }
      </div>

      <!-- Authentication Buttons -->
      @if (!account()) {
        <div class="auth-buttons">
          <button class="btn-signin" (click)="openLoginModal()">
            <fa-icon [icon]="faSignInAlt"></fa-icon>
            <span>Sign in</span>
          </button>
          <button class="btn-register" (click)="openRegisterModal()">
            <fa-icon [icon]="faUserPlus"></fa-icon>
            <span>Register</span>
          </button>
        </div>
      }
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="home-container">
  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-content">
      <div class="feature-icons-horizontal">
        <fa-icon [icon]="faWhatsapp" class="feature-icon"></fa-icon>
        <fa-icon [icon]="faEnvelope" class="feature-icon"></fa-icon>
        <fa-icon [icon]="faMicrophone" class="feature-icon"></fa-icon>
      </div>
      <h1 class="hero-title">For Conversations</h1>
      <p class="hero-subtitle">"La cura para tus conversaciones"</p>
      <p class="hero-description">La plataforma inteligente que unifica tus conversaciones para búsquedas inteligentes.</p>
    </div>
  </div>
</div>
