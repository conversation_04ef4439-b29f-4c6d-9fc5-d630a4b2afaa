/* Custom Header Styles */
.home-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;

  .logo-icon {
    font-size: 1.8rem;
    color: #1e3c72;
  }

  .logo-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3c72;
    letter-spacing: -0.5px;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

/* Language Selector */
.language-selector {
  position: relative;

  .language-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: transparent;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
      border-color: #1e3c72;
    }

    .flag {
      font-size: 1.2rem;
    }

    .language-name {
      font-size: 0.9rem;
      font-weight: 500;
      color: #333;
    }

    .globe-icon {
      font-size: 0.8rem;
      color: #666;
    }
  }

  .language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 150px;
    z-index: 1001;

    .language-option {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      width: 100%;
      padding: 0.75rem 1rem;
      background: transparent;
      border: none;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: #f5f5f5;
      }

      &.active {
        background: #e3f2fd;
        color: #1e3c72;
      }

      .flag {
        font-size: 1.1rem;
      }

      .name {
        font-size: 0.9rem;
        font-weight: 500;
      }
    }
  }
}

/* Authentication Buttons */
.auth-buttons {
  display: flex;
  gap: 1rem;

  .btn-signin,
  .btn-register {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;

    fa-icon {
      font-size: 0.9rem;
    }
  }

  .btn-signin {
    background: transparent;
    color: #1e3c72;
    border: 1px solid #1e3c72;

    &:hover {
      background: #1e3c72;
      color: white;
    }
  }

  .btn-register {
    background: #1e3c72;
    color: white;

    &:hover {
      background: #2a5298;
      transform: translateY(-1px);
    }
  }
}

.user-welcome {
  display: flex;
  align-items: center;
  gap: 1rem;

  span {
    color: #333;
    font-weight: 500;
  }

  .btn-dashboard {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #1e3c72;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;

    &:hover {
      background: #2a5298;
      transform: translateY(-1px);
    }
  }
}

.home-container {
  min-height: 100vh;
  padding-top: 80px; /* Space for fixed header */
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 50%, #ffff 100%);
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.hero-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  position: relative;
}

.hero-content {
  max-width: 800px;
  z-index: 2;
}

.hero-icon {
  font-size: 3.5rem;
  color: #1e3c72;
  margin-bottom: 1.5rem;
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 300;
  color: #1e3c72;
  margin-bottom: 1rem;
  letter-spacing: -1px;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: #e91e63;
  font-weight: 500;
  margin-bottom: 2rem;
  font-style: italic;
}

.hero-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 3rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Feature Icons Horizontal Layout */
.feature-icons-horizontal {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;

  .feature-icon {
    font-size: 3rem;
    color: #1e3c72;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      color: #2a5aa0;
    }
  }
}

// Decorative elements
.hero-section::before {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(233, 30, 99, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
}

.hero-section::after {
  content: '';
  position: absolute;
  bottom: 30%;
  left: 15%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(74, 144, 226, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 1;
}

// Responsive design
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .feature-icons-horizontal {
    gap: 1.5rem;

    .feature-icon {
      font-size: 2.5rem;
    }
  }

  .hero-section {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .feature-icons-horizontal {
    gap: 1rem;

    .feature-icon {
      font-size: 2rem;
    }
  }
}
