import { Component, OnInit, OnDestroy, signal, inject } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { AccountService } from 'app/core/auth/account.service';
import { Account } from 'app/core/auth/account.model';
import { StateStorageService } from 'app/core/auth/state-storage.service';
import SharedModule from 'app/shared/shared.module';
import { LANGUAGES } from 'app/config/language.constants';
import {
  faComments,
  faEnvelope,
  faMicrophone,
  faCheckCircle,
  faProjectDiagram,
  faSignInAlt,
  faRocket,
  faUserPlus,
  faGlobe,
  faTimes,
} from '@fortawesome/free-solid-svg-icons';
import { faWhatsapp } from '@fortawesome/free-brands-svg-icons';
import { LoginModalComponent } from 'app/login/login-modal.component';
import { RegisterModalComponent } from 'app/account/register/register-modal.component';

@Component({
  selector: 'jhi-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [SharedModule, RouterModule],
})
export class HomeComponent implements OnInit, OnDestroy {
  account = signal<Account | null>(null);

  // UI State
  showLanguageDropdown = signal(false);

  // Languages
  languages = LANGUAGES;
  currentLanguage = signal('en');

  // Font Awesome icons
  faComments = faComments;
  faWhatsapp = faWhatsapp;
  faEnvelope = faEnvelope;
  faMicrophone = faMicrophone;
  faCheckCircle = faCheckCircle;
  faProjectDiagram = faProjectDiagram;
  faSignInAlt = faSignInAlt;
  faRocket = faRocket;
  faUserPlus = faUserPlus;
  faGlobe = faGlobe;
  faTimes = faTimes;

  private readonly destroy$ = new Subject<void>();
  private readonly accountService = inject(AccountService);
  private readonly router = inject(Router);
  private readonly translateService = inject(TranslateService);
  private readonly stateStorageService = inject(StateStorageService);
  private modalService: NgbModal;

  constructor() {
    this.modalService = inject(NgbModal);
  }

  ngOnInit(): void {
    this.accountService
      .getAuthenticationState()
      .pipe(takeUntil(this.destroy$))
      .subscribe(account => {
        this.account.set(account);
        // Redirect authenticated users to dashboard
        if (account) {
          this.router.navigate(['/dashboard']); // Cambiado de '/message' a '/dashboard'
        }
      });

    // Set current language
    this.currentLanguage.set(this.translateService.currentLang || 'en');
  }

  // Authentication methods
  openLoginModal(): void {
    this.modalService.open(LoginModalComponent, { size: 'md' });
  }

  openRegisterModal(): void {
    this.modalService.open(RegisterModalComponent, { size: 'md' });
  }

  // Language methods
  toggleLanguageDropdown(): void {
    this.showLanguageDropdown.set(!this.showLanguageDropdown());
  }

  changeLanguage(languageKey: string): void {
    this.currentLanguage.set(languageKey);
    this.stateStorageService.storeLocale(languageKey);
    this.translateService.use(languageKey);
    this.showLanguageDropdown.set(false);
  }

  getLanguageFlag(language: string): string {
    switch (language) {
      case 'es':
        return '🇪🇸';
      case 'en':
        return '🇬🇧';
      default:
        return '🌐';
    }
  }

  getLanguageName(language: string): string {
    switch (language) {
      case 'es':
        return 'Español';
      case 'en':
        return 'English';
      default:
        return language;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
