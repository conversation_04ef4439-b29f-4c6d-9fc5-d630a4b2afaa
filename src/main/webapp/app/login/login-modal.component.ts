import { Component, inject } from '@angular/core';
import { FormGroup, FormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import { LoginService } from 'app/login/login.service';
import SharedModule from 'app/shared/shared.module';
import { faSignInAlt, faTimes } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'jhi-login-modal',
  templateUrl: './login-modal.component.html',
  standalone: true,
  imports: [SharedModule, FormsModule, ReactiveFormsModule],
})
export class LoginModalComponent {
  authenticationError = false;

  loginForm = new FormGroup({
    username: new FormControl('', {
      nonNullable: true,
      validators: [Validators.required],
    }),
    password: new FormControl('', {
      nonNullable: true,
      validators: [Validators.required],
    }),
    rememberMe: new FormControl(false, {
      nonNullable: true,
    }),
  });

  faSignInAlt = faSignInAlt;
  faTimes = faTimes;

  private loginService = inject(LoginService);
  private router = inject(Router);

  constructor(public activeModal: NgbActiveModal) {}

  login(): void {
    this.loginService.login(this.loginForm.getRawValue()).subscribe({
      next: () => {
        this.authenticationError = false;
        this.activeModal.close();
        this.router.navigate(['/dashboard']); // Asegurar que navega a dashboard
      },
      error: () => {
        this.authenticationError = true;
      },
    });
  }

  dismiss(): void {
    this.activeModal.dismiss();
  }
}
