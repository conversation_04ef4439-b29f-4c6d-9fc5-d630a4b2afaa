/* Modal Styles */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e0e0e0;

  h2 {
    margin: 0;
    color: #1e3c72;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .modal-close {
    background: transparent;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }
  }
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
  margin-top: 1.5rem;
  text-align: center;

  p {
    margin: 0;
    font-size: 0.9rem;
    color: #666;

    a {
      color: #1e3c72;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

/* Form Styles */
.auth-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #333;
    }

    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 1rem;
      transition: border-color 0.3s ease;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #1e3c72;
        box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
      }
    }
  }

  .form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;

    .form-check-input {
      margin: 0;
    }

    .form-check-label {
      font-size: 0.9rem;
      color: #666;
      cursor: pointer;
    }
  }

  .btn-primary {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem;
    background: #1e3c72;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: #2a5298;
      transform: translateY(-1px);
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }
  }
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;

  &.alert-danger {
    background: #ffeaea;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
  }

  &.alert-success {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .modal-header {
    padding: 1rem 1.5rem;

    h2 {
      font-size: 1.3rem;
    }
  }

  .modal-body {
    padding: 1.5rem;
  }
}
