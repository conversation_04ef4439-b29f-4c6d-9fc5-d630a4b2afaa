<div class="modal-header">
  <h2>Sign in</h2>
  <button class="modal-close" (click)="dismiss()">
    <fa-icon [icon]="faTimes"></fa-icon>
  </button>
</div>

<div class="modal-body">
  <div class="alert alert-danger" *ngIf="authenticationError">
    <strong>Failed to sign in!</strong> Please check your credentials and try again.
  </div>

  <form class="auth-form" (ngSubmit)="login()" [formGroup]="loginForm">
    <div class="form-group">
      <label for="username">Username</label>
      <input type="text" id="username" class="form-control" formControlName="username" placeholder="Your username" />
    </div>

    <div class="form-group">
      <label for="password">Password</label>
      <input type="password" id="password" class="form-control" formControlName="password" placeholder="Your password" />
    </div>

    <div class="form-check">
      <input type="checkbox" id="rememberMe" class="form-check-input" formControlName="rememberMe" />
      <label for="rememberMe" class="form-check-label">Remember me</label>
    </div>

    <button type="submit" class="btn-primary" [disabled]="loginForm.invalid">
      <fa-icon [icon]="faSignInAlt"></fa-icon>
      <span>Sign in</span>
    </button>
  </form>
</div>
