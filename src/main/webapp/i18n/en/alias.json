{"forconversationsApp": {"alias": {"home": {"title": "Aliases", "refreshListLabel": "Refresh list", "createLabel": "Create a new <PERSON><PERSON>", "createOrEditLabel": "Create or edit a Alias", "search": "Search for <PERSON><PERSON>", "notFound": "No Aliases found"}, "created": "A new Alias is created with identifier {{ param }}", "updated": "A Alias is updated with identifier {{ param }}", "deleted": "A Alias is deleted with identifier {{ param }}", "delete": {"question": "Are you sure you want to delete <PERSON><PERSON> {{ id }}?"}, "detail": {"title": "<PERSON><PERSON>"}, "id": "ID", "value": "Value", "type": "Type"}}}