{"metrics": {"title": "Application Metrics", "refresh.button": "Refresh", "updating": "Updating...", "jvm": {"title": "JVM Metrics", "memory": {"title": "Memory", "total": "Total Memory", "heap": "Heap Memory", "nonheap": "Non-Heap Memory"}, "threads": {"title": "Threads", "all": "All", "runnable": "Runnable", "timedwaiting": "Timed waiting", "waiting": "Waiting", "blocked": "Blocked", "dump": {"title": "Threads dump", "id": "Id: ", "blockedtime": "Blocked Time", "blockedcount": "Blocked Count", "waitedtime": "Waited Time", "waitedcount": "Waited Count", "lockname": "Lock name", "stacktrace": "Stacktrace", "show": "Show Stacktrace", "hide": "<PERSON>de Stacktrace"}}, "gc": {"title": "Garbage collections", "marksweepcount": "<PERSON> Sweep count", "marksweeptime": "<PERSON>weep time", "scavengecount": "Scavenge count", "scavengetime": "Scavenge time"}, "http": {"title": "HTTP requests (time in millisecond)", "active": "Active requests:", "total": "Total requests:", "table": {"code": "Code", "count": "Count", "mean": "Mean", "average": "Average", "max": "Max"}, "code": {"ok": "Ok", "notfound": "Not found", "servererror": "Server Error"}}}, "servicesstats": {"title": "Services statistics (time in millisecond)", "table": {"name": "Service name", "count": "Count", "mean": "Mean", "min": "Min", "max": "Max", "p50": "p50", "p75": "p75", "p95": "p95", "p99": "p99"}}, "cache": {"title": "Cache statistics", "cachename": "Cache name", "hits": "<PERSON><PERSON>", "misses": "<PERSON><PERSON>", "gets": "<PERSON><PERSON>", "puts": "<PERSON><PERSON>", "removals": "<PERSON><PERSON>", "evictions": "<PERSON><PERSON>", "hitPercent": "<PERSON><PERSON> Hit %", "missPercent": "<PERSON><PERSON> %", "averageGetTime": "Average get time (µs)", "averagePutTime": "Average put time (µs)", "averageRemoveTime": "Average remove time (µs)"}, "datasource": {"usage": "Connection Pool Usage", "title": "DataSource statistics (time in millisecond)", "name": "Pool usage", "count": "Count", "mean": "Mean", "min": "Min", "max": "Max", "p50": "p50", "p75": "p75", "p95": "p95", "p99": "p99"}}}