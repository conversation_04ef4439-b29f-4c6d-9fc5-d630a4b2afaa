{"metrics": {"title": "Métricas de aplicación", "refresh.button": "<PERSON><PERSON><PERSON><PERSON>", "updating": "Actualizando...", "jvm": {"title": "Métricas de JVM", "memory": {"title": "Memoria", "total": "Memoria total", "heap": "Memoria del Heap", "nonheap": "Memoria no Heap"}, "threads": {"title": "<PERSON><PERSON>", "all": "Todo", "runnable": "Ejecutables", "timedwaiting": "Tiempo esperando", "waiting": "<PERSON><PERSON><PERSON><PERSON>", "blocked": "Bloqueados", "dump": {"title": "Volcado de hilos", "id": "Id: ", "blockedtime": "Tiempo bloqueado", "blockedcount": "Contador blo<PERSON>", "waitedtime": "Tiempo en espera", "waitedcount": "Contador en espera", "lockname": "Nombre del bloqueo", "stacktrace": "Stacktrace", "show": "Mostrar", "hide": "Ocultar"}}, "gc": {"title": "Colecciones de basura", "marksweepcount": "<PERSON> Sweep count", "marksweeptime": "<PERSON>weep time", "scavengecount": "Scavenge count", "scavengetime": "Scavenge time"}, "http": {"title": "Peticiones HTTP (eventos por segundo)", "active": "Peticiones activas:", "total": "Peticiones totales:", "table": {"code": "Código", "count": "Recuento", "mean": "Media", "average": "Promedio", "max": "Max"}, "code": {"ok": "Ok", "notfound": "No encontrado", "servererror": "<PERSON><PERSON><PERSON> de servidor"}}}, "servicesstats": {"title": "Estadísticas de servicios (tiempo en milisegundos)", "table": {"name": "Nombre del servicio", "count": "<PERSON><PERSON><PERSON>", "mean": "Media", "min": "Min", "max": "Max", "p50": "p50", "p75": "p75", "p95": "p95", "p99": "p99"}}, "cache": {"title": "Estadísticas de cache", "cachename": "Nombre cache", "hits": "Accesos", "misses": "Fall<PERSON>", "evictions": "<PERSON><PERSON><PERSON>"}, "datasource": {"usage": "<PERSON><PERSON>", "title": "Estadísticas de la fuente de datos (tiempo en milisegundos)", "name": "Uso del pool", "count": "Recuento", "mean": "Media", "min": "Min", "max": "Max", "p50": "p50", "p75": "p75", "p95": "p95", "p99": "p99"}}}