<!doctype html>
<html class="no-js" lang="en" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>forconversations</title>
    <meta name="description" content="Description for forconversations" />
    <meta name="google" content="notranslate" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="theme-color" content="#000000" />
    <base href="/" />
    <link rel="icon" href="favicon.ico" />
    <link rel="manifest" href="manifest.webapp" />
    <link rel="stylesheet" href="content/css/loading.css" />
    <!-- jhipster-needle-add-resources-to-root - JHipster will add new resources here -->
  </head>
  <body>
    <!--[if lt IE 9]>
      <p class="browserupgrade">
        You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> to improve
        your experience.
      </p>
    <![endif]-->
    <jhi-app>
      <div class="app-loading">
        <div class="lds-pacman">
          <div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <div>
            <div></div>
            <div></div>
            <div></div>
          </div>
        </div>
      </div>
      <div class="app-loading">
        <div id="jhipster-error" style="display: none">
          <!-- This content is for troubleshooting purpose and will be removed when app renders -->
          <h1>An error has occurred :-(</h1>
          <h2>Usual error causes</h2>
          <ol>
            <li>
              You started the application from an IDE and you didn't run <code style="color: red">npm start</code> or
              <code style="color: red">npm run webapp:build</code>.
            </li>
            <li>
              You had a network error while running <code style="color: red">npm install</code>. If you are behind a corporate proxy, it is
              likely that this error was caused by your proxy. Have a look at the JHipster error logs, you will probably have the cause of
              the error.
            </li>
            <li>
              You installed a Node.js version that doesn't work with JHipster: please use an LTS (long-term support) version, as it's the
              only version we support.
            </li>
          </ol>
          <h2>Building the client side code again</h2>
          <p>If you want to go fast, run <code style="color: red">./mvnw</code> to build and run everything.</p>
          <p>If you want to have more control, so you can debug your issue more easily, you should follow the following steps:</p>
          <ol>
            <li>Install npm dependencies with the command <code style="color: red">npm install</code></li>
            <li>
              Build the client with the command <code style="color: red">npm run webapp:build</code> or
              <code style="color: red">npm start</code>
            </li>
            <li>Start the server with <code style="color: red">./mvnw</code> or using your IDE</li>
          </ol>

          <h2>Getting more help</h2>

          <h3>If you have a question on how to use JHipster</h3>
          <p>
            Go to Stack Overflow with the
            <a href="http://stackoverflow.com/tags/jhipster" target="_blank" rel="noopener noreferrer">"jhipster"</a> tag.
          </p>

          <h3>If you have a bug or a feature request</h3>
          <p>
            First read our
            <a href="https://github.com/jhipster/generator-jhipster/blob/main/CONTRIBUTING.md" target="_blank" rel="noopener noreferrer"
              >contributing guidelines</a
            >.
          </p>
          <p>
            Then, fill a ticket on our
            <a href="https://github.com/jhipster/generator-jhipster/issues/new/choose" target="_blank" rel="noopener noreferrer"
              >bug tracker</a
            >, we'll be happy to resolve your issue!
          </p>

          <h3>If you want to chat with contributors and other users</h3>
          <p>
            Join our chat room on
            <a href="https://gitter.im/jhipster/generator-jhipster" target="_blank" rel="noopener noreferrer">Gitter.im</a>. Please note
            that this is a public chat room, and that we expect you to respect other people and write in a correct English language!
          </p>
          <!-- end of troubleshooting content -->
        </div>
      </div>
    </jhi-app>
    <noscript>
      <h1>You must enable JavaScript to view this page.</h1>
    </noscript>
    <script type="text/javascript">
      // show an error message if the app loading takes more than 4 sec
      window.onload = function () {
        setTimeout(showError, 4000);
      };
      function showError() {
        const errorElm = document.getElementById('jhipster-error');
        if (errorElm?.style) {
          errorElm.style.display = 'block';
        }
      }
    </script>
  </body>
</html>
