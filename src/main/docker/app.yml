# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: forconversations
services:
  app:
    image: forconversations
    environment:
      - _JAVA_OPTIONS=-Xmx512m -Xms256m
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - <PERSON>NAGEMENT_PROMETHEUS_METRICS_EXPORT_ENABLED=true
      - SPRING_DATA_MONGODB_URI=mongodb://mongodb:27017/forconversations?waitQueueMultiple=1000
      - SPRING_ELASTICSEARCH_URIS=http://elasticsearch:9200
    ports:
      - 127.0.0.1:8080:8080
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/management/health
      interval: 5s
      timeout: 5s
      retries: 40
    depends_on:
      mongodb:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
  mongodb:
    extends:
      file: ./mongodb.yml
      service: mongodb
  elasticsearch:
    extends:
      file: ./elasticsearch.yml
      service: elasticsearch
