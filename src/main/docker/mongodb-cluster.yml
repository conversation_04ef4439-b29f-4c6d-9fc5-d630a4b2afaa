# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: forconversations
services:
  mongodb:
    image: mongo:8.0.9
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:27017:27017
    command: mongos --configdb csvr/forconversations-mongodb-config --bind_ip 0.0.0.0
  mongodb-node:
    build:
      context: .
      dockerfile: mongodb/MongoDB.Dockerfile
    command: mongod --shardsvr --replSet rs1
  mongodb-config:
    image: mongo:8.0.9
    container_name: forconversations-mongodb-config
    command: mongod --configsvr --dbpath /data/db --replSet csvr
