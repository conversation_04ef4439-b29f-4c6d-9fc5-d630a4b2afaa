<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>

<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <logger name="com.wishforthecure.forconversations" level="INFO"/>

    <logger name="tech.jhipster" level="WARN"/>

    <!-- https://www.testcontainers.org/supported_docker_environment/logging_config/ -->
    <logger name="org.testcontainers" level="INFO"/>
    <logger name="com.github.dockerjava" level="WARN"/>

    <logger name="jakarta.activation" level="WARN"/>
    <logger name="jakarta.mail" level="WARN"/>
    <logger name="jakarta.xml.bind" level="WARN"/>
    <logger name="ch.qos.logback" level="WARN"/>
    <logger name="com.jayway.jsonpath" level="WARN"/>
    <logger name="com.ryantenney" level="WARN"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="io.netty" level="WARN"/>
    <logger name="io.searchbox" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.apache.catalina.startup.DigesterFactory" level="OFF"/>
    <logger name="org.bson" level="WARN"/>
    <logger name="org.elasticsearch" level="WARN"/>
    <logger name="org.hibernate.validator" level="WARN"/>
    <logger name="org.mongodb.driver" level="WARN"/>
    <logger name="org.reflections" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>
    <logger name="org.springframework.security" level="WARN"/>
    <logger name="org.springframework.cache" level="WARN"/>
    <logger name="org.synchronoss" level="WARN"/>
    <logger name="org.thymeleaf" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="reactor" level="WARN"/>
    <logger name="io.swagger.v3" level="INFO"/>
    <logger name="sun.rmi" level="WARN"/>
    <logger name="sun.rmi.transport" level="WARN"/>
    <logger name="com.tngtech.archunit.core.importer" level="ERROR"/>
    <!-- jhipster-needle-logback-add-log - JHipster will add a new log with level -->

</configuration>
