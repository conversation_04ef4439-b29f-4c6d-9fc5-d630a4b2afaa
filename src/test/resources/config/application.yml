# ===================================================================
# Spring Boot configuration.
#
# This configuration is used for unit/integration tests.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

mongock:
  migration-scan-package:
    - com.wishforthecure.forconversations.config.dbmigrations
  mongo-db:
    read-concern: local
    write-concern:
      journal: false
      w: 'majority'

spring:
  application:
    name: forconversations
  jackson:
    serialization:
      write-durations-as-timestamps: false
  mail:
    host: localhost
    port: 0 # Use port 0 to prevent connection attempts
    properties:
      mail.smtp.connectiontimeout: 1000
      mail.smtp.timeout: 1000
      mail.smtp.writetimeout: 1000
    test-connection: false
  main:
    allow-bean-definition-overriding: true
    web-application-type: reactive
  config:
    import: classpath:config/application-test.yml
  messages:
    basename: i18n/messages
  security:
    oauth2:
      resourceserver:
        jwt:
          authority-prefix: ''
          authorities-claim-name: auth
  task:
    execution:
      thread-name-prefix: forconversations-task-
      pool:
        core-size: 1
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: forconversations-scheduling-
      pool:
        size: 20
  thymeleaf:
    mode: HTML

server:
  port: 10344
  address: localhost

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================
jhipster:
  clientApp:
    name: 'forconversationsApp'
  mail:
    from: <EMAIL>
    base-url: http://127.0.0.1:8080
  logging:
    # To test json console appender
    use-json-format: false
    logstash:
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 (you can type `echo 'secret-key'|base64` on your command line)
        base64-secret: YTU1ZWI0NDBiN2Y1MDA5Y2EzNWRmZjI2MGY4OTRiMzg1ZDhiMmQ2MTIzMzA4MzRkMGYyYjQzYjA0MzFlYThjNDlmNDkxYWEwYjY0NTE0NDEyMDRiODhmYzZmMGM2YjI2ZDMwNjM3NDA4MjM0ZjZhMDgwNWE5ZGE0NmNkOGUzMTY=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 86400

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
management:
  health:
    mail:
      enabled: false
