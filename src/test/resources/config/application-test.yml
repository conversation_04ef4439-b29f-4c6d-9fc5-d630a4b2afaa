spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration
      - org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ReactiveElasticsearchRestClientAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ReactiveElasticsearchRepositoriesAutoConfiguration
  data:
    mongodb:
      uri: mongodb://localhost:27017/forconversations?waitQueueMultiple=1000
  mail:
    host: localhost
    port: 0 # Use port 0 to prevent connection attempts
    properties:
      mail.smtp.connectiontimeout: 1000
      mail.smtp.timeout: 1000
      mail.smtp.writetimeout: 1000
    test-connection: false

# Disable mail sending in tests
logging:
  level:
    com.wishforthecure.forconversations.service.MailService: WARN
