package com.wishforthecure.forconversations.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.wishforthecure.forconversations.ForconversationsApp;
import com.wishforthecure.forconversations.domain.enumeration.AliasType;
import com.wishforthecure.forconversations.service.IMailService;
import com.wishforthecure.forconversations.service.MessageService;
import com.wishforthecure.forconversations.service.SourceService;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import com.wishforthecure.forconversations.service.dto.AudioMessageSourceResponseDto;
import com.wishforthecure.forconversations.service.dto.MessageDTO;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import com.wishforthecure.forconversations.service.infra.GoogleSpeechAdapter;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.web.server.ResponseStatusException;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@SpringBootTest(classes = ForconversationsApp.class)
@Testcontainers
@SpringJUnitConfig
@Import(AudioMessageSourceServiceImplIT.IntegrationTestConfig.class)
@Disabled
class AudioMessageSourceServiceImplIT {

    @Container
    @ServiceConnection
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:8.0");

    @TestConfiguration
    static class IntegrationTestConfig {

        @Bean
        public MessageService messageService() {
            return mock(MessageService.class);
        }

        @Bean
        public SourceService sourceService() {
            return mock(SourceService.class);
        }

        @Bean
        public GoogleSpeechAdapter googleSpeechAdapter() {
            return mock(GoogleSpeechAdapter.class);
        }

        @Bean
        public IMailService mailService() {
            return mock(IMailService.class);
        }
    }

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("application.google.speech.enabled", () -> "true");
        // Especificar el nombre de la base de datos para evitar que use "test"
        registry.add("spring.data.mongodb.database", () -> "forconversations");
    }

    @Autowired
    private AudioMessageSourceServiceImpl audioMessageSourceService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private SourceService sourceService;

    @Autowired
    private GoogleSpeechAdapter googleSpeechAdapter;

    @Captor
    private ArgumentCaptor<MessageDTO> messageCaptor;

    @Captor
    private ArgumentCaptor<SourceDTO> sourceCaptor;

    private byte[] validAudioBytes;

    @BeforeEach
    void setUp() {
        validAudioBytes = "RIFF....WAVEfmt ".getBytes(StandardCharsets.UTF_8);
        reset(messageService, sourceService, googleSpeechAdapter);
    }

    @Test
    @DisplayName("loadFile: given valid audio, should return DTO with test transcription")
    void givenValidAudio_whenLoadFile_thenReturnsDtoWithTestTranscription() {
        // Act
        var result = audioMessageSourceService.loadFile(validAudioBytes);

        // Assert
        StepVerifier.create(result)
            .assertNext(response -> {
                assertThat(response.getText()).isEqualTo("Es una texto de prueba");
                assertThat(response.getFile()).isEqualTo(validAudioBytes);
                // Corrected assertion to match the actual detected MIME type
                assertThat(response.getFileContentType()).isEqualTo("audio/vnd.wave");
            })
            .verifyComplete();
    }

    @Test
    @DisplayName("loadFile: given empty audio content, should fail with BAD_REQUEST")
    void givenEmptyAudio_whenLoadFile_thenFails() {
        // Act
        var result = audioMessageSourceService.loadFile(new byte[0]);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(
                e -> e instanceof ResponseStatusException && ((ResponseStatusException) e).getStatusCode() == HttpStatus.BAD_REQUEST
            )
            .verify();
    }

    @Test
    @DisplayName("loadFile: given unsupported format, should fail with UNSUPPORTED_MEDIA_TYPE")
    void givenUnsupportedFormat_whenLoadFile_thenFails() {
        // Arrange
        byte[] textFileBytes = "this is a plain text file".getBytes();

        // Act
        var result = audioMessageSourceService.loadFile(textFileBytes);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(
                e ->
                    e instanceof ResponseStatusException &&
                    ((ResponseStatusException) e).getStatusCode() == HttpStatus.UNSUPPORTED_MEDIA_TYPE
            )
            .verify();
    }

    @Test
    @DisplayName("save: given valid DTO, should save message and source")
    void givenValidDto_whenSave_thenSavesMessageAndSource() {
        // Arrange
        var savedMessage = new MessageDTO(Instant.now(), "s", "r", "t", null);
        savedMessage.setId(UUID.randomUUID().toString());
        when(messageService.save(any(MessageDTO.class))).thenReturn(Mono.just(savedMessage));
        when(sourceService.save(any(SourceDTO.class))).thenReturn(Mono.just(new SourceDTO()));

        var requestDto = createValidSaveRequest();

        // Act
        var result = audioMessageSourceService.save(requestDto);

        // Assert
        StepVerifier.create(result).verifyComplete();
        verify(messageService).save(messageCaptor.capture());
        assertThat(messageCaptor.getValue().getSender()).isEqualTo("sender-alias");
        assertThat(messageCaptor.getValue().getRecipient()).isEqualTo("receiver-alias");

        verify(sourceService).save(sourceCaptor.capture());
        assertThat(sourceCaptor.getValue().getFile()).isEqualTo(validAudioBytes);
        assertThat(sourceCaptor.getValue().getMessagesIds()).containsExactly(savedMessage.getId());
    }

    @Test
    @DisplayName("save: given a MessageService error, should fail and not save source")
    void givenMessageServiceFails_whenSave_thenFailsAndAborts() {
        // Arrange
        when(messageService.save(any(MessageDTO.class))).thenReturn(Mono.error(new RuntimeException("Database connection failed")));
        var requestDto = createValidSaveRequest();

        // Act
        var result = audioMessageSourceService.save(requestDto);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(e -> e instanceof RuntimeException && e.getMessage().contains("Database connection failed"))
            .verify();

        verify(sourceService, never()).save(any());
    }

    @Test
    @DisplayName("save: given blank sender alias, should fail with BAD_REQUEST")
    void givenBlankSender_whenSave_thenFails() {
        // Arrange
        var requestDto = createValidSaveRequest();
        requestDto.getAliasSender().setValue("  ");

        // Act
        var result = audioMessageSourceService.save(requestDto);

        // Assert
        StepVerifier.create(result)
            .expectErrorMatches(
                e ->
                    e instanceof ResponseStatusException &&
                    ((ResponseStatusException) e).getStatusCode() == HttpStatus.BAD_REQUEST &&
                    ((ResponseStatusException) e).getReason().contains("Sender alias cannot be null or empty")
            )
            .verify();
    }

    // --- Helper Methods ---

    private AudioMessageSourceResponseDto createValidSaveRequest() {
        var requestDto = new AudioMessageSourceResponseDto();
        requestDto.setAliasSender(new AliasDTO("sender-alias", AliasType.AUDIO));
        requestDto.setAliasReceiver(new AliasDTO("receiver-alias", AliasType.AUDIO));
        requestDto.setText("This is the transcribed text.");
        requestDto.setTime(Instant.now());
        requestDto.setFile(validAudioBytes);
        requestDto.setFileContentType("audio/wav"); // Using a known valid type for the DTO
        return requestDto;
    }
}
