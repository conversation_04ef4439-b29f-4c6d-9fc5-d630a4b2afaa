package com.wishforthecure.forconversations.service.infra;

import static org.assertj.core.api.Assertions.assertThat;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.service.dto.EmailMessageDTO;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

class MBoxAdapterTest {

    private final MBoxAdapter mboxAdapter = new MBoxAdapter();

    @Test
    void testParseMboxWithRealFile() throws IOException {
        // Given: A real MBOX file from test resources
        ClassPathResource resource = new ClassPathResource("source-examples/mbox_example_01");
        byte[] mboxBytes = resource.getInputStream().readAllBytes();

        // When: Parsing the real MBOX content
        List<EmailMessageDTO> result = mboxAdapter.parse(mboxBytes);

        // Then: Should parse messages correctly
        assertThat(result).isNotEmpty();

        // Verify that all messages have the required fields
        for (EmailMessageDTO message : result) {
            assertThat(message.getSender()).isNotNull();
            assertThat(message.getContent()).isNotNull();
            assertThat(message.getType()).isEqualTo(SourceType.EMAIL);
            assertThat(message.getTime()).isNotNull();
        }

        // Log some information about the parsed messages for verification
        System.out.println("Parsed " + result.size() + " messages from real MBOX file");
        if (!result.isEmpty()) {
            EmailMessageDTO firstMessage = result.get(0);
            System.out.println("First message sender: " + firstMessage.getSender());
            System.out.println(
                "First message subject: " +
                (firstMessage.getContent().contains("##_##") ? firstMessage.getContent().split("##_##")[0] : "No subject")
            );
        }
    }

    @Test
    void testParseMboxWithRealFileDetails() throws IOException {
        // Given: A real MBOX file from test resources
        ClassPathResource resource = new ClassPathResource("source-examples/mbox_example_01");
        byte[] mboxBytes = resource.getInputStream().readAllBytes();

        // When: Parsing the real MBOX content
        List<EmailMessageDTO> result = mboxAdapter.parse(mboxBytes);

        // Then: Should parse multiple messages correctly
        assertThat(result).hasSizeGreaterThan(1);

        // Verify specific details from the real file
        boolean foundSlackMessage = false;
        boolean foundAtlassianMessage = false;

        for (EmailMessageDTO message : result) {
            String content = message.getContent();
            String sender = message.getSender();

            if (sender != null && sender.contains("slack.com")) {
                foundSlackMessage = true;
                // Check for any Slack-related content
                assertThat(content.toLowerCase()).containsAnyOf("slack", "base100", "workspace");
            }

            if (sender != null && sender.contains("atlassian.net")) {
                foundAtlassianMessage = true;
                assertThat(content.toLowerCase()).containsAnyOf("jira", "atlassian", "invite");
            }
        }

        // Verify we found expected messages from the real file
        assertThat(foundSlackMessage || foundAtlassianMessage).isTrue();

        System.out.println("Found Slack message: " + foundSlackMessage);
        System.out.println("Found Atlassian message: " + foundAtlassianMessage);
    }

    @Test
    void testParseMboxWithEmptyContent() {
        // Given: Empty MBOX content
        byte[] mboxBytes = new byte[0];

        // When: Parsing the empty MBOX content
        List<EmailMessageDTO> result = mboxAdapter.parse(mboxBytes);

        // Then: Should return empty list
        assertThat(result).isEmpty();
    }

    @Test
    void testParseMboxWithInvalidContent() {
        // Given: Invalid MBOX content
        String invalidContent = "This is not a valid MBOX format";
        byte[] mboxBytes = invalidContent.getBytes(StandardCharsets.UTF_8);

        // When: Parsing the invalid MBOX content
        List<EmailMessageDTO> result = mboxAdapter.parse(mboxBytes);

        // Then: Should handle gracefully and return empty list or skip invalid messages
        assertThat(result).isNotNull();
        // The exact behavior depends on how the parser handles invalid content
        // It should not throw exceptions
    }
}
