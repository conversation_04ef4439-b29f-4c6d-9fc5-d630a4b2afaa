package com.wishforthecure.forconversations.web.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.wishforthecure.forconversations.ForconversationsApp;
import com.wishforthecure.forconversations.domain.enumeration.AliasType;
import com.wishforthecure.forconversations.service.AudioMessageSourceService;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import com.wishforthecure.forconversations.service.dto.AudioMessageSourceResponseDto;
import java.io.IOException;
import java.time.Instant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.server.ResponseStatusException;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import reactor.core.publisher.Mono;

@SpringBootTest(classes = ForconversationsApp.class)
@Import(AudioMessageSourceResourceIT.TestConfig.class)
@Testcontainers
@Disabled
class AudioMessageSourceResourceIT {

    private static final String API_URL = "/api/audio-message-sources";
    private static final String LOAD_DATA_URL = API_URL + "/load-data";

    @Container
    @ServiceConnection
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:8.0");

    // Mock the service layer for this controller test
    @MockBean
    private AudioMessageSourceService audioMessageSourceService;

    @Autowired
    private ApplicationContext applicationContext;

    private WebTestClient webTestClient;

    @BeforeEach
    void setUp() {
        this.webTestClient = WebTestClient.bindToApplicationContext(applicationContext).build();
    }

    @Test
    @DisplayName("POST /load-data: given valid file, should return 200 OK with DTO")
    void givenValidFile_whenLoadFile_thenReturns200() throws IOException {
        // Arrange
        var responseDto = new AudioMessageSourceResponseDto();
        responseDto.setText("Test transcription");
        responseDto.setTime(Instant.now());
        when(audioMessageSourceService.loadFile(any(byte[].class))).thenReturn(Mono.just(responseDto));

        var multipartBodyBuilder = new MultipartBodyBuilder();
        multipartBodyBuilder.part("file", new ClassPathResource("source-examples/valid_one_user.txt")).contentType(MediaType.TEXT_PLAIN);

        // Act & Assert
        webTestClient
            .post()
            .uri(LOAD_DATA_URL)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(BodyInserters.fromMultipartData(multipartBodyBuilder.build()))
            .exchange()
            .expectStatus()
            .isOk()
            .expectBody(AudioMessageSourceResponseDto.class)
            .value(dto -> {
                assertThat(dto.getText()).isEqualTo("Test transcription");
            });
    }

    @Test
    @DisplayName("POST /load-data: given service error, should return corresponding status")
    void givenServiceError_whenLoadFile_thenReturnsErrorStatus() throws IOException {
        // Arrange
        var error = new ResponseStatusException(HttpStatus.UNSUPPORTED_MEDIA_TYPE, "Unsupported format");
        when(audioMessageSourceService.loadFile(any(byte[].class))).thenReturn(Mono.error(error));

        var multipartBodyBuilder = new MultipartBodyBuilder();
        multipartBodyBuilder.part("file", new byte[10]).filename("test.wav"); // Dummy content

        // Act & Assert
        webTestClient
            .post()
            .uri(LOAD_DATA_URL)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(BodyInserters.fromMultipartData(multipartBodyBuilder.build()))
            .exchange()
            .expectStatus()
            .isEqualTo(HttpStatus.UNSUPPORTED_MEDIA_TYPE);
    }

    @Test
    @DisplayName("POST /: given valid DTO, should return 201 CREATED")
    void givenValidDto_whenSave_thenReturns201() {
        // Arrange
        when(audioMessageSourceService.save(any(AudioMessageSourceResponseDto.class))).thenReturn(Mono.empty());
        var requestDto = createValidSaveRequestDto();

        // Act & Assert
        webTestClient
            .post()
            .uri(API_URL)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(requestDto)
            .exchange()
            .expectStatus()
            .isCreated();
    }

    @Test
    @DisplayName("POST /: given invalid DTO, should return 400 BAD_REQUEST from service")
    void givenInvalidDto_whenSave_thenReturns400() {
        // Arrange
        var error = new ResponseStatusException(HttpStatus.BAD_REQUEST, "Sender alias cannot be null or empty.");
        when(audioMessageSourceService.save(any(AudioMessageSourceResponseDto.class))).thenReturn(Mono.error(error));
        var requestDto = createValidSaveRequestDto();
        requestDto.getAliasSender().setValue(""); // Invalid alias

        // Act & Assert
        webTestClient
            .post()
            .uri(API_URL)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(requestDto)
            .exchange()
            .expectStatus()
            .isBadRequest();
    }

    // --- Helper Methods ---

    private AudioMessageSourceResponseDto createValidSaveRequestDto() {
        var requestDto = new AudioMessageSourceResponseDto();
        requestDto.setAliasSender(new AliasDTO("sender-alias", AliasType.AUDIO));
        requestDto.setAliasReceiver(new AliasDTO("receiver-alias", AliasType.AUDIO));
        requestDto.setText("This is the transcribed text.");
        requestDto.setTime(Instant.now());
        // File content is not needed for the save DTO in this test
        return requestDto;
    }

    @TestConfiguration
    static class TestConfig {

        @Bean
        SecurityWebFilterChain testSecurityWebFilterChain(ServerHttpSecurity http) {
            return http
                .authorizeExchange(exchanges -> exchanges.anyExchange().permitAll())
                .csrf(csrf -> csrf.disable())
                .httpBasic(basic -> basic.disable())
                .formLogin(form -> form.disable())
                .build();
        }
    }
}
