package com.wishforthecure.forconversations.web.rest;

import com.wishforthecure.forconversations.ForconversationsApp;
import com.wishforthecure.forconversations.service.EmailMessageSourceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.testcontainers.service.connection.ServiceConnection;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.reactive.function.BodyInserters;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

@SpringBootTest(classes = ForconversationsApp.class)
@Import(EmailMessageSourceResourceIT.TestConfig.class)
@Testcontainers
@Disabled
class EmailMessageSourceResourceIT {

    private static final String API_URL = "/api/email-message-sources";
    private static final String LOAD_DATA_URL = API_URL + "/load-data";

    @Container
    @ServiceConnection
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:8.0");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Especificar el nombre de la base de datos para evitar que use "test"
        registry.add("spring.data.mongodb.database", () -> "forconversations");
    }

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private EmailMessageSourceService emailMessageSourceService;

    private WebTestClient webTestClient;

    @BeforeEach
    void setUp() {
        this.webTestClient = WebTestClient.bindToApplicationContext(applicationContext).build();
    }

    @Test
    void testLoadMboxFile() {
        // Given: A real MBOX file from test resources
        ClassPathResource resource = new ClassPathResource("source-examples/mbox_example_01");

        // When: Uploading the MBOX file
        webTestClient
            .post()
            .uri(LOAD_DATA_URL)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(BodyInserters.fromMultipartData("file", resource))
            .exchange()
            // Then: Should return 200 OK
            .expectStatus()
            .isOk();
    }

    @Test
    void testLoadEmptyFile() {
        // Given: An empty file
        byte[] emptyContent = new byte[0];

        // When: Uploading the empty file
        webTestClient
            .post()
            .uri(LOAD_DATA_URL)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(BodyInserters.fromMultipartData("file", emptyContent).with("filename", "empty.mbox"))
            .exchange()
            // Then: Should return 200 OK (gracefully handle empty files)
            .expectStatus()
            .isOk();
    }

    @Test
    void testLoadInvalidMboxFile() {
        // Given: Invalid MBOX content
        String invalidContent = "This is not a valid MBOX format\nJust some random text";
        byte[] invalidBytes = invalidContent.getBytes();

        // When: Uploading the invalid file
        webTestClient
            .post()
            .uri(LOAD_DATA_URL)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .body(BodyInserters.fromMultipartData("file", invalidBytes).with("filename", "invalid.mbox"))
            .exchange()
            // Then: Should return 200 OK (gracefully handle invalid format)
            .expectStatus()
            .isOk();
    }

    @Test
    void testLoadDataWithoutFile() {
        // When: Making request without file parameter
        webTestClient
            .post()
            .uri(LOAD_DATA_URL)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .exchange()
            // Then: Should return 500 Internal Server Error (multipart boundary missing)
            .expectStatus()
            .is5xxServerError();
    }

    @TestConfiguration
    static class TestConfig {

        @Bean
        SecurityWebFilterChain testSecurityWebFilterChain(ServerHttpSecurity http) {
            return http
                .authorizeExchange(exchanges -> exchanges.anyExchange().permitAll())
                .csrf(csrf -> csrf.disable())
                .httpBasic(basic -> basic.disable())
                .formLogin(form -> form.disable())
                .build();
        }
    }
}
