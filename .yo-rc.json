{"generator-jhipster": {"applicationType": "monolith", "authenticationType": "jwt", "baseName": "forconversations", "buildTool": "maven", "cacheProvider": null, "clientFramework": "angular", "clientTestFrameworks": [], "clientTheme": "journal", "clientThemeVariant": "primary", "creationTimestamp": 1749411112001, "databaseType": "mongodb", "devDatabaseType": null, "enableHibernateCache": null, "enableTranslation": true, "entities": ["Message", "Source", "WhatsappMessage", "WhatsappMessageSource", "AudioMessage", "AudioMessageSource", "EmailMessage", "EmailMessageSource", "<PERSON><PERSON>", "Participant"], "feignClient": null, "jhipsterVersion": "8.11.0", "jwtSecretKey": "YTU1ZWI0NDBiN2Y1MDA5Y2EzNWRmZjI2MGY4OTRiMzg1ZDhiMmQ2MTIzMzA4MzRkMGYyYjQzYjA0MzFlYThjNDlmNDkxYWEwYjY0NTE0NDEyMDRiODhmYzZmMGM2YjI2ZDMwNjM3NDA4MjM0ZjZhMDgwNWE5ZGE0NmNkOGUzMTY=", "languages": ["en", "es"], "lastLiquibaseTimestamp": 1749411310000, "microfrontend": null, "microfrontends": [], "nativeLanguage": "en", "packageName": "com.wishforthecure.forconversations", "prodDatabaseType": null, "reactive": true, "searchEngine": "elasticsearch", "serverPort": null, "serviceDiscoveryType": null, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": true}}