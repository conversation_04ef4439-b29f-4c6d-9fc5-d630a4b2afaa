# Node modules and npm files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build directories (except target with JAR)
target/angular/
target/classes/
target/generated-sources/
target/generated-test-sources/
target/maven-archiver/
target/maven-status/
target/node/
target/spotless-index/
target/test-classes/
target/*.original

# Source code (not needed in production image)
src/
webpack/

# Development files
.git/
.gitignore
README.md
*.md

# IDE files
.vscode/
.idea/
*.iml

# OS files
.DS_Store
Thumbs.db

# Logs
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Package files
package*.json
angular.json
tsconfig*.json
jest.conf.js
eslint.config.mjs

# Maven wrapper (optional, keep if you want to use it in container)
mvnw
mvnw.cmd
.mvn/

# Docker files
Dockerfile
.dockerignore
docker-compose*.yml

# Other build tools
sonar-project.properties
checkstyle.xml
