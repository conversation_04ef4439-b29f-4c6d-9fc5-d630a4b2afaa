# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [1.1.0] - 2024-12-19

### Added

- **DDD Message Architecture**: Implemented Domain-Driven Design for message system
  - `BaseMessage` abstract class as foundation for all message types
  - `WhatsAppMessage` aggregate root with WhatsApp-specific business logic
  - `AudioMessage` aggregate root with audio transcription and quality management
  - `EmailMessage` aggregate root with email-specific features (attachments, replies, HTML support)
  - `MessageFactory` for centralized message creation
  - `MessageId` value object for type-safe message identification
- **jMolecules Integration**: Added jMolecules library for DDD pattern enforcement
  - `@AggregateRoot` annotations for domain aggregates
  - `@Entity` and `@ValueObject` annotations for domain modeling
  - `@Repository` and `@Service` annotations for architectural clarity
  - Domain event support with `@DomainEvent`
- **Email Message Features**:
  - Email validation with regex patterns
  - HTML content support
  - File attachment management
  - Reply functionality with subject prefixing
  - CC/BCC recipient support
- **WhatsApp Message Features**:
  - Message forwarding capabilities
  - Group message support
  - Media attachment handling
  - Contact information management

### Changed

- **Message Class**: Marked as `@Deprecated` for backward compatibility
  - Legacy `create()` methods maintained but deprecated
  - Added conversion methods to/from new aggregate roots
- **Architecture**: Migrated from anemic domain model to rich domain model
  - Business logic moved from services to domain entities
  - Improved encapsulation and type safety
  - Better separation of concerns following DDD principles

### Fixed

- **Compilation Issues**: Resolved all compilation errors from initial DDD implementation
  - Fixed `JMoleculesAggregateRoot` import conflicts in `BaseMessage`
  - Removed invalid `@Factory` annotations from static methods
  - Corrected `MessageContent.fromText()` to `MessageContent.of()` usage
  - Added missing `java.util.regex.Pattern` import in `EmailMessage`
- **Code Quality**: Improved code organization and formatting
  - Cleaned up unused imports
  - Standardized method parameter formatting
  - Enhanced JavaDoc documentation

### Technical Details

- **Files Modified**:
  - `src/main/java/com/wishforthecure/forconversations/hexagonal/domain/model/message/BaseMessage.java`
  - `src/main/java/com/wishforthecure/forconversations/hexagonal/domain/model/message/AudioMessage.java`
  - `src/main/java/com/wishforthecure/forconversations/hexagonal/domain/model/message/EmailMessage.java`
  - `src/main/java/com/wishforthecure/forconversations/hexagonal/domain/model/message/WhatsAppMessage.java`
  - `src/main/java/com/wishforthecure/forconversations/hexagonal/domain/model/message/MessageFactory.java`
  - `src/main/java/com/wishforthecure/forconversations/hexagonal/domain/model/message/Message.java` (deprecated)

### Documentation

- Added comprehensive DDD architecture documentation
- Created migration guide for legacy code transition
- Documented compilation fixes and technical decisions
- Organized documentation in `docs/refactoring/2024-12-19-ddd-message-refactoring/`

### Migration Notes

- Legacy `Message` class remains functional for backward compatibility
- New code should use specific aggregate roots (`AudioMessage`, `EmailMessage`, `WhatsAppMessage`)
- Use `MessageFactory` for polymorphic message creation
- See `docs/refactoring/2024-12-19-ddd-message-refactoring/migration-guide.md` for detailed migration steps

---

## [1.0.0] - 2024-12-01

### Added

- Initial project setup with JHipster 8.11.0
- Basic message management functionality
- WhatsApp file upload capabilities
- Audio message processing
- Email message handling
- MongoDB integration
- Elasticsearch integration
- Basic REST API endpoints

### Infrastructure

- Docker configuration
- Maven build system
- Jest testing framework
- ESLint configuration
- Angular frontend setup
