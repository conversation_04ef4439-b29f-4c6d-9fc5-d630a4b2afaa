sonar.projectKey = forconversations
sonar.projectName = forconversations generated by j<PERSON>ter

# Typescript tests files must be inside sources and tests, otherwise `INFO: Test execution data ignored for 80 unknown files, including:` 
# is shown.
sonar.sources = src
sonar.tests = src
sonar.host.url = http://localhost:9001

sonar.test.inclusions = src/test/**/*.*, src/main/webapp/app/**/*.spec.ts
sonar.coverage.jacoco.xmlReportPaths = target/site/**/jacoco*.xml
sonar.java.codeCoveragePlugin = jacoco
sonar.junit.reportPaths = target/surefire-reports,target/failsafe-reports
sonar.testExecutionReportPaths = target/test-results/jest/TESTS-results-sonar.xml
sonar.javascript.lcov.reportPaths = target/test-results/lcov.info

sonar.sourceEncoding = UTF-8
sonar.exclusions = src/main/webapp/content/**/*.*, src/main/webapp/i18n/*.js, target/classes/static/**/*.*

sonar.issue.ignore.multicriteria = S6437,S125,S3437,S4502,S4684,S5145,S7027-domain,S7027-dto,UndocumentedApi

# Rule https://rules.sonarsource.com/java/RSPEC-6437 is ignored, hardcoded passwords are provided for development purposes
sonar.issue.ignore.multicriteria.S6437.resourceKey = src/main/resources/config/*
sonar.issue.ignore.multicriteria.S6437.ruleKey = java:S6437
# Rule https://rules.sonarsource.com/java/RSPEC-3437 is ignored, as a JPA-managed field cannot be transient
sonar.issue.ignore.multicriteria.S3437.resourceKey = src/main/java/**/*
sonar.issue.ignore.multicriteria.S3437.ruleKey = squid:S3437
# Rule https://rules.sonarsource.com/java/RSPEC-4502 is ignored, as for JWT tokens we are not subject to CSRF attack
sonar.issue.ignore.multicriteria.S4502.resourceKey = src/main/java/**/*
sonar.issue.ignore.multicriteria.S4502.ruleKey = java:S4502
# Rule https://rules.sonarsource.com/java/RSPEC-4684
sonar.issue.ignore.multicriteria.S4684.resourceKey = src/main/java/**/*
sonar.issue.ignore.multicriteria.S4684.ruleKey = java:S4684
# Rule https://rules.sonarsource.com/java/RSPEC-5145 log filter is applied
sonar.issue.ignore.multicriteria.S5145.resourceKey = src/main/java/**/*
sonar.issue.ignore.multicriteria.S5145.ruleKey = javasecurity:S5145
# Rule https://rules.sonarsource.com/java/RSPEC-7027 is ignored for entities
sonar.issue.ignore.multicriteria.S7027-domain.resourceKey = src/main/java/com/wishforthecure/forconversations/domain/**/*
sonar.issue.ignore.multicriteria.S7027-domain.ruleKey = javaarchitecture:S7027
# Rule https://rules.sonarsource.com/java/RSPEC-7027 is ignored for dtos
sonar.issue.ignore.multicriteria.S7027-dto.resourceKey = src/main/java/com/wishforthecure/forconversations/service/dto/**/*
sonar.issue.ignore.multicriteria.S7027-dto.ruleKey = javaarchitecture:S7027
# Rule https://rules.sonarsource.com/java/RSPEC-1176 is ignored, as we want to follow "clean code" guidelines and classes, methods and 
# arguments names should be self-explanatory
sonar.issue.ignore.multicriteria.UndocumentedApi.resourceKey = src/main/java/**/*
sonar.issue.ignore.multicriteria.UndocumentedApi.ruleKey = squid:UndocumentedApi
# Rule https://rules.sonarsource.com/xml/RSPEC-125
sonar.issue.ignore.multicriteria.S125.resourceKey = src/main/resources/logback-spring.xml
sonar.issue.ignore.multicriteria.S125.ruleKey = xml:S125
