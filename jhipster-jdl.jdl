
enum SourceType {
    WHATSAPP
    AUDIO
    EMAIL
}
// Entity Message
entity Message {
    time Instant required
    sender String required
    recipients String required
    content String required
    type SourceType required
}
// Entity Source
entity Source {
    time Instant required
    messages TextBlob required
    type SourceType required
    sourceId String required
}
// Entity WhatsappMessage
@SkipClient
entity WhatsappMessage {
    time Instant required
    sender String required
    recipients String required
    content String required
    type SourceType required
}
// Entity WhatsappMessageSource
entity WhatsappMessageSource {
    time Instant required
    file AnyBlob required
}
// Entity AudioMessage
@SkipClient
entity AudioMessage {
    time Instant required
    sender String required
    recipients String required
    content String required
    type SourceType required
}
// Entity AudioMessageSource
entity AudioMessageSource {
    time Instant required
    file AnyBlob required
}
// Entity EmailMessage
@SkipClient
entity EmailMessage {
    time Instant required
    sender String required
    recipients String required
    content String required
    type SourceType required
}
entity EmailMessageSource {
    time Instant required
    file AnyBlob required
}
enum AliasType {
    WHATSAPP
    EMAIL
}
entity Alias {
    value String
	type AliasType
}
entity Participant {
    name String required
	surname String required
    secondSurname String
    emailContact String
    mobileContact String
}
relationship OneToMany {
    WhatsappMessageSource{messages} to WhatsappMessage , AudioMessageSource{messages} to AudioMessage , EmailMessageSource{messages} to EmailMessage
}
dto * with mapstruct
service * with serviceClass
service * with serviceImpl
search * with elasticsearch
// 1. Despliegue para Desarrollo con Docker-Compose
deployment {
  deploymentType docker-compose
  appsFolders [dockercompose]
  dockerRepositoryName "wishforthecure.forconversations"
}
