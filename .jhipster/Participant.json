{"annotations": {"changelogDate": "20250608193510"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "surname", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "second<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "emailContact", "fieldType": "String"}, {"fieldName": "mobileContact", "fieldType": "String"}], "name": "Participant", "relationships": [], "searchEngine": "elasticsearch", "service": "serviceImpl"}