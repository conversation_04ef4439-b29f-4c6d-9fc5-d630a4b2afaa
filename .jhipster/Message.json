{"annotations": {"changelogDate": "20250608193501"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "time", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "sender", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "recipients", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "content", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "SourceType", "fieldValidateRules": ["required"], "fieldValues": "WHATSAPP,AUDIO,EMAIL"}], "name": "Message", "relationships": [], "searchEngine": "elasticsearch", "service": "serviceImpl"}