{"annotations": {"changelogDate": "20250608193506"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "time", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "file", "fieldType": "AnyBlob", "fieldValidateRules": ["required"]}], "name": "AudioMessageSource", "relationships": [{"otherEntityName": "audioMessage", "relationshipName": "messages", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "elasticsearch", "service": "serviceImpl"}