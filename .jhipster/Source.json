{"annotations": {"changelogDate": "20250608193502"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "time", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "messages", "fieldType": "TextBlob", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "SourceType", "fieldValidateRules": ["required"], "fieldValues": "WHATSAPP,AUDIO,EMAIL"}, {"fieldName": "sourceId", "fieldType": "String", "fieldValidateRules": ["required"]}], "name": "Source", "relationships": [], "searchEngine": "elasticsearch", "service": "serviceImpl"}