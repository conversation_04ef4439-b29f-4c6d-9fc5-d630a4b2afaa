{"annotations": {"changelogDate": "20250608193508"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "time", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "file", "fieldType": "AnyBlob", "fieldValidateRules": ["required"]}], "name": "EmailMessageSource", "relationships": [{"otherEntityName": "emailMessage", "relationshipName": "messages", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "elasticsearch", "service": "serviceImpl"}