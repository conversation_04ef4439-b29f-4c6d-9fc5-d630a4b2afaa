{"annotations": {"changelogDate": "20250608193507", "skipClient": true}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "time", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "sender", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "recipient", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "content", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "type", "fieldType": "SourceType", "fieldValidateRules": ["required"], "fieldValues": "WHATSAPP,AUDIO,EMAIL"}], "name": "EmailMessage", "relationships": [], "searchEngine": "elasticsearch", "service": "serviceImpl"}