{"annotations": {"changelogDate": "20250608193504"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "time", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"fieldName": "file", "fieldType": "AnyBlob", "fieldValidateRules": ["required"]}], "name": "WhatsappMessageSource", "relationships": [{"otherEntityName": "whatsappMessage", "relationshipName": "messages", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "elasticsearch", "service": "serviceImpl"}