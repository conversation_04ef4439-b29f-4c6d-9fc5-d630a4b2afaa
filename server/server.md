# Servidor de Producción - Guía de Despliegue

## 🚀 Comandos <PERSON>pid<PERSON> (Selecciona y Ctrl+C para copiar)

**Despliegue completo:**

```
./mvnw jib:build -Pprod
```

**Reiniciar deployment:**

```
kubectl rollout restart deployment/forconversations-deployment -n forconversations
```

**Ver estado:**

```
kubectl get pods -n forconversations
```

**Ver logs:**

```
kubectl logs -f deployment/forconversations-deployment -n forconversations
```

**Aplicar ConfigMap actualizado:**

```
kubectl apply -f server/k8s/app/app-configmap.yaml
```

**Reiniciar después de cambiar ConfigMap:**

```
kubectl rollout restart deployment/forconversations-deployment -n forconversations
```

**Aplicar deployment actualizado:**

```
kubectl apply -f server/k8s/app/app-deployment-service-ingress.yaml
```

## 1. Configuración Inicial

### Transferir archivos K8s

```bash
# Subir ficheros k8s al servidor
scp -r ./server/k8s/** root@**************:/root/k8s/

# Bajar ficheros k8s del servidor
scp -r root@**************:/root/k8s/** ./server/k8s/
```

### Configurar Docker Hub

```bash
# En el servidor, hacer login con Personal Access Token
docker login -u wishforthecure
# Cuando pida password, usar: ************************************
```

## 2. Despliegue con Jib (Recomendado)

### Construir y subir imagen automáticamente

**Comando para ejecutar:**

```bash
./mvnw jib:build -Pprod
```

**O copia y pega esto en tu terminal:**

```
./mvnw jib:build -Pprod
```

## 3. Tagear Nueva Versión

### Actualizar versión en pom.xml

```bash
# Cambiar la versión en pom.xml, por ejemplo:
# <version>2506191200</version>
```

### Construir y subir con nueva versión

```bash
# Jib creará automáticamente estos tags:
# - wishforthecure/forconversations:2506191200 (versión específica)
# - wishforthecure/forconversations:latest
# - wishforthecure/forconversations:20250619120000 (timestamp)
./mvnw jib:build -Pprod
```

### Gestión clúster K8s

- Aplicar deployemnt

```bash
kubectl apply -f /root/k8s/app/app-deployment-service-ingress.yaml
```

- Opción 1: Reiniciar deployment (usa latest automáticamente)

```bash
kubectl rollout restart deployment/forconversations-deploymet -n forconversations
```

```bash
kubectl scale deployment forconversations-deployment -n forconversations --replicas=0
```

```bash
kubectl scale deployment forconversations-deployment -n forconversations --replicas=1
```

- Opción 2: Actualizar imagen específica

```bash
kubectl set image deployment/forconversations-deployment forconversations-app=wishforthecure/forconversations:20250619183929 -n forconversations
```

- Opción 3: Editar el deployment manualmente

```bash
kubectl edit deployment forconversations-deployment -n forconversations
```

```bash
# Verificar el progreso del rollout
kubectl rollout status deployment/forconversations-deployment -n forconversations
```

### Verificar estado después del despliegue

- Ver pods en tiempo real

```bash
kubectl get pods -n forconversations -w
```

- Ver logs de la aplicación

```bash
kubectl logs -f deployment/forconversations-deployment -n forconversations
```

```bash
# Verificar health checks
kubectl describe pod $(kubectl get pods -n forconversations -l app=forconversations -o jsonpath='{.items[0].metadata.name}') -n forconversations
```

## 4. MongoDB - Comandos de Gestión

### Obtener contraseña de root

```bash
export MONGODB_ROOT_PASSWORD=$(kubectl get secret --namespace forconversations mongodb -o jsonpath="{.data.mongodb-root-password}" | base64 -d)
echo "Contraseña de root: $MONGODB_ROOT_PASSWORD"
```

### Conectar desde contenedor en el clúster

```bash
kubectl run --namespace forconversations mongodb-client --rm --tty -i --restart='Never' \
 --env="MONGODB_ROOT_PASSWORD=$MONGODB_ROOT_PASSWORD" \
  --image docker.io/bitnami/mongodb:8.0.10-debian-12-r2 --command -- bash -c \
  "mongosh admin --host 'mongodb' --authenticationDatabase admin -u root -p \"$MONGODB_ROOT_PASSWORD\""
```

### Túnel SSH para conexión local

```bash
# Port-forward
ssh -N -L 27017:127.0.0.1:27017 root@**************

# Conexión local
*******************************************************************************************
```

## 5. Base de Datos de Producción - Gestión Limpia

### Crear Base de Datos Limpia para Mongock

Cuando necesites una base de datos limpia para que se ejecuten correctamente los scripts de migración de Mongock:

#### Paso 1: Conectar a MongoDB

```bash
# Obtener el nombre del pod de MongoDB
kubectl get pods -n forconversations | grep mongodb

# Conectar al pod de MongoDB (reemplaza el nombre del pod)
kubectl exec -it mongodb-cb78b9c49-szzfm -n forconversations -- mongosh --username wishforthecure --password 'W3sf4rth2c5r2#' --authenticationDatabase admin
```

#### Paso 2: Limpiar Base de Datos

```javascript
// Dentro de mongosh
use;
forconversations;

// Ver colecciones existentes
show;
collections;

// Borrar todas las colecciones de datos (mantener índices si es necesario)
db.user.drop();
db.authority.drop();
db.message.drop();
db.participant.drop();
db.source.drop();
db.alias.drop();
db.whatsappMessageSource.drop();
db.emailMessageSource.drop();
db.audioMessageSource.drop();

// Borrar colecciones de Mongock para que se ejecuten las migraciones
db.mongockChangeLog.drop();
db.mongockLock.drop();

// Verificar que las colecciones se borraron
show;
collections;

// Salir de mongosh
exit;
```

#### Paso 3: Reiniciar Aplicación

```bash
# Reiniciar el deployment para que se ejecuten las migraciones
kubectl rollout restart deployment/forconversations-deployment -n forconversations

# Verificar logs para confirmar que Mongock ejecuta las migraciones
kubectl logs -f $(kubectl get pods -n forconversations -l app=forconversations -o jsonpath='{.items[0].metadata.name}') -n forconversations
```

#### Paso 4: Verificar Migración Exitosa

```bash
# Conectar nuevamente a MongoDB
kubectl exec -it mongodb-cb78b9c49-szzfm -n forconversations -- mongosh --username wishforthecure --password 'W3sf4rth2c5r2#' --authenticationDatabase admin
```

```javascript
// Verificar que se crearon las colecciones y datos iniciales
use;
forconversations;
show;
collections;

// Verificar usuario admin creado
db.user.find({ login: 'admin' });

// Verificar authorities creadas
db.authority.find();

// Verificar que Mongock registró las migraciones
db.mongockChangeLog.find();
```

### Datos Iniciales Creados por Mongock

El script `InitialSetupMigration.java` crea:

- **Authorities:** `ROLE_USER` y `ROLE_ADMIN`
- **Usuario Admin:**
  - Login: `admin`
  - Password: `admin` (hash: `$2a$10$9VhyuCYebOVy8QEf8X8riejFfddE476rnHyA8J3x8gS1W2BnTZY5m`)
  - Email: `<EMAIL>`
  - Roles: `ROLE_USER` y `ROLE_ADMIN`

### Troubleshooting Base de Datos

```bash
# Si la aplicación no arranca por problemas de BD
kubectl describe pod $(kubectl get pods -n forconversations -l app=forconversations -o jsonpath='{.items[0].metadata.name}') -n forconversations

# Ver logs específicos de conexión a MongoDB
kubectl logs $(kubectl get pods -n forconversations -l app=forconversations -o jsonpath='{.items[0].metadata.name}') -n forconversations | grep -i mongo

# Ver logs específicos de Mongock
kubectl logs $(kubectl get pods -n forconversations -l app=forconversations -o jsonpath='{.items[0].metadata.name}') -n forconversations | grep -i mongock
```

## 6. Kubectl - Comandos Útiles

### Gestión de Pods

```bash
# Ver pods en tiempo real
kubectl get pods -n forconversations -w

# Borrar pod (se recrea automáticamente)
kubectl delete pod -l app=forconversations -n forconversations

# Describir pod para debug
kubectl describe pod $(kubectl get pods -n forconversations -l app=forconversations -o jsonpath='{.items[0].metadata.name}') -n forconversations

# Ver logs en tiempo real
kubectl logs -f $(kubectl get pods -n forconversations -l app=forconversations -o jsonpath='{.items[0].metadata.name}') -n forconversations
```

### Gestión de Deployments

```bash
# Reiniciar deployment
kubectl rollout restart deployment/forconversations-deployment -n forconversations

# Escalar a 0 replicas (parar app)
kubectl scale deployment forconversations-deployment -n forconversations --replicas=0

# Escalar a 1 replica (arrancar app)
kubectl scale deployment forconversations-deployment -n forconversations --replicas=1
```

## 6. Gestión de Imágenes (containerd)

### Comandos de bajo nivel para imágenes

```bash
# Verificar arquitectura del servidor
uname -m

# Importar imagen desde archivo .tar
sudo ctr -n k8s.io images import /ruta/al/fichero.tar

# Listar imágenes en containerd
sudo ctr -n k8s.io images list | grep forconversations

# Borrar imagen de containerd
sudo ctr -n k8s.io images delete <nombre-completo-de-la-imagen>
```

## 7. Troubleshooting

### Debug de pods

```bash
# Ver imagen exacta que usa el pod
kubectl get pod <nombre-del-pod> -n forconversations -o jsonpath='{.spec.containers[0].image}'

# Ver eventos del pod
kubectl describe pod <nombre-del-pod> -n forconversations

# Ver logs detallados
kubectl logs <nombre-del-pod> -n forconversations --previous
```

## 8. Arquitectura del Servidor

### Configuración Recomendada

- **Bases de Datos (MongoDB/Elasticsearch):** Fuera de Kubernetes usando Docker Compose

  - Mayor eficiencia y estabilidad
  - Gestión de persistencia más simple

- **Aplicaciones (Spring Boot):** Dentro de Kubernetes
  - Auto-reparación y escalado automático
  - Organización por namespaces

### Diagrama de Arquitectura

```
+-------------------------------------------------------------+
| Servidor VPS (Ubuntu 24.04)                                |
|                                                             |
| +-----------------+  <-- Conexión -->  +------------------+ |
| | Docker Compose  |      (Red Local)     | Kubernetes       | |
| | - MongoDB       |                      | - forconversations| |
| | - Elasticsearch |                      |   - Spring Boot  | |
| +-----------------+                      +------------------+ |
+-------------------------------------------------------------+
```

---

## Información del Servidor

- **IP:** **************
- **Usuario:** root
- **Namespace K8s:** forconversations
