# mongodb-values.yaml
# ---
# Configuración del Replica Set de MongoDB
replicaSet:
  enabled: true
  replicas: 3 # Número de miembros del replica set (recomendado para HA)

# Configuración de autenticación
auth:
  enabled: true
  rootPassword: "${MONGO_ROOT_PASSWORD}"
  username: "${MONGO_USERNAME}"
  password: "${MONGO_USER_PASSWORD}"
  database: "${MONGO_DATABASE}"

# Configuración de persistencia de datos
persistence:
  enabled: true
  storageClass: "${MONGO_STORAGE_CLASS}" # StorageClass a usar para los Persistent Volumes
  size: "${MONGO_STORAGE_SIZE}"         # Tamaño de cada Persistent Volume

# Recursos de CPU y Memoria para los pods de MongoDB
resources:
  requests:
    memory: "1Gi"
    cpu: "500m"
  limits:
    memory: "2Gi"
    cpu: "1000m"

# Probes de Liveness (comprueba si la aplicación está viva)
livenessProbe:
  enabled: true
  initialDelaySeconds: 30 # Retraso antes de que el liveness probe comience
  periodSeconds: 10       # Frecuencia de la comprobación
  timeoutSeconds: 5       # Tiempo de espera para la respuesta del probe
  failureThreshold: 6     # Número de fallos consecutivos antes de reiniciar el contenedor

# Probes de Readiness (comprueba si la aplicación está lista para servir tráfico)
readinessProbe:
  enabled: true
  initialDelaySeconds: 10 # Retraso antes de que el readiness probe comience
  periodSeconds: 15       # Frecuencia de la comprobación
  timeoutSeconds: 5       # Tiempo de espera para la respuesta del probe
  failureThreshold: 3     # Número de fallos consecutivos antes de marcar el pod como no listo

# Configuración de métricas (para monitoreo, ej. con Prometheus)
metrics:
  enabled: false # Cambia a 'true' si quieres exportar métricas y tienes un sistema de monitoreo
