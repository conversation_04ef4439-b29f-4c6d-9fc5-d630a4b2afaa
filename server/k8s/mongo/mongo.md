# MongoDB - Guía de Conexión y Administración

## 🔗 Conectar a MongoDB por Consola en el Contenedor

### 1. Conectar al contenedor MongoDB

```bash
kubectl exec -it mongodb-cb78b9c49-szzfm -n forconversations -- bash
```

### 2. Conectar a MongoDB con mongosh

```bash
mongosh --host localhost --port 27017 -u wishforthecure -p 'W3sf4rth2c5r2#' --authenticationDatabase admin
```

### 3. Conexión directa (un solo comando)

```bash
kubectl exec -it mongodb-cb78b9c49-szzfm -n forconversations -- mongosh --host localhost --port 27017 -u wishforthecure -p 'W3sf4rth2c5r2#' --authenticationDatabase admin
```

## 📊 Comandos Básicos de MongoDB

### Ver bases de datos disponibles

```javascript
show dbs
```

### Cambiar a la base de datos de la aplicación

```javascript
use forconversations
```

### Ver colecciones en la base de datos

```javascript
show collections
```

## 👥 Gestión de Usuarios

### Ver todos los usuarios

```javascript
db.jhi_user.find();
```

### Ver usuarios con campos específicos

```javascript
db.jhi_user.find({}, { login: 1, email: 1, password: 1, activated: 1 });
```

### Buscar usuario por login

```javascript
db.jhi_user.find({ login: 'admin' });
```

### Buscar usuario por email

```javascript
db.jhi_user.find({ email: '<EMAIL>' });
```

## 🔐 Cambiar Contraseña de Usuario

### Generar Hash BCrypt para una contraseña

La aplicación usa **BCrypt con 10 rounds** para hashear contraseñas. Para generar un hash:

#### Opción 1: Usar herramienta online

- Ve a: https://bcrypt-generator.com/
- Introduce tu contraseña: `W3sf4rth2c5r2#`
- Selecciona rounds: `10`
- Copia el hash generado

#### Opción 2: Usar la aplicación Spring Boot

```bash
# Desde el directorio del proyecto
./mvnw spring-boot:run -Dspring-boot.run.arguments="--generate-password=W3sf4rth2c5r2#"
```

### Actualizar contraseña del usuario

#### Cambiar contraseña del usuario admin

```javascript
db.jhi_user.updateOne(
  { login: 'admin' },
  {
    $set: {
      password: '$2a$10$TU_HASH_BCRYPT_AQUI',
    },
  },
);
```

#### Cambiar login y email del usuario

```javascript
db.jhi_user.updateOne(
  { login: 'admin' },
  {
    $set: {
      login: '<EMAIL>',
      email: '<EMAIL>',
      password: '$2a$10$TU_HASH_BCRYPT_AQUI',
    },
  },
);
```

### Ejemplo completo: Configurar usuario con email y contraseña personalizada

```javascript
// Hash BCrypt para la contraseña "W3sf4rth2c5r2#"
db.jhi_user.updateOne(
  { login: 'admin' },
  {
    $set: {
      login: '<EMAIL>',
      email: '<EMAIL>',
      password: '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjZGgaEBNEkJkMFwqRDQyQC6.xJUyW',
    },
  },
);
```

## 🔍 Verificar Cambios

### Verificar que el usuario se actualizó correctamente

```javascript
db.jhi_user.find({}, { login: 1, email: 1, activated: 1, authorities: 1 });
```

### Ver autoridades/roles disponibles

```javascript
db.jhi_authority.find();
```

## 🚪 Salir de MongoDB

```javascript
exit;
```

## 📝 Notas Importantes

### Credenciales de MongoDB

- **Usuario**: `wishforthecure`
- **Contraseña**: `W3sf4rth2c5r2#`
- **Base de datos de autenticación**: `admin`
- **Base de datos de la aplicación**: `forconversations`

### Estructura del Usuario en la Base de Datos

```javascript
{
  _id: "uuid",
  login: "nombre_usuario",
  password: "$2a$10$hash_bcrypt",
  first_name: "Nombre",
  last_name: "Apellido",
  email: "<EMAIL>",
  activated: true,
  lang_key: "en",
  time_zone: "Europe/Madrid",
  authorities: [
    { _id: "ROLE_USER" },
    { _id: "ROLE_ADMIN" }
  ],
  created_by: "system",
  created_date: ISODate("..."),
  last_modified_by: "system",
  last_modified_date: ISODate("..."),
  _class: "com.wishforthecure.forconversations.domain.User"
}
```

### Algoritmo de Hash de Contraseñas

- **Algoritmo**: BCrypt
- **Rounds**: 10
- **Formato**: `$2a$10$[salt][hash]`
- **Longitud**: 60 caracteres

### Comandos de Verificación Rápida

```bash
# Ver estado del pod MongoDB
kubectl get pods -n forconversations | grep mongodb

# Ver logs de MongoDB
kubectl logs mongodb-cb78b9c49-szzfm -n forconversations

# Verificar conectividad desde la aplicación
kubectl exec -it forconversations-deployment-xxx -n forconversations -- curl -s mongodb:27017
```
