## **Guía Completa de Elasticsearch para Producción (HA y Seguro)**

Esta guía cubre la instalación de un clúster de Elasticsearch y Kibana **para producción** en tu clúster de Kubernetes, utilizando el Helm Chart de Bitnami con configuración de alta disponibilidad y seguridad.

---

### **1. Preparación de Archivos de Configuración**

Necesitaremos el archivo `es.env` para tus credenciales y un `elasticsearch-prod-values.yaml` para la configuración del despliegue en producción.

#### **1.1. Archivo `.env` para Elasticsearch (`es.env`)**

Crea un archivo llamado `es.env`. Esta vez, las contraseñas son aún más críticas.

```bash
# es.env
# ---
# Credenciales de Elasticsearch (¡CAMBIA ESTAS CONTRASEÑAS POR VALORES REALMENTE SEGUROS Y ÚNICOS!)
ELASTICSEARCH_PASSWORD="tu_password_elastic_PROD_segura" # Contraseña para el usuario 'elastic'
ELASTICSEARCH_KIBANA_PASSWORD="tu_password_kibana_PROD_segura" # Contraseña específica para el usuario de Kibana

# Configuración de Almacenamiento (verifica tu StorageClass con 'kubectl get storageclass')
ELASTICSEARCH_STORAGE_CLASS="standard" # O el nombre de tu StorageClass de producción (ej. gp2, azurefile, csi-gce-pd)
ELASTICSEARCH_MASTER_STORAGE_SIZE="50Gi" # Tamaño para los discos de los nodos master
ELASTICSEARCH_DATA_STORAGE_SIZE="100Gi" # Tamaño para los discos de los nodos data (ajusta según tus necesidades de datos)
```

#### **1.2. Archivo de Valores para Helm (`elasticsearch-prod-values.yaml`)**

Crea un archivo llamado `elasticsearch-prod-values.yaml`. Esta configuración prioriza la estabilidad, resiliencia y seguridad.

```yaml
clusterName: 'prod-my-es-cluster' # CAMBIA ESTO a un nombre descriptivo para PRODUCCIÓN

# Configuración de la autenticación de seguridad (¡CRUCIAL para PROD!)
auth:
  enabled: true
  elasticPassword: '${ELASTICSEARCH_PASSWORD}'
  kibanaPassword: '${ELASTICSEARCH_KIBANA_PASSWORD}' # Contraseña para el usuario de Kibana
  # Si necesitas usuarios adicionales, aquí es donde los añadirías
  # users:
  #   - name: appuser
  #     password: "tu_password_appuser_segura"
  #     roles: ["data_reader"]

# Configuración del nodo Master (3 réplicas para QUÓRUM y Alta Disponibilidad en PROD)
master:
  replicas: 3
  persistence:
    enabled: true
    storageClass: '${ELASTICSEARCH_STORAGE_CLASS}'
    size: '${ELASTICSEARCH_MASTER_STORAGE_SIZE}'
  resources:
    requests:
      memory: '2Gi' # Requisitos de CPU y memoria para Master (ajustar según tu VPS/cargas)
      cpu: '1000m' # 1 CPU
    limits:
      memory: '4Gi'
      cpu: '2000m'

# Configuración del nodo Data (mínimo 2 réplicas para Alta Disponibilidad y escalabilidad en PROD)
data:
  replicas: 2
  persistence:
    enabled: true
    storageClass: '${ELASTICSEARCH_STORAGE_CLASS}'
    size: '${ELASTICSEARCH_DATA_STORAGE_SIZE}'
  resources:
    requests:
      memory: '4Gi' # Requisitos de CPU y memoria para Data (ajustar según tu VPS/cargas)
      cpu: '2000m' # 2 CPUs
    limits:
      memory: '8Gi'
      cpu: '4000m'

# Nodos Ingest (habilitar si necesitas procesamiento de datos separado)
ingest:
  replicas: 0 # Deja en 0 si no lo necesitas como nodo separado

# Nodos Client (para acceso a la API de Elasticsearch)
client:
  replicas: 1
  service:
    type: ClusterIP # Para acceso interno. Usa LoadBalancer si necesitas acceso externo directo (¡cuidado con la seguridad!)
  resources:
    requests:
      memory: '1Gi'
      cpu: '500m'
    limits:
      memory: '2Gi'
      cpu: '1000m'

# Kibana (interfaz web de Elasticsearch)
kibana:
  enabled: true
  replicas: 1
  service:
    type: ClusterIP # Para acceso interno. Usa LoadBalancer si necesitas acceso externo (¡cuidado con la seguridad!)
  resources:
    requests:
      memory: '1Gi'
      cpu: '500m'
    limits:
      memory: '2Gi'
      cpu: '1000m'

# Configuración de los Probes (esenciales para Kubernetes para saber si está sano)
livenessProbe:
  enabled: true
  initialDelaySeconds: 120 # Aumentar para que tenga tiempo de arrancar
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 6
readinessProbe:
  enabled: true
  initialDelaySeconds: 60 # Aumentar para que tenga tiempo de prepararse
  periodSeconds: 20
  timeoutSeconds: 10
  failureThreshold: 5

# Configuración de Sysctl (requerido por Elasticsearch en algunos entornos)
# Asegúrate que tu nodo Kubernetes permita 'vm.max_map_count' o que lo configure automáticamente.
# El Chart de Bitnami a veces intenta configurar esto con un Init Container.
sysctlInit:
  enabled: true
  image:
    registry: docker.io
    repository: bitnami/kubectl
    tag: 1.29.3-debian-12-r0 # Asegura que sea compatible con tu versión de K8s y Docker
    pullPolicy: IfNotPresent

# Configuración de JVM (ajusta para tu caso, pero los recursos del pod son los principales)
# No modificar si los limites del pod están bien.
# jvmFlags: ["-Xms2g", "-Xmx2g"]

extraEnvVars: []
```

---

### **2. Proceso de Despliegue de Elasticsearch para Producción**

Sigue estos pasos con el nuevo archivo `elasticsearch-prod-values.yaml`.

#### **2.1. Limpieza Previa Total (¡Crítico!)**

Es absolutamente crucial eliminar cualquier instalación anterior de Elasticsearch. **Esto borrará todos los datos de Elasticsearch.**

```bash
echo "--- Realizando limpieza COMPLETA de Elasticsearch en 'forconversations' ---"
helm uninstall elasticsearch -n forconversations 2>/dev/null || true
# Borrar todos los PVCs de Elasticsearch por etiqueta
kubectl delete pvc -l app.kubernetes.io/instance=elasticsearch -n forconversations 2>/dev/null || true
kubectl delete statefulset -l app.kubernetes.io/instance=elasticsearch -n forconversations 2>/dev/null || true
kubectl delete deployment -l app.kubernetes.io/instance=elasticsearch -n forconversations 2>/dev/null || true
kubectl delete service -l app.kubernetes.io/instance=elasticsearch -n forconversations 2>/dev/null || true
kubectl delete secret -l app.kubernetes.io/instance=elasticsearch -n forconversations 2>/dev/null || true
kubectl delete configmap -l app.kubernetes.io/instance=elasticsearch -n forconversations 2>/dev/null || true
echo "Verificación de limpieza (debería mostrar 'No resources found' para Elasticsearch):"
kubectl get all -n forconversations | grep -i elasticsearch
kubectl get pvc -n forconversations | grep -i elasticsearch
echo "--- Limpieza completa. ---"
```

#### **2.2. Ejecución de Comandos de Instalación**

```bash
echo "--- Preparando e instalando Elasticsearch para PRODUCCIÓN con Helm ---"

# 1. Cargar las variables de entorno para Elasticsearch
source es.env

# 2. Asegurar que el namespace 'forconversations' exista
kubectl create namespace forconversations --dry-run=client -o yaml | kubectl apply -f -

# 3. Añadir y actualizar el repositorio de Helm de Bitnami
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update

# 4. Procesar el archivo de valores YAML con las variables de entorno
envsubst < elasticsearch-prod-values.yaml > /tmp/final-elasticsearch-prod.yaml

# 5. Instalar Elasticsearch con Helm (usando la configuración de PRODUCCIÓN)
helm install elasticsearch bitnami/elasticsearch -n forconversations -f /tmp/final-elasticsearch-prod.yaml

# 6. Verificar el estado del despliegue (ESTO TARDARÁ MÁS, pueden ser 5-10 minutos)
echo "--- Verificando el estado de los pods de Elasticsearch (¡esto tomará tiempo!). ---"
sleep 300 # Espera 5 minutos (300 segundos) para que los pods inicien y formen el clúster
kubectl get pods -n forconversations -l app.kubernetes.io/instance=elasticsearch
kubectl get svc -n forconversations -l app.kubernetes.io/instance=elasticsearch
kubectl get pvc -n forconversations -l app.kubernetes.io/instance=elasticsearch

echo "--- Despliegue de Elasticsearch para producción completado. Revisa el estado de los pods. ---"
```

---

### **3. Conexión y Acceso a Elasticsearch y Kibana**

Una vez que los pods estén `Running` y `READY`.

#### **3.1. Obtener Contraseñas**

```bash
# Contraseña para el usuario 'elastic'
export ELASTICSEARCH_PASSWORD=$(kubectl get secret --namespace forconversations elasticsearch -o jsonpath="{.data.elastic-password}" | base64 -d)
echo "Contraseña de 'elastic': $ELASTICSEARCH_PASSWORD"

# Contraseña para el usuario de Kibana
export ELASTICSEARCH_KIBANA_PASSWORD=$(kubectl get secret --namespace forconversations elasticsearch-kibana -o jsonpath="{.data.password}" | base64 -d)
echo "Contraseña de Kibana: $ELASTICSEARCH_KIBANA_PASSWORD"
```

#### **3.2. Acceder a Kibana desde tu Máquina Local**

Abre una **nueva ventana de terminal** y ejecuta el `port-forward` para Kibana.

```bash
kubectl port-forward --namespace forconversations svc/elasticsearch-kibana 5601:5601 &
```

Luego, en tu navegador web, ve a: **`https://127.0.0.1:5601`**

- **Usuario:** `elastic` (o el usuario de Kibana si lo configuraste diferente)
- **Contraseña:** `$ELASTICSEARCH_KIBANA_PASSWORD` (o la contraseña del usuario de Kibana)

#### **3.3. Conectar a Elasticsearch API desde tu Máquina Local**

En la misma terminal (o en otra), conéctate a la API de Elasticsearch.

```bash
curl -u "elastic:$ELASTICSEARCH_PASSWORD" -k "https://127.0.0.1:9200/_cluster/health?pretty"
```

---

### **4. Configuración de tu Aplicación para Conectar a Elasticsearch**

La conexión de tu aplicación sigue siendo la misma, apuntando al servicio `elasticsearch-client`.

#### **4.1. Modificar `app-deployment-service-ingress.yaml`**

Abre tu archivo **`app-deployment-service-ingress.yaml`** y asegúrate de que la variable de entorno `SPRING_ELASTICSEARCH_URIS` en la sección de tu Deployment apunte a:

```yaml
- name: SPRING_ELASTICSEARCH_URIS
  value: 'http://elasticsearch-client:9200'
```

#### **4.2. Aplicar Cambios a tu Aplicación**

```bash
source app.env
envsubst < app-deployment-service-ingress.yaml > /tmp/final-app.yaml
kubectl apply -f /tmp/final-app.yaml
kubectl get pods -n forconversations -l app=forconversations
```

---
