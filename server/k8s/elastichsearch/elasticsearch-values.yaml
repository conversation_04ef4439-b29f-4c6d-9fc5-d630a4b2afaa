replicaCount: 1

image:
  registry: docker.io
  repository: bitnami/elasticsearch
  tag: 8.13.2
  pullPolicy: IfNotPresent

auth:
  enabled: true
  password: 'changeme'
  username: 'elastic'

service:
  type: ClusterIP
  port: 9200

resources:
  requests:
    memory: '1Gi'
    cpu: '500m'
  limits:
    memory: '2Gi'
    cpu: '1000m'

persistence:
  enabled: true
  storageClass: ''
  accessModes:
    - ReadWriteOnce
  size: 8Gi

volumePermissions:
  enabled: true

securityContext:
  enabled: true
  fsGroup: 1001
  runAsUser: 1001

elasticsearch:
  clusterName: 'elasticsearch'
  nodeGroup: 'master'
  roles: ['master', 'data', 'ingest']

  heapSize: '1024m'
  extraEnvs: []
  extraVolumeMounts: []
  extraVolumes: []

  config:
    elasticsearch.yml: |
      cluster.name: "elasticsearch"
      network.host: 0.0.0.0
      node.name: ${HOSTNAME}
      discovery.type: single-node # <-- Esto es clave: nodo único
      xpack.security.enabled: true

kibana:
  enabled: true
  image:
    registry: docker.io
    repository: bitnami/kibana
    tag: 8.13.2
    pullPolicy: IfNotPresent
  replicaCount: 1
  service:
    type: ClusterIP
    port: 5601
  resources:
    requests:
      memory: '1Gi'
      cpu: '500m'
    limits:
      memory: '2Gi'
      cpu: '1000m'
  elasticsearch:
    hosts:
      - http://elasticsearch:9200
    user: 'elastic'
    password: 'changeme'

extraEnvVars: []

sysctlImage: # <-- Esta es la sección que me pediste añadir
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi