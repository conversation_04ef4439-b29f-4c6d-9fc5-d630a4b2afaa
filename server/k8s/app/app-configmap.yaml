apiVersion: v1
kind: ConfigMap
metadata:
  name: forconversations-config
  namespace: forconversations
data:
  application.yaml: |
    server:
      port: 9101
    management:
      endpoints:
        web:
          exposure:
            include: 'health,info'
          base-path: /actuator
      endpoint:
        health:
          show-details: when-authorized
          probes:
            enabled: true
      health:
        livenessstate:
          enabled: true
        readinessstate:
          enabled: true
    spring:
      profiles:
        active: prod
      data:
        mongodb:
          uri: mongodb://wishforthecure:W3sf4rth2c5r2#@mongodb:27017/forconversations?authSource=admin
      elasticsearch:
        uris: http://elasticsearch:9200
        username: elastic
        password: changeme
        connection-timeout: 30s
        socket-timeout: 60s
    java_opts: -Xmx512m -Xms256m -XX:+UseG1GC -XX:+UseStringDeduplication --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED
