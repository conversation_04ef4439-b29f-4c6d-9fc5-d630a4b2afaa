apiVersion: apps/v1
kind: Deployment
metadata:
  name: forconversations-deployment
  namespace: forconversations
  labels:
    app: forconversations
spec:
  replicas: 1
  selector:
    matchLabels:
      app: forconversations
  template:
    metadata:
      labels:
        app: forconversations
    spec:
      containers:
        - name: forconversations-app
          image: 'wishforthecure/forconversations:20250620104032'
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 9101
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: 'prod'
            - name: SERVER_PORT
              value: '9101'
            - name: SPRING_DATA_MONGODB_URI
              value: 'mongodb://wishforthecure:W3sf4rth2c5r2#@mongodb:27017/forconversations?authSource=admin'
            - name: SPRING_ELASTICSEARCH_URIS
              value: 'http://elasticsearch:9200'
            - name: SPRING_ELASTICSEARCH_USERNAME
              value: 'wishforthecure'
            - name: SPRING_ELASTICSEARCH_PASSWORD
              value: 'W3sf4rth2c5r2#'
            - name: SPRING_ELASTICSEARCH_CONNECTION_TIMEOUT
              value: '30s'
            - name: SPRING_ELASTICSEARCH_SOCKET_TIMEOUT
              value: '60s'
            - name: JAVA_OPTS
              value: '-Xmx512m -Xms256m -XX:+UseG1GC -XX:+UseStringDeduplication --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED'
            - name: MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE
              value: 'health,info'
            - name: MANAGEMENT_ENDPOINTS_WEB_BASE_PATH
              value: '/management'
            - name: MANAGEMENT_ENDPOINT_HEALTH_PROBES_ENABLED
              value: 'true'
            - name: MANAGEMENT_HEALTH_LIVENESSSTATE_ENABLED
              value: 'true'
            - name: MANAGEMENT_HEALTH_READINESSSTATE_ENABLED
              value: 'true'
          volumeMounts:
            - name: config-volume
              mountPath: /config
              readOnly: true
          resources:
            requests:
              memory: '512Mi'
              cpu: '250m'
            limits:
              memory: '1Gi'
              cpu: '500m' # He ajustado la CPU límite a 500m, ya que la request es 250m y tu VPS tiene más RAM
          readinessProbe:
            httpGet:
              path: /management/health/readiness
              port: http
            initialDelaySeconds: 45
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 5
          livenessProbe:
            httpGet:
              path: /management/health/liveness
              port: http
            initialDelaySeconds: 180
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
      volumes:
        - name: config-volume
          configMap:
            name: forconversations-config
---
apiVersion: v1
kind: Service
metadata:
  name: forconversations-svc
  namespace: forconversations
  labels:
    app: forconversations
spec:
  type: ClusterIP
  selector:
    app: forconversations
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 9101
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: forconversations-ingress
  namespace: forconversations
  annotations:
    cert-manager.io/cluster-issuer: 'letsencrypt-production'
    nginx.ingress.kubernetes.io/ssl-redirect: 'true'
spec:
  ingressClassName: 'nginx'
  rules:
    - host: 'forconversations.wishforthecure.com'
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: forconversations-svc
                port:
                  number: 80
  tls:
    - hosts:
        - 'forconversations.wishforthecure.com'
      secretName: forconversations-wishforthecure-com-tls
