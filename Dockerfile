# Multi-stage build for JHipster application
FROM eclipse-temurin:21-jre-alpine AS runtime

# Set working directory
WORKDIR /app

# Create a non-root user
RUN addgroup -g 1001 -S jhipster && \
    adduser -S jhipster -u 1001 -G jhipster

# Copy the JAR file
COPY target/forconversations-*.jar app.jar

# Change ownership of the app directory
RUN chown -R jhipster:jhipster /app

# Switch to non-root user
USER jhipster

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/management/health || exit 1

# Set JVM options for production with Java 21 module system fixes
ENV JAVA_OPTS="-Xmx512m -Xms256m -XX:+UseG1GC -XX:+UseStringDeduplication --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED"

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
